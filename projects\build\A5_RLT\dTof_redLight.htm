<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\build\A5_RLT\dTof_redLight.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\build\A5_RLT\dTof_redLight.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Wed Jul 30 15:15:18 2025
<BR><P>
<H3>Maximum Stack Usage =       3052 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; UART_AllHandle &rArr; XSJ_UART_AllHandle &rArr; XSJ_DealProtocol &rArr; VI4302_AllInit &rArr; flash_SaveFmData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[9]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">PendSV_Handler</a><BR>
 <LI><a href="#[1e]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1e]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1e]">ADC_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[5]">BusFault_Handler</a> from n32g401_it.o(i.BusFault_Handler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[20]">COMP_1_2_3_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[16]">DMA_Channel1_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[17]">DMA_Channel2_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[18]">DMA_Channel3_IRQHandler</a> from usart.o(i.DMA_Channel3_IRQHandler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[19]">DMA_Channel4_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[1a]">DMA_Channel5_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[1b]">DMA_Channel6_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[1c]">DMA_Channel7_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[1d]">DMA_Channel8_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from n32g401_it.o(i.DebugMon_Handler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[11]">EXTI0_IRQHandler</a> from main.o(i.EXTI0_IRQHandler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[12]">EXTI1_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[13]">EXTI2_IRQHandler</a> from main.o(i.EXTI2_IRQHandler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[14]">EXTI3_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[15]">EXTI4_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[f]">FLASH_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[3]">HardFault_Handler</a> from n32g401_it.o(i.HardFault_Handler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[34]">LPTIM_WKUP_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[1f]">MMU_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[4]">MemManage_Handler</a> from n32g401_it.o(i.MemManage_Handler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[2]">NMI_Handler</a> from n32g401_it.o(i.NMI_Handler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[c]">PVD_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[10]">RCC_IRQHandler</a> from n32g401_it.o(i.RCC_IRQHandler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[33]">RTCAlarm_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[d]">RTC_TAMPER_STAMP_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[e]">RTC_WKUP_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[1]">Reset_Handler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from n32g401_it.o(i.SVC_Handler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[a]">SysTick_Handler</a> from n32g401_it.o(i.SysTick_Handler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[3d]">System_Initializes</a> from system_n32g401.o(i.System_Initializes) referenced from startup_n32g401.o(.text)
 <LI><a href="#[22]">TIM1_BRK_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[23]">TIM1_UP_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[3a]">TIM5_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[3b]">TIM6_IRQHandler</a> from main.o(i.TIM6_IRQHandler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[36]">TIM8_UP_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[31]">UART3_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[39]">UART4_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from usart.o(i.USART2_IRQHandler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[6]">UsageFault_Handler</a> from n32g401_it.o(i.UsageFault_Handler) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[b]">WWDG_IRQHandler</a> from startup_n32g401.o(.text) referenced from startup_n32g401.o(RESET)
 <LI><a href="#[3e]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_n32g401.o(.text)
 <LI><a href="#[3c]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[44]">spi_write_data</a> from VI4302_Handle.o(i.spi_write_data) referenced from VI4302_Handle.o(i.user_driver_init)
 <LI><a href="#[3f]">vi4302_HW_set_demo</a> from VI4302_Handle.o(i.vi4302_HW_set_demo) referenced from VI4302_Handle.o(i.user_driver_init)
 <LI><a href="#[42]">vi4302_read_mul_reg</a> from VI4302_Handle.o(i.vi4302_read_mul_reg) referenced from VI4302_Handle.o(i.user_driver_init)
 <LI><a href="#[40]">vi4302_read_register</a> from VI4302_Handle.o(i.vi4302_read_register) referenced from VI4302_Handle.o(i.user_driver_init)
 <LI><a href="#[43]">vi4302_write_cmd</a> from VI4302_Handle.o(i.vi4302_write_cmd) referenced from VI4302_Handle.o(i.user_driver_init)
 <LI><a href="#[41]">vi4302_write_register</a> from VI4302_Handle.o(i.vi4302_write_register) referenced from VI4302_Handle.o(i.user_driver_init)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[3e]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(.text)
</UL>
<P><STRONG><a name="[14c]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[45]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[58]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[14d]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[14e]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[14f]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[150]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[151]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[1]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>COMP_1_2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_Channel8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>LPTIM_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>MMU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_n32g401.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[12e]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Update_firmware
</UL>

<P><STRONG><a name="[152]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[153]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[48]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[154]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[155]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[47]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Update_firmware
</UL>

<P><STRONG><a name="[94]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCO_OutputInit
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_UART_AllHandle
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CmdAckSend
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AllHandle
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_SinglePixel_Output
</UL>

<P><STRONG><a name="[156]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[49]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[4a]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vis_tof_compensation
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>

<P><STRONG><a name="[4f]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[50]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>

<P><STRONG><a name="[51]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vis_tof_compensation
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[52]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adaptation_REG_and_ALGO
</UL>

<P><STRONG><a name="[53]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vis_tof_compensation
</UL>

<P><STRONG><a name="[55]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adaptation_REG_and_ALGO
</UL>

<P><STRONG><a name="[4b]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[157]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[54]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[158]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[4c]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[159]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[15a]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[4e]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>

<P><STRONG><a name="[4d]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>

<P><STRONG><a name="[56]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[57]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[13c]"></a>__ARM_scalbn</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[15b]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[132]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>

<P><STRONG><a name="[46]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[15c]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[59]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[15d]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl.o(.text), UNUSED)

<P><STRONG><a name="[15e]"></a>__decompress0</STRONG> (Thumb, 58 bytes, Stack size unknown bytes, __dczerorl.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>ADC_AHB_Clock_Mode_Config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_AHB_Clock_Mode_Config))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Clock_Mode_Config
</UL>

<P><STRONG><a name="[5a]"></a>ADC_AllInit</STRONG> (Thumb, 254 bytes, Stack size 88 bytes, adc.o(i.ADC_AllInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = ADC_AllInit &rArr; GPIO_Peripheral_Initialize &rArr; GPIO_Mode_Set
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ADC_1M_Clock_Config
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Reset
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Request_Remap
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Enable
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Temperature_Sensor_And_Vrefint_Channel_Enable
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Regular_Sequence_Conversion_Number_Config
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Regular_Channels_Software_Conversion_Operation
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ON
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes_Structure
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Flag_Status_Get
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Transfer_Enable
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Clock_Mode_Config
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Channel_Sample_Time_Config
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Calibration_Operation
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB_Peripheral_Clock_Enable
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Structure_Initialize
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6c]"></a>ADC_Calibration_Operation</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Calibration_Operation))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[66]"></a>ADC_Channel_Sample_Time_Config</STRONG> (Thumb, 134 bytes, Stack size 12 bytes, n32g401_adc.o(i.ADC_Channel_Sample_Time_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ADC_Channel_Sample_Time_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[5c]"></a>ADC_Clock_Mode_Config</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, n32g401_adc.o(i.ADC_Clock_Mode_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_Clock_Mode_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ADC_PLL_Clock_Prescaler_Enable
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ADC_PLL_Clock_Disable
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ADC_Hclk_Enable
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ADC_Hclk_Config
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_PLL_Clock_Mode_Config
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AHB_Clock_Mode_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[77]"></a>ADC_Continue_Conversion_Disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Continue_Conversion_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes
</UL>

<P><STRONG><a name="[76]"></a>ADC_Continue_Conversion_Enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Continue_Conversion_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes
</UL>

<P><STRONG><a name="[69]"></a>ADC_DMA_Transfer_Enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_DMA_Transfer_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[79]"></a>ADC_Data_Alignment_Config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Data_Alignment_Config))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes
</UL>

<P><STRONG><a name="[6b]"></a>ADC_Flag_Status_Get</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Flag_Status_Get))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[65]"></a>ADC_Initializes</STRONG> (Thumb, 64 bytes, Stack size 4 bytes, n32g401_adc.o(i.ADC_Initializes))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ADC_Initializes
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Regular_Group_External_Trigger_Source_Config
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Regular_Channels_Number_Config
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Multichannels_Enable
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Multichannels_Disable
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Data_Alignment_Config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Continue_Conversion_Enable
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Continue_Conversion_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[64]"></a>ADC_Initializes_Structure</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Initializes_Structure))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[75]"></a>ADC_Multichannels_Disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Multichannels_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes
</UL>

<P><STRONG><a name="[74]"></a>ADC_Multichannels_Enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Multichannels_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes
</UL>

<P><STRONG><a name="[6a]"></a>ADC_ON</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_ON))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[73]"></a>ADC_PLL_Clock_Mode_Config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_PLL_Clock_Mode_Config))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Clock_Mode_Config
</UL>

<P><STRONG><a name="[7a]"></a>ADC_Regular_Channels_Number_Config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Regular_Channels_Number_Config))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes
</UL>

<P><STRONG><a name="[6d]"></a>ADC_Regular_Channels_Software_Conversion_Operation</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Regular_Channels_Software_Conversion_Operation))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[78]"></a>ADC_Regular_Group_External_Trigger_Source_Config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Regular_Group_External_Trigger_Source_Config))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Initializes
</UL>

<P><STRONG><a name="[67]"></a>ADC_Regular_Sequence_Conversion_Number_Config</STRONG> (Thumb, 148 bytes, Stack size 8 bytes, n32g401_adc.o(i.ADC_Regular_Sequence_Conversion_Number_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_Regular_Sequence_Conversion_Number_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[68]"></a>ADC_Temperature_Sensor_And_Vrefint_Channel_Enable</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, n32g401_adc.o(i.ADC_Temperature_Sensor_And_Vrefint_Channel_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[14b]"></a>ALL_ParaInit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, cmd_handle.o(i.ALL_ParaInit))
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7b]"></a>Adaptation_REG_and_ALGO</STRONG> (Thumb, 304 bytes, Stack size 32 bytes, VI4302_System.o(i.Adaptation_REG_and_ALGO))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = Adaptation_REG_and_ALGO &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[5]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[f5]"></a>CMD_SumNegateCheck</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, cmd_handle.o(i.CMD_SumNegateCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CMD_SumNegateCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CmdAckSend
</UL>

<P><STRONG><a name="[82]"></a>DMA_Buffer_Size_Config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Buffer_Size_Config))
<BR><BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel3_IRQHandler
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[18]"></a>DMA_Channel3_IRQHandler</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, usart.o(i.DMA_Channel3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DMA_Channel3_IRQHandler &rArr; uart1outfifo_DataOut
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1outfifo_HaveData
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1outfifo_DataOut
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Interrupt_Status_Get
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Interrupt_Status_Clear
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Disable
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Buffer_Size_Config
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Enable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>DMA_Channel_Disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Channel_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_DmaConfig
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel3_IRQHandler
</UL>

<P><STRONG><a name="[63]"></a>DMA_Channel_Enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Channel_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_DmaConfig
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel3_IRQHandler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[62]"></a>DMA_Channel_Request_Remap</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Channel_Request_Remap))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_DmaConfig
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[8a]"></a>DMA_Circular_Mode_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Circular_Mode_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[102]"></a>DMA_Current_Data_Transfer_Number_Get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Current_Data_Transfer_Number_Get))
<BR><BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[85]"></a>DMA_Destination_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Destination_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[61]"></a>DMA_Initializes</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, n32g401_dma.o(i.DMA_Initializes))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_Initializes
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Buffer_Size_Config
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Priority_Config
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Peripheral_Data_Width_Config
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Peripheral_Address_Config
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Peripheral_Addr_Increment_Config
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Memory_Data_Width_Config
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Memory_Address_Config
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Memory_Addr_Increment_Config
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Memory_2_Memory_Config
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Destination_Config
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Circular_Mode_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_DmaConfig
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[7e]"></a>DMA_Interrupt_Status_Clear</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Interrupt_Status_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel3_IRQHandler
</UL>

<P><STRONG><a name="[7d]"></a>DMA_Interrupt_Status_Get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Interrupt_Status_Get))
<BR><BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel3_IRQHandler
</UL>

<P><STRONG><a name="[f7]"></a>DMA_Interrupts_Enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Interrupts_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_DmaConfig
</UL>

<P><STRONG><a name="[8c]"></a>DMA_Memory_2_Memory_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Memory_2_Memory_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[87]"></a>DMA_Memory_Addr_Increment_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Memory_Addr_Increment_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[84]"></a>DMA_Memory_Address_Config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Memory_Address_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[89]"></a>DMA_Memory_Data_Width_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Memory_Data_Width_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[86]"></a>DMA_Peripheral_Addr_Increment_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Peripheral_Addr_Increment_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[83]"></a>DMA_Peripheral_Address_Config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Peripheral_Address_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[88]"></a>DMA_Peripheral_Data_Width_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Peripheral_Data_Width_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[8b]"></a>DMA_Priority_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Priority_Config))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
</UL>

<P><STRONG><a name="[60]"></a>DMA_Reset</STRONG> (Thumb, 172 bytes, Stack size 0 bytes, n32g401_dma.o(i.DMA_Reset))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_DmaConfig
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, n32g401_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, main.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Interrupt_Status_Get
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Interrupt_Status_Clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, main.o(i.EXTI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI2_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Interrupt_Status_Get
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Interrupt_Status_Clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>EXTI_Interrupt_Status_Clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, n32g401_exti.o(i.EXTI_Interrupt_Status_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI2_IRQHandler
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler
</UL>

<P><STRONG><a name="[8d]"></a>EXTI_Interrupt_Status_Get</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, n32g401_exti.o(i.EXTI_Interrupt_Status_Get))
<BR><BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI2_IRQHandler
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler
</UL>

<P><STRONG><a name="[92]"></a>EXTI_LineCmd_Disable</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, n32g401_exti.o(i.EXTI_LineCmd_Disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI_LineCmd_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Peripheral_Initializes
</UL>

<P><STRONG><a name="[8f]"></a>EXTI_Peripheral_Initializes</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, n32g401_exti.o(i.EXTI_Peripheral_Initializes))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = EXTI_Peripheral_Initializes &rArr; EXTI_Work_Mode_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Work_Mode_Config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Trigger_Config
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_LineCmd_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[91]"></a>EXTI_Trigger_Config</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, n32g401_exti.o(i.EXTI_Trigger_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI_Trigger_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Peripheral_Initializes
</UL>

<P><STRONG><a name="[90]"></a>EXTI_Work_Mode_Config</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, n32g401_exti.o(i.EXTI_Work_Mode_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI_Work_Mode_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Peripheral_Initializes
</UL>

<P><STRONG><a name="[93]"></a>Execute_instruction</STRONG> (Thumb, 534 bytes, Stack size 192 bytes, cmd_handle.o(i.Execute_instruction))
<BR><BR>[Stack]<UL><LI>Max Depth = 528<LI>Call Chain = Execute_instruction &rArr; cal_final_tof &rArr; vis_tof_compensation &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Week
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NTC_TempGet
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_InfifoDataOut
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_InfifoDataIn
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CmdAckSend
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cal_final_tof
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Noise_To_Confidence
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Temp_Bvd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Read_Histogram
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_ranging_data_with_firmware
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a3]"></a>FLASH_Flag_Status_Clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, n32g401_flash.o(i.FLASH_Flag_Status_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Word_Program
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_One_Page_Erase
</UL>

<P><STRONG><a name="[a0]"></a>FLASH_Last_Operation_Wait</STRONG> (Thumb, 38 bytes, Stack size 4 bytes, n32g401_flash.o(i.FLASH_Last_Operation_Wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Status_Get
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Word_Program
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_One_Page_Erase
</UL>

<P><STRONG><a name="[d9]"></a>FLASH_Lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_flash.o(i.FLASH_Lock))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[a2]"></a>FLASH_One_Page_Erase</STRONG> (Thumb, 76 bytes, Stack size 12 bytes, n32g401_flash.o(i.FLASH_One_Page_Erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_One_Page_Erase &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Last_Operation_Wait
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Flag_Status_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[a1]"></a>FLASH_Status_Get</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, n32g401_flash.o(i.FLASH_Status_Get))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Last_Operation_Wait
</UL>

<P><STRONG><a name="[d7]"></a>FLASH_Unlock</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, n32g401_flash.o(i.FLASH_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[a4]"></a>FLASH_Word_Program</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, n32g401_flash.o(i.FLASH_Word_Program))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Last_Operation_Wait
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Flag_Status_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[aa]"></a>GPIO_Alternate_Set</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, n32g401_gpio.o(i.GPIO_Alternate_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = GPIO_Alternate_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
</UL>

<P><STRONG><a name="[ae]"></a>GPIO_Driver_Set</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, n32g401_gpio.o(i.GPIO_Driver_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = GPIO_Driver_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
</UL>

<P><STRONG><a name="[a8]"></a>GPIO_EXTI_Line_Set</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, n32g401_gpio.o(i.GPIO_EXTI_Line_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIO_EXTI_Line_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[a5]"></a>GPIO_Init</STRONG> (Thumb, 174 bytes, Stack size 40 bytes, GPIO.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = GPIO_Init &rArr; NVIC_Configuration &rArr; NVIC_Initializes
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2_Peripheral_Clock_Enable
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB_Peripheral_Clock_Enable
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Structure_Initialize
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pins_Reset
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_EXTI_Line_Set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Peripheral_Initializes
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ab]"></a>GPIO_Mode_Set</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, n32g401_gpio.o(i.GPIO_Mode_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_Mode_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
</UL>

<P><STRONG><a name="[5f]"></a>GPIO_Peripheral_Initialize</STRONG> (Thumb, 86 bytes, Stack size 20 bytes, n32g401_gpio.o(i.GPIO_Peripheral_Initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = GPIO_Peripheral_Initialize &rArr; GPIO_Mode_Set
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SlewRate_Set
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pull_Set
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Mode_Set
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Driver_Set
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Alternate_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCO_OutputInit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[a7]"></a>GPIO_Pins_Reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_gpio.o(i.GPIO_Pins_Reset))
<BR><BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsLow
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Disen
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TX_Disen
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[bc]"></a>GPIO_Pins_Set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_gpio.o(i.GPIO_Pins_Set))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsHigh
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_En
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TX_En
</UL>

<P><STRONG><a name="[ac]"></a>GPIO_Pull_Set</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, n32g401_gpio.o(i.GPIO_Pull_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = GPIO_Pull_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
</UL>

<P><STRONG><a name="[ad]"></a>GPIO_SlewRate_Set</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, n32g401_gpio.o(i.GPIO_SlewRate_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIO_SlewRate_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
</UL>

<P><STRONG><a name="[5e]"></a>GPIO_Structure_Initialize</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, n32g401_gpio.o(i.GPIO_Structure_Initialize))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCO_OutputInit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[113]"></a>Get_Fw_bytes</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fw_44_00_00_80_R00.o(i.Get_Fw_bytes))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[b5]"></a>Get_NVR_Value</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, system_n32g401.o(i.Get_NVR_Value))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Get_NVR_Value
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PLL_TrimValueLoad
</UL>

<P><STRONG><a name="[13e]"></a>Get_Temperature_Rectified_Tof</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, data_handle.o(i.Get_Temperature_Rectified_Tof))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Get_Temperature_Rectified_Tof
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cal_final_tof
</UL>

<P><STRONG><a name="[3]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>NMI_Handler</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, n32g401_it.o(i.NMI_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NMI_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSE_Config
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Interrupt_Status_Get
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Interrupt_Status_Clear
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Interrupt_Enable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[97]"></a>NTC_TempGet</STRONG> (Thumb, 182 bytes, Stack size 20 bytes, adc.o(i.NTC_TempGet))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = NTC_TempGet
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[a9]"></a>NVIC_Configuration</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, GPIO.o(i.NVIC_Configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = NVIC_Configuration &rArr; NVIC_Initializes
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Initializes
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_DmaConfig
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_Configuration
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[b3]"></a>NVIC_Initializes</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, misc.o(i.NVIC_Initializes))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Initializes
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>

<P><STRONG><a name="[149]"></a>NVIC_Priority_Group_Set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, misc.o(i.NVIC_Priority_Group_Set))
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9b]"></a>Noise_To_Confidence</STRONG> (Thumb, 138 bytes, Stack size 20 bytes, data_handle.o(i.Noise_To_Confidence))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Noise_To_Confidence
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[b4]"></a>PLL_TrimValueLoad</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, system_n32g401.o(i.PLL_TrimValueLoad))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = PLL_TrimValueLoad &rArr; Get_NVR_Value
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_NVR_Value
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Clock_Set
</UL>

<P><STRONG><a name="[5d]"></a>RCC_ADC_1M_Clock_Config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_ADC_1M_Clock_Config))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
</UL>

<P><STRONG><a name="[70]"></a>RCC_ADC_Hclk_Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_ADC_Hclk_Config))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Clock_Mode_Config
</UL>

<P><STRONG><a name="[6e]"></a>RCC_ADC_Hclk_Enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_ADC_Hclk_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Clock_Mode_Config
</UL>

<P><STRONG><a name="[6f]"></a>RCC_ADC_PLL_Clock_Disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_ADC_PLL_Clock_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Clock_Mode_Config
</UL>

<P><STRONG><a name="[72]"></a>RCC_ADC_PLL_Clock_Prescaler_Enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_ADC_PLL_Clock_Prescaler_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Clock_Mode_Config
</UL>

<P><STRONG><a name="[5b]"></a>RCC_AHB_Peripheral_Clock_Enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCO_OutputInit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[dc]"></a>RCC_APB1_Peripheral_Clock_Enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_APB1_Peripheral_Clock_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_Configuration
</UL>

<P><STRONG><a name="[a6]"></a>RCC_APB2_Peripheral_Clock_Enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_APB2_Peripheral_Clock_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCO_OutputInit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[ca]"></a>RCC_APB2_Peripheral_Reset</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_APB2_Peripheral_Reset))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_Reset
</UL>

<P><STRONG><a name="[105]"></a>RCC_Clocks_Frequencies_Value_Get</STRONG> (Thumb, 250 bytes, Stack size 20 bytes, n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Baud_Rate_Config
</UL>

<P><STRONG><a name="[b6]"></a>RCC_Flag_Status_Get</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_Flag_Status_Get))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_IRQHandler
</UL>

<P><STRONG><a name="[b0]"></a>RCC_HSE_Config</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_HSE_Config))
<BR><BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>

<P><STRONG><a name="[10]"></a>RCC_IRQHandler</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, n32g401_it.o(i.RCC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Sysclk_Config
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLL_Enable
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Interrupt_Status_Get
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Interrupt_Status_Clear
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Flag_Status_Get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[b1]"></a>RCC_Interrupt_Enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_Interrupt_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>

<P><STRONG><a name="[b2]"></a>RCC_Interrupt_Status_Clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_Interrupt_Status_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_IRQHandler
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>

<P><STRONG><a name="[af]"></a>RCC_Interrupt_Status_Get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_Interrupt_Status_Get))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_IRQHandler
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>

<P><STRONG><a name="[ba]"></a>RCC_MCO_Source_Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_MCO_Source_Config))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCO_OutputInit
</UL>

<P><STRONG><a name="[b7]"></a>RCC_PLL_Enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_PLL_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_IRQHandler
</UL>

<P><STRONG><a name="[b8]"></a>RCC_Sysclk_Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, n32g401_rcc.o(i.RCC_Sysclk_Config))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_IRQHandler
</UL>

<P><STRONG><a name="[b9]"></a>RCO_OutputInit</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, GPIO.o(i.RCO_OutputInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = RCO_OutputInit &rArr; GPIO_Peripheral_Initialize &rArr; GPIO_Mode_Set
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_MCO_Source_Config
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2_Peripheral_Clock_Enable
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB_Peripheral_Clock_Enable
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Structure_Initialize
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bb]"></a>SPI1_Init</STRONG> (Thumb, 190 bytes, Stack size 48 bytes, spi.o(i.SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = SPI1_Init &rArr; GPIO_Peripheral_Initialize &rArr; GPIO_Mode_Set
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Set_Nss_Level
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ON
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes_Structure
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_Reset
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CRC_Disable
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2_Peripheral_Clock_Enable
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB_Peripheral_Clock_Enable
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Structure_Initialize
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pins_Set
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[c3]"></a>SPI1_ReadNByte</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, spi.o(i.SPI1_ReadNByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SPI1_ReadNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_ranging_data_with_firmware
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_mul_reg
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_his_reg
</UL>

<P><STRONG><a name="[c4]"></a>SPI1_ReadWriteByte</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, spi.o(i.SPI1_ReadWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_Flag_Status_Get
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_Data_Transmit
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_Data_Get
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Week
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_WriteNByte
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteNByte
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadNByte
</UL>

<P><STRONG><a name="[c8]"></a>SPI1_ReadWriteNByte</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, spi.o(i.SPI1_ReadWriteNByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SPI1_ReadWriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_write_register
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_register
</UL>

<P><STRONG><a name="[c9]"></a>SPI1_WriteNByte</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, spi.o(i.SPI1_WriteNByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SPI1_WriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_write_cmd
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_ranging_data_with_firmware
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_mul_reg
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_his_reg
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_data
</UL>

<P><STRONG><a name="[d1]"></a>SPI_BaudRatePres_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_BaudRatePres_Config))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[ce]"></a>SPI_CLKPHA_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_CLKPHA_Config))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[cf]"></a>SPI_CLKPOL_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_CLKPOL_Config))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[c1]"></a>SPI_CRC_Disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_CRC_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[d3]"></a>SPI_CRC_Polynomial_Set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_CRC_Polynomial_Set))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[cb]"></a>SPI_DataDirection_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_DataDirection_Config))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[cd]"></a>SPI_DataLen_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_DataLen_Config))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[d2]"></a>SPI_FirstBit_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_FirstBit_Config))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[c7]"></a>SPI_I2S_Data_Get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_I2S_Data_Get))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[c6]"></a>SPI_I2S_Data_Transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_I2S_Data_Transmit))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[c5]"></a>SPI_I2S_Flag_Status_Get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_I2S_Flag_Status_Get))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[bd]"></a>SPI_I2S_Reset</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, n32g401_spi.o(i.SPI_I2S_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_I2S_Reset
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2_Peripheral_Reset
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[bf]"></a>SPI_Initializes</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, n32g401_spi.o(i.SPI_Initializes))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Initializes
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_SpiMode_Config
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_NSS_Config
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_FirstBit_Config
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DataLen_Config
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DataDirection_Config
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CRC_Polynomial_Set
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CLKPOL_Config
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CLKPHA_Config
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_BaudRatePres_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[be]"></a>SPI_Initializes_Structure</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_Initializes_Structure))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[d0]"></a>SPI_NSS_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_NSS_Config))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[c2]"></a>SPI_ON</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_ON))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[c0]"></a>SPI_Set_Nss_Level</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_Set_Nss_Level))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[cc]"></a>SPI_SpiMode_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_spi.o(i.SPI_SpiMode_Config))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Initializes
</UL>

<P><STRONG><a name="[d4]"></a>STMFLASH_Read</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, flash.o(i.STMFLASH_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_ReadByte
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_ReadSnData
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_ReadFmData
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_ReadConfigerData
</UL>

<P><STRONG><a name="[d5]"></a>STMFLASH_ReadByte</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, flash.o(i.STMFLASH_ReadByte))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>

<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, n32g401_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[d8]"></a>STMFLASH_ReadWord</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, flash.o(i.STMFLASH_ReadWord))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>

<P><STRONG><a name="[d6]"></a>STMFLASH_Write</STRONG> (Thumb, 218 bytes, Stack size 2088 bytes, flash.o(i.STMFLASH_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 2108<LI>Call Chain = STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Word_Program
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_One_Page_Erase
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_ReadWord
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Week
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveSnData
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveFmData
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveConfigerData
</UL>

<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, n32g401_it.o(i.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>System_Initializes</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, system_n32g401.o(i.System_Initializes))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = System_Initializes &rArr; System_Clock_Set &rArr; PLL_TrimValueLoad &rArr; Get_NVR_Value
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Clock_Set
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(.text)
</UL>
<P><STRONG><a name="[db]"></a>TIM6_Configuration</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, timer.o(i.TIM6_Configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = TIM6_Configuration &rArr; NVIC_Configuration &rArr; NVIC_Initializes
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_On
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Interrupt_Enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Struct_Initialize
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Reload_Mode_Set
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1_Peripheral_Clock_Enable
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3b]"></a>TIM6_IRQHandler</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, main.o(i.TIM6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM6_IRQHandler &rArr; TIM_Interrupt_Status_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Interrupt_Status_Get
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Interrupt_Status_Clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[e6]"></a>TIM_Base_Auto_Reload_Set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Auto_Reload_Set))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[e9]"></a>TIM_Base_Channel1</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Channel1))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[eb]"></a>TIM_Base_Channel2</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Channel2))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[ec]"></a>TIM_Base_Channel3</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Channel3))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[ed]"></a>TIM_Base_Channel4</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Channel4))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[e4]"></a>TIM_Base_Count_Mode_Set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Count_Mode_Set))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[de]"></a>TIM_Base_Initialize</STRONG> (Thumb, 156 bytes, Stack size 12 bytes, n32g401_tim.o(i.TIM_Base_Initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_Base_Initialize
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Reload_Mode_Set
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Clock_Division_Set
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Repeat_Count_Set
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Prescaler_Set
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_OCrefClear
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Count_Mode_Set
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Channel4
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Channel3
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Channel2
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Channel1
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Auto_Reload_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_Configuration
</UL>

<P><STRONG><a name="[ea]"></a>TIM_Base_OCrefClear</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_OCrefClear))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[e7]"></a>TIM_Base_Prescaler_Set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Prescaler_Set))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[df]"></a>TIM_Base_Reload_Mode_Set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Reload_Mode_Set))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_Configuration
</UL>

<P><STRONG><a name="[e8]"></a>TIM_Base_Repeat_Count_Set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Repeat_Count_Set))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[dd]"></a>TIM_Base_Struct_Initialize</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Base_Struct_Initialize))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_Configuration
</UL>

<P><STRONG><a name="[e5]"></a>TIM_Clock_Division_Set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Clock_Division_Set))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_Initialize
</UL>

<P><STRONG><a name="[e0]"></a>TIM_Interrupt_Enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Interrupt_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_Configuration
</UL>

<P><STRONG><a name="[e3]"></a>TIM_Interrupt_Status_Clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_Interrupt_Status_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_IRQHandler
</UL>

<P><STRONG><a name="[e2]"></a>TIM_Interrupt_Status_Get</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, n32g401_tim.o(i.TIM_Interrupt_Status_Get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_Interrupt_Status_Get
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_IRQHandler
</UL>

<P><STRONG><a name="[e1]"></a>TIM_On</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, n32g401_tim.o(i.TIM_On))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_Configuration
</UL>

<P><STRONG><a name="[ee]"></a>TX_Disen</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, GPIO.o(i.TX_Disen))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TX_Disen
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pins_Reset
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
</UL>

<P><STRONG><a name="[ef]"></a>TX_En</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, GPIO.o(i.TX_En))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TX_En
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pins_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[95]"></a>UART2_SendData</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, usart.o(i.UART2_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UART2_SendData &rArr; uart1outfifo_DataOut
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1outfifo_HaveData
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1outfifo_DataOut
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1outfifo_DataIn
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Interrupt_Status_Clear
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Disable
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Buffer_Size_Config
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CmdAckSend
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[f1]"></a>UART_AllHandle</STRONG> (Thumb, 76 bytes, Stack size 264 bytes, cmd_handle.o(i.UART_AllHandle))
<BR><BR>[Stack]<UL><LI>Max Depth = 3052<LI>Call Chain = UART_AllHandle &rArr; XSJ_UART_AllHandle &rArr; XSJ_DealProtocol &rArr; VI4302_AllInit &rArr; flash_SaveFmData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Week
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1infifo_HaveData
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1infifo_DataOut
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_UART_AllHandle
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9f]"></a>UART_CmdAckSend</STRONG> (Thumb, 126 bytes, Stack size 88 bytes, cmd_handle.o(i.UART_CmdAckSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = UART_CmdAckSend &rArr; UART2_SendData &rArr; uart1outfifo_DataOut
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CMD_SumNegateCheck
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[f6]"></a>USART2_DmaConfig</STRONG> (Thumb, 176 bytes, Stack size 48 bytes, usart.o(i.USART2_DmaConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = USART2_DmaConfig &rArr; NVIC_Configuration &rArr; NVIC_Initializes
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Interrupts_Enable
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Disable
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Reset
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Initializes
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Request_Remap
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Enable
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[f8]"></a>USART2_Init</STRONG> (Thumb, 166 bytes, Stack size 48 bytes, usart.o(i.USART2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = USART2_Init &rArr; USART_Initializes &rArr; USART_Baud_Rate_Config &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1outfifo_Clear
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1infifo_Clear
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Interrput_Enable
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Initializes
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Enable
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMA_Transfer_Enable
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_DmaConfig
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1_Peripheral_Clock_Enable
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2_Peripheral_Clock_Enable
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB_Peripheral_Clock_Enable
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Structure_Initialize
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Peripheral_Initialize
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Configuration
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fc]"></a>USART_DMA_Transfer_Enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, n32g401_usart.o(i.USART_DMA_Transfer_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[101]"></a>USART_Data_Receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, n32g401_usart.o(i.USART_Data_Receive))
<BR><BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[6]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, n32g401_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[14a]"></a>WDT_Init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, wdt.o(i.WDT_Init))
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 212 bytes, Stack size 16 bytes, usart.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = USART2_IRQHandler &rArr; uart1infifo_DataIn
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1infifo_DataIn
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Interrupt_Status_Get
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Interrupt_Status_Clear
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Data_Receive
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Current_Data_Transfer_Number_Get
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Disable
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Buffer_Size_Config
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel_Enable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_n32g401.o(RESET)
</UL>
<P><STRONG><a name="[104]"></a>USART_Baud_Rate_Config</STRONG> (Thumb, 114 bytes, Stack size 56 bytes, n32g401_usart.o(i.USART_Baud_Rate_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = USART_Baud_Rate_Config &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Initializes
</UL>

<P><STRONG><a name="[fe]"></a>USART_Enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, n32g401_usart.o(i.USART_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[10a]"></a>USART_Hardware_Flow_Control_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_usart.o(i.USART_Hardware_Flow_Control_Config))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Initializes
</UL>

<P><STRONG><a name="[fb]"></a>USART_Initializes</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, n32g401_usart.o(i.USART_Initializes))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = USART_Initializes &rArr; USART_Baud_Rate_Config &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Word_Length_Config
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Stop_Bits_Config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Parity_Config
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Mode_Config
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Hardware_Flow_Control_Config
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Baud_Rate_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[fd]"></a>USART_Interrput_Enable</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, n32g401_usart.o(i.USART_Interrput_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_Interrput_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[100]"></a>USART_Interrupt_Status_Clear</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, n32g401_usart.o(i.USART_Interrupt_Status_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_Interrupt_Status_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[ff]"></a>USART_Interrupt_Status_Get</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, n32g401_usart.o(i.USART_Interrupt_Status_Get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = USART_Interrupt_Status_Get
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[109]"></a>USART_Mode_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_usart.o(i.USART_Mode_Config))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Initializes
</UL>

<P><STRONG><a name="[108]"></a>USART_Parity_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_usart.o(i.USART_Parity_Config))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Initializes
</UL>

<P><STRONG><a name="[107]"></a>USART_Stop_Bits_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_usart.o(i.USART_Stop_Bits_Config))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Initializes
</UL>

<P><STRONG><a name="[106]"></a>USART_Word_Length_Config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, n32g401_usart.o(i.USART_Word_Length_Config))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Initializes
</UL>

<P><STRONG><a name="[10b]"></a>VI4302_AllInit</STRONG> (Thumb, 268 bytes, Stack size 24 bytes, VI4302_Handle.o(i.VI4302_AllInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 2140<LI>Call Chain = VI4302_AllInit &rArr; flash_SaveFmData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveFmData
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NTC_TempGet
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TX_En
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Write_Reg
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Update_firmware
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Update_Succcess_Fail
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Update_Config
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_TDC_Cal
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Reg_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Pin_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_InfifoClear
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Frame_Rate_AutoCtrl
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Enable_DcDc
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Bvd_Cal
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_BVD_Calculate
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adaptation_REG_and_ALGO
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_driver_init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Fw_bytes
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[112]"></a>VI4302_BVD_Calculate</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, VI4302_System.o(i.VI4302_BVD_Calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = VI4302_BVD_Calculate &rArr; VI4302_Set_Bvd
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Set_Bvd
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[118]"></a>VI4302_Bvd_Cal</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Bvd_Cal))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = VI4302_Bvd_Cal &rArr; vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_status_from_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[11e]"></a>VI4302_CsHigh</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, spi.o(i.VI4302_CsHigh))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = VI4302_CsHigh
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pins_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_write_register
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_write_cmd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_register
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_ranging_data_with_firmware
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_mul_reg
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_his_reg
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_data
</UL>

<P><STRONG><a name="[11f]"></a>VI4302_CsLow</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, spi.o(i.VI4302_CsLow))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = VI4302_CsLow
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pins_Reset
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_write_register
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_write_cmd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_register
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_ranging_data_with_firmware
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_mul_reg
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_his_reg
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_data
</UL>

<P><STRONG><a name="[120]"></a>VI4302_Disen</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, GPIO.o(i.VI4302_Disen))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = VI4302_Disen
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pins_Reset
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_HW_set_demo
</UL>

<P><STRONG><a name="[121]"></a>VI4302_En</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, GPIO.o(i.VI4302_En))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = VI4302_En
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Pins_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_HW_set_demo
</UL>

<P><STRONG><a name="[110]"></a>VI4302_Enable_DcDc</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Enable_DcDc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = VI4302_Enable_DcDc
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[11b]"></a>VI4302_Frame_Rate_AutoCtrl</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, VI4302_System.o(i.VI4302_Frame_Rate_AutoCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = VI4302_Frame_Rate_AutoCtrl &rArr; vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_status_from_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[128]"></a>VI4302_Get_Frame_Cnt</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, VI4302_System.o(i.VI4302_Get_Frame_Cnt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = VI4302_Get_Frame_Cnt
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_SinglePixel_Output
</UL>

<P><STRONG><a name="[10c]"></a>VI4302_InfifoClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uartfifo.o(i.VI4302_InfifoClear))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[9d]"></a>VI4302_InfifoDataIn</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, uartfifo.o(i.VI4302_InfifoDataIn))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[9e]"></a>VI4302_InfifoDataOut</STRONG> (Thumb, 112 bytes, Stack size 12 bytes, uartfifo.o(i.VI4302_InfifoDataOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = VI4302_InfifoDataOut
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[10f]"></a>VI4302_Pin_Init</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, VI4302_System.o(i.VI4302_Pin_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = VI4302_Pin_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[122]"></a>VI4302_Read_His_Config</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Read_His_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = VI4302_Read_His_Config &rArr; vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_status_from_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
</UL>

<P><STRONG><a name="[96]"></a>VI4302_Read_Histogram</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, VI4302_System.o(i.VI4302_Read_Histogram))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = VI4302_Read_Histogram &rArr; vi4302_read_his_reg &rArr; SPI1_WriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_his_reg
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[111]"></a>VI4302_Reg_Init</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, VI4302_System.o(i.VI4302_Reg_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = VI4302_Reg_Init &rArr; VI4302_Write_Reg &rArr; vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Write_Reg
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[11c]"></a>VI4302_Set_Bvd</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Set_Bvd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = VI4302_Set_Bvd
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_BVD_Calculate
</UL>

<P><STRONG><a name="[126]"></a>VI4302_Set_Frame_Data_Format</STRONG> (Thumb, 312 bytes, Stack size 24 bytes, VI4302_System.o(i.VI4302_Set_Frame_Data_Format))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = VI4302_Set_Frame_Data_Format
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_SinglePixel_Output
</UL>

<P><STRONG><a name="[12a]"></a>VI4302_Set_Mp_All</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Set_Mp_All))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = VI4302_Set_Mp_All
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_SinglePixel_Output
</UL>

<P><STRONG><a name="[127]"></a>VI4302_Set_Mp_Openonly</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, VI4302_System.o(i.VI4302_Set_Mp_Openonly))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = VI4302_Set_Mp_Openonly
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_SinglePixel_Output
</UL>

<P><STRONG><a name="[124]"></a>VI4302_SinglePixel_Output</STRONG> (Thumb, 338 bytes, Stack size 96 bytes, VI4302_System.o(i.VI4302_SinglePixel_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = VI4302_SinglePixel_Output &rArr; VI4302_read_frame
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_read_frame
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Stream_On
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Set_Mp_Openonly
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Set_Mp_All
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Set_Frame_Data_Format
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Get_Frame_Cnt
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
</UL>

<P><STRONG><a name="[12b]"></a>VI4302_Start_Ranging</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, VI4302_System.o(i.VI4302_Start_Ranging))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = VI4302_Start_Ranging &rArr; VI4302_Stop_Ranging &rArr; vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Stop_Ranging
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_status_from_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12c]"></a>VI4302_Stop_Ranging</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, VI4302_System.o(i.VI4302_Stop_Ranging))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = VI4302_Stop_Ranging &rArr; vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_status_from_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Start_Ranging
</UL>

<P><STRONG><a name="[125]"></a>VI4302_Stream_On</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Stream_On))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = VI4302_Stream_On
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_SinglePixel_Output
</UL>

<P><STRONG><a name="[117]"></a>VI4302_TDC_Cal</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_TDC_Cal))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = VI4302_TDC_Cal &rArr; vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_status_from_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[9a]"></a>VI4302_Temp_Bvd</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Temp_Bvd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = VI4302_Temp_Bvd
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[114]"></a>VI4302_Update_Config</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Update_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = VI4302_Update_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[116]"></a>VI4302_Update_Succcess_Fail</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Update_Succcess_Fail))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = VI4302_Update_Succcess_Fail
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[115]"></a>VI4302_Update_firmware</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, VI4302_System.o(i.VI4302_Update_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = VI4302_Update_firmware &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Week
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[11a]"></a>VI4302_Write_Reg</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, VI4302_System.o(i.VI4302_Write_Reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = VI4302_Write_Reg &rArr; vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_status_from_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Reg_Init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[129]"></a>VI4302_read_frame</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, VI4302_System.o(i.VI4302_read_frame))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = VI4302_read_frame
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_SinglePixel_Output
</UL>

<P><STRONG><a name="[98]"></a>WDT_Week</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, wdt.o(i.WDT_Week))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AllHandle
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Update_firmware
</UL>

<P><STRONG><a name="[130]"></a>XSJ_DealProtocol</STRONG> (Thumb, 290 bytes, Stack size 360 bytes, cmd_handle.o(i.XSJ_DealProtocol))
<BR><BR>[Stack]<UL><LI>Max Depth = 2500<LI>Call Chain = XSJ_DealProtocol &rArr; VI4302_AllInit &rArr; flash_SaveFmData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TX_Disen
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CmdAckSend
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Stop_Ranging
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Start_Ranging
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_SinglePixel_Output
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Read_His_Config
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_write_register
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_register
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_HW_set_demo
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_UART_AllHandle
</UL>

<P><STRONG><a name="[f4]"></a>XSJ_UART_AllHandle</STRONG> (Thumb, 154 bytes, Stack size 288 bytes, cmd_handle.o(i.XSJ_UART_AllHandle))
<BR><BR>[Stack]<UL><LI>Max Depth = 2788<LI>Call Chain = XSJ_UART_AllHandle &rArr; XSJ_DealProtocol &rArr; VI4302_AllInit &rArr; flash_SaveFmData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AllHandle
</UL>

<P><STRONG><a name="[13d]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[131]"></a>__hardfp_floor</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, floor.o(i.__hardfp_floor))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vis_tof_compensation
</UL>

<P><STRONG><a name="[7c]"></a>__hardfp_pow</STRONG> (Thumb, 3072 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adaptation_REG_and_ALGO
</UL>

<P><STRONG><a name="[13b]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[135]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[133]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[139]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[136]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[13a]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[15f]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[160]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[161]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[134]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[9c]"></a>cal_final_tof</STRONG> (Thumb, 224 bytes, Stack size 80 bytes, data_handle.o(i.cal_final_tof))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = cal_final_tof &rArr; vis_tof_compensation &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vis_tof_compensation
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Temperature_Rectified_Tof
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[10e]"></a>delay_ms</STRONG> (Thumb, 78 bytes, Stack size 40 bytes, delay.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Week
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DBG_SysTick_Config
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SnDataInit
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_DataInit
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Start_Ranging
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_read_status_from_firmware
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Reg_Init
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vi4302_HW_set_demo
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[138]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[141]"></a>flash_DataInit</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, flash.o(i.flash_DataInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 2124<LI>Call Chain = flash_DataInit &rArr; flash_SaveConfigerData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveConfigerData
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_ReadConfigerData
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[144]"></a>flash_FmDataInit</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, flash.o(i.flash_FmDataInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 2132<LI>Call Chain = flash_FmDataInit &rArr; flash_SaveFmData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveFmData
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[142]"></a>flash_ReadConfigerData</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, flash.o(i.flash_ReadConfigerData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = flash_ReadConfigerData &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveConfigerData
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_DataInit
</UL>

<P><STRONG><a name="[145]"></a>flash_ReadFmData</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, flash.o(i.flash_ReadFmData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = flash_ReadFmData &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveFmData
</UL>

<P><STRONG><a name="[146]"></a>flash_ReadSnData</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, flash.o(i.flash_ReadSnData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = flash_ReadSnData &rArr; STMFLASH_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SnDataInit
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveSnData
</UL>

<P><STRONG><a name="[143]"></a>flash_SaveConfigerData</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, flash.o(i.flash_SaveConfigerData))
<BR><BR>[Stack]<UL><LI>Max Depth = 2116<LI>Call Chain = flash_SaveConfigerData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_ReadConfigerData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_DataInit
</UL>

<P><STRONG><a name="[119]"></a>flash_SaveFmData</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, flash.o(i.flash_SaveFmData))
<BR><BR>[Stack]<UL><LI>Max Depth = 2116<LI>Call Chain = flash_SaveFmData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_ReadFmData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_FmDataInit
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[147]"></a>flash_SaveSnData</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, flash.o(i.flash_SaveSnData))
<BR><BR>[Stack]<UL><LI>Max Depth = 2116<LI>Call Chain = flash_SaveSnData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_ReadSnData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFLASH_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SnDataInit
</UL>

<P><STRONG><a name="[148]"></a>flash_SnDataInit</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, flash.o(i.flash_SnDataInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 2124<LI>Call Chain = flash_SnDataInit &rArr; flash_SaveSnData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SaveSnData
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_ReadSnData
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12f]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Update_firmware
</UL>

<P><STRONG><a name="[3c]"></a>main</STRONG> (Thumb, 94 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 3052<LI>Call Chain = main &rArr; UART_AllHandle &rArr; XSJ_UART_AllHandle &rArr; XSJ_DealProtocol &rArr; VI4302_AllInit &rArr; flash_SaveFmData &rArr; STMFLASH_Write &rArr; FLASH_Word_Program &rArr; FLASH_Last_Operation_Wait
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_Init
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_Configuration
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_SnDataInit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_FmDataInit
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_DataInit
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AllInit
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCO_OutputInit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Priority_Group_Set
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AllHandle
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ALL_ParaInit
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Start_Ranging
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[12d]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Update_firmware
</UL>

<P><STRONG><a name="[44]"></a>spi_write_data</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, VI4302_Handle.o(i.spi_write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = spi_write_data &rArr; SPI1_WriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsLow
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsHigh
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_WriteNByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> VI4302_Handle.o(i.user_driver_init)
</UL>
<P><STRONG><a name="[137]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[f9]"></a>uart1infifo_Clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uartfifo.o(i.uart1infifo_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[103]"></a>uart1infifo_DataIn</STRONG> (Thumb, 50 bytes, Stack size 12 bytes, uartfifo.o(i.uart1infifo_DataIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = uart1infifo_DataIn
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[f3]"></a>uart1infifo_DataOut</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, uartfifo.o(i.uart1infifo_DataOut))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AllHandle
</UL>

<P><STRONG><a name="[f2]"></a>uart1infifo_HaveData</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uartfifo.o(i.uart1infifo_HaveData))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AllHandle
</UL>

<P><STRONG><a name="[fa]"></a>uart1outfifo_Clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uartfifo.o(i.uart1outfifo_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[f0]"></a>uart1outfifo_DataIn</STRONG> (Thumb, 86 bytes, Stack size 12 bytes, uartfifo.o(i.uart1outfifo_DataIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = uart1outfifo_DataIn
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
</UL>

<P><STRONG><a name="[81]"></a>uart1outfifo_DataOut</STRONG> (Thumb, 92 bytes, Stack size 12 bytes, uartfifo.o(i.uart1outfifo_DataOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = uart1outfifo_DataOut
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel3_IRQHandler
</UL>

<P><STRONG><a name="[80]"></a>uart1outfifo_HaveData</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uartfifo.o(i.uart1outfifo_HaveData))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_SendData
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Channel3_IRQHandler
</UL>

<P><STRONG><a name="[10d]"></a>user_driver_init</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, VI4302_Handle.o(i.user_driver_init))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_AllInit
</UL>

<P><STRONG><a name="[3f]"></a>vi4302_HW_set_demo</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, VI4302_Handle.o(i.vi4302_HW_set_demo))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = vi4302_HW_set_demo &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_En
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Disen
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
</UL>
<BR>[Address Reference Count : 1]<UL><LI> VI4302_Handle.o(i.user_driver_init)
</UL>
<P><STRONG><a name="[123]"></a>vi4302_read_his_reg</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, VI4302_Handle.o(i.vi4302_read_his_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = vi4302_read_his_reg &rArr; SPI1_WriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsLow
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsHigh
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_WriteNByte
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadNByte
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Read_Histogram
</UL>

<P><STRONG><a name="[42]"></a>vi4302_read_mul_reg</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, VI4302_Handle.o(i.vi4302_read_mul_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = vi4302_read_mul_reg &rArr; SPI1_WriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsLow
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsHigh
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_WriteNByte
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadNByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> VI4302_Handle.o(i.user_driver_init)
</UL>
<P><STRONG><a name="[99]"></a>vi4302_read_ranging_data_with_firmware</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, VI4302_Handle.o(i.vi4302_read_ranging_data_with_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vi4302_read_ranging_data_with_firmware &rArr; SPI1_WriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsLow
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsHigh
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_WriteNByte
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadNByte
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_instruction
</UL>

<P><STRONG><a name="[40]"></a>vi4302_read_register</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, VI4302_Handle.o(i.vi4302_read_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vi4302_read_register &rArr; SPI1_ReadWriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsLow
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsHigh
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteNByte
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
</UL>
<BR>[Address Reference Count : 1]<UL><LI> VI4302_Handle.o(i.user_driver_init)
</UL>
<P><STRONG><a name="[43]"></a>vi4302_write_cmd</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, VI4302_Handle.o(i.vi4302_write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vi4302_write_cmd &rArr; SPI1_WriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsLow
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsHigh
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_WriteNByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> VI4302_Handle.o(i.user_driver_init)
</UL>
<P><STRONG><a name="[41]"></a>vi4302_write_register</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, VI4302_Handle.o(i.vi4302_write_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = vi4302_write_register &rArr; SPI1_ReadWriteNByte &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsLow
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_CsHigh
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteNByte
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XSJ_DealProtocol
</UL>
<BR>[Address Reference Count : 1]<UL><LI> VI4302_Handle.o(i.user_driver_init)
</UL>
<P><STRONG><a name="[13f]"></a>vis_tof_compensation</STRONG> (Thumb, 2236 bytes, Stack size 128 bytes, data_handle.o(i.vis_tof_compensation))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = vis_tof_compensation &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cal_final_tof
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[140]"></a>DBG_SysTick_Config</STRONG> (Thumb, 86 bytes, Stack size 12 bytes, delay.o(i.DBG_SysTick_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DBG_SysTick_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[da]"></a>System_Clock_Set</STRONG> (Thumb, 280 bytes, Stack size 24 bytes, system_n32g401.o(i.System_Clock_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = System_Clock_Set &rArr; PLL_TrimValueLoad &rArr; Get_NVR_Value
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PLL_TrimValueLoad
</UL>
<BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Initializes
</UL>

<P><STRONG><a name="[11d]"></a>vi4302_read_status_from_firmware</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, VI4302_System.o(i.vi4302_read_status_from_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = vi4302_read_status_from_firmware &rArr; delay_ms &rArr; RCC_Clocks_Frequencies_Value_Get
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Stop_Ranging
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Start_Ranging
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Read_His_Config
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Write_Reg
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_TDC_Cal
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Frame_Rate_AutoCtrl
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VI4302_Bvd_Cal
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
