Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    GPIO.o(i.GPIO_Init) refers to n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Enable) for RCC_AHB_Peripheral_Clock_Enable
    GPIO.o(i.GPIO_Init) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Clock_Enable) for RCC_APB2_Peripheral_Clock_Enable
    GPIO.o(i.GPIO_Init) refers to n32g401_gpio.o(i.GPIO_Pins_Reset) for GPIO_Pins_Reset
    GPIO.o(i.GPIO_Init) refers to n32g401_gpio.o(i.GPIO_Structure_Initialize) for GPIO_Structure_Initialize
    GPIO.o(i.GPIO_Init) refers to n32g401_gpio.o(i.GPIO_Peripheral_Initialize) for GPIO_Peripheral_Initialize
    GPIO.o(i.GPIO_Init) refers to n32g401_gpio.o(i.GPIO_EXTI_Line_Set) for GPIO_EXTI_Line_Set
    GPIO.o(i.GPIO_Init) refers to n32g401_exti.o(i.EXTI_Peripheral_Initializes) for EXTI_Peripheral_Initializes
    GPIO.o(i.GPIO_Init) refers to GPIO.o(i.NVIC_Configuration) for NVIC_Configuration
    GPIO.o(i.NVIC_Configuration) refers to misc.o(i.NVIC_Initializes) for NVIC_Initializes
    GPIO.o(i.RCO_OutputInit) refers to memseta.o(.text) for __aeabi_memclr4
    GPIO.o(i.RCO_OutputInit) refers to n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Enable) for RCC_AHB_Peripheral_Clock_Enable
    GPIO.o(i.RCO_OutputInit) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Clock_Enable) for RCC_APB2_Peripheral_Clock_Enable
    GPIO.o(i.RCO_OutputInit) refers to n32g401_gpio.o(i.GPIO_Structure_Initialize) for GPIO_Structure_Initialize
    GPIO.o(i.RCO_OutputInit) refers to n32g401_gpio.o(i.GPIO_Peripheral_Initialize) for GPIO_Peripheral_Initialize
    GPIO.o(i.RCO_OutputInit) refers to n32g401_rcc.o(i.RCC_MCO_Source_Config) for RCC_MCO_Source_Config
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_Reset) for RCC_Reset
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_HSE_Config) for RCC_HSE_Config
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_HSE_Stable_Wait) for RCC_HSE_Stable_Wait
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_flash.o(i.FLASH_Latency_Set) for FLASH_Latency_Set
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_Hclk_Config) for RCC_Hclk_Config
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_Pclk2_Config) for RCC_Pclk2_Config
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_Pclk1_Config) for RCC_Pclk1_Config
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_PLL_Config) for RCC_PLL_Config
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_PLL_Enable) for RCC_PLL_Enable
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_Sysclk_Config) for RCC_Sysclk_Config
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_Sysclk_Source_Get) for RCC_Sysclk_Source_Get
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_Clock_Security_System_Enable) for RCC_Clock_Security_System_Enable
    GPIO.o(i.SetSysClockToPLL) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    GPIO.o(i.TX_Disen) refers to n32g401_gpio.o(i.GPIO_Pins_Reset) for GPIO_Pins_Reset
    GPIO.o(i.TX_En) refers to n32g401_gpio.o(i.GPIO_Pins_Set) for GPIO_Pins_Set
    GPIO.o(i.VI4302_Disen) refers to n32g401_gpio.o(i.GPIO_Pins_Reset) for GPIO_Pins_Reset
    GPIO.o(i.VI4302_En) refers to n32g401_gpio.o(i.GPIO_Pins_Set) for GPIO_Pins_Set
    adc.o(i.ADC_AllInit) refers to n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Enable) for RCC_AHB_Peripheral_Clock_Enable
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Clock_Mode_Config) for ADC_Clock_Mode_Config
    adc.o(i.ADC_AllInit) refers to n32g401_rcc.o(i.RCC_ADC_1M_Clock_Config) for RCC_ADC_1M_Clock_Config
    adc.o(i.ADC_AllInit) refers to n32g401_gpio.o(i.GPIO_Structure_Initialize) for GPIO_Structure_Initialize
    adc.o(i.ADC_AllInit) refers to n32g401_gpio.o(i.GPIO_Peripheral_Initialize) for GPIO_Peripheral_Initialize
    adc.o(i.ADC_AllInit) refers to n32g401_dma.o(i.DMA_Reset) for DMA_Reset
    adc.o(i.ADC_AllInit) refers to n32g401_dma.o(i.DMA_Initializes) for DMA_Initializes
    adc.o(i.ADC_AllInit) refers to n32g401_dma.o(i.DMA_Channel_Request_Remap) for DMA_Channel_Request_Remap
    adc.o(i.ADC_AllInit) refers to n32g401_dma.o(i.DMA_Channel_Enable) for DMA_Channel_Enable
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Initializes_Structure) for ADC_Initializes_Structure
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Initializes) for ADC_Initializes
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Channel_Sample_Time_Config) for ADC_Channel_Sample_Time_Config
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Regular_Sequence_Conversion_Number_Config) for ADC_Regular_Sequence_Conversion_Number_Config
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Temperature_Sensor_And_Vrefint_Channel_Enable) for ADC_Temperature_Sensor_And_Vrefint_Channel_Enable
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_DMA_Transfer_Enable) for ADC_DMA_Transfer_Enable
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_ON) for ADC_ON
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Flag_Status_Get) for ADC_Flag_Status_Get
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Calibration_Operation) for ADC_Calibration_Operation
    adc.o(i.ADC_AllInit) refers to n32g401_adc.o(i.ADC_Regular_Channels_Software_Conversion_Operation) for ADC_Regular_Channels_Software_Conversion_Operation
    adc.o(i.ADC_AllInit) refers to adc.o(.bss) for g_ADCBufferSize
    adc.o(i.NTC_TempGet) refers to adc.o(.bss) for g_ADCBufferSize
    adc.o(i.NTC_TempGet) refers to adc.o(.constdata) for NTC_ResgistBuff
    delay.o(i.HAL_Timer_Delay_ms) refers to delay.o(i.delay_ms) for delay_ms
    delay.o(i.HAL_Timer_Delay_us) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_ms) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    delay.o(i.delay_ms) refers to delay.o(i.DBG_SysTick_Config) for DBG_SysTick_Config
    delay.o(i.delay_ms) refers to wdt.o(i.WDT_Week) for WDT_Week
    delay.o(i.delay_us) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    delay.o(i.delay_us) refers to delay.o(i.DBG_SysTick_Config) for DBG_SysTick_Config
    flash.o(i.STMFLASH_Read) refers to flash.o(i.STMFLASH_ReadByte) for STMFLASH_ReadByte
    flash.o(i.STMFLASH_Write) refers to memseta.o(.text) for __aeabi_memclr4
    flash.o(i.STMFLASH_Write) refers to n32g401_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    flash.o(i.STMFLASH_Write) refers to flash.o(i.STMFLASH_ReadWord) for STMFLASH_ReadWord
    flash.o(i.STMFLASH_Write) refers to n32g401_flash.o(i.FLASH_One_Page_Erase) for FLASH_One_Page_Erase
    flash.o(i.STMFLASH_Write) refers to n32g401_flash.o(i.FLASH_Word_Program) for FLASH_Word_Program
    flash.o(i.STMFLASH_Write) refers to wdt.o(i.WDT_Week) for WDT_Week
    flash.o(i.STMFLASH_Write) refers to n32g401_flash.o(i.FLASH_Lock) for FLASH_Lock
    flash.o(i.flash_DataInit) refers to flash.o(i.flash_ReadConfigerData) for flash_ReadConfigerData
    flash.o(i.flash_DataInit) refers to delay.o(i.delay_ms) for delay_ms
    flash.o(i.flash_DataInit) refers to flash.o(i.flash_SaveConfigerData) for flash_SaveConfigerData
    flash.o(i.flash_DataInit) refers to flash.o(.bss) for g_dataNeedSaved
    flash.o(i.flash_FmDataInit) refers to flash.o(i.flash_SaveFmData) for flash_SaveFmData
    flash.o(i.flash_FmDataInit) refers to flash.o(.bss) for g_fmNeedSaved
    flash.o(i.flash_FmDataInit) refers to flash.o(.constdata) for VI4302_NORMAL_QUANJU_PARA
    flash.o(i.flash_ReadConfigerData) refers to flash.o(i.STMFLASH_Read) for STMFLASH_Read
    flash.o(i.flash_ReadConfigerData) refers to flash.o(.bss) for g_dataNeedSaved
    flash.o(i.flash_ReadFmData) refers to flash.o(i.STMFLASH_Read) for STMFLASH_Read
    flash.o(i.flash_ReadFmData) refers to flash.o(.bss) for g_fmNeedSaved
    flash.o(i.flash_ReadSnData) refers to flash.o(i.STMFLASH_Read) for STMFLASH_Read
    flash.o(i.flash_ReadSnData) refers to flash.o(.bss) for g_snNeedSaved
    flash.o(i.flash_SaveConfigerData) refers to flash.o(i.STMFLASH_Write) for STMFLASH_Write
    flash.o(i.flash_SaveConfigerData) refers to flash.o(i.flash_ReadConfigerData) for flash_ReadConfigerData
    flash.o(i.flash_SaveConfigerData) refers to flash.o(.bss) for g_dataNeedSaved
    flash.o(i.flash_SaveFmData) refers to flash.o(i.STMFLASH_Write) for STMFLASH_Write
    flash.o(i.flash_SaveFmData) refers to flash.o(i.flash_ReadFmData) for flash_ReadFmData
    flash.o(i.flash_SaveFmData) refers to flash.o(.bss) for g_fmNeedSaved
    flash.o(i.flash_SaveSnData) refers to flash.o(i.STMFLASH_Write) for STMFLASH_Write
    flash.o(i.flash_SaveSnData) refers to flash.o(i.flash_ReadSnData) for flash_ReadSnData
    flash.o(i.flash_SaveSnData) refers to flash.o(.bss) for g_snNeedSaved
    flash.o(i.flash_SnDataInit) refers to flash.o(i.flash_ReadSnData) for flash_ReadSnData
    flash.o(i.flash_SnDataInit) refers to delay.o(i.delay_ms) for delay_ms
    flash.o(i.flash_SnDataInit) refers to flash.o(i.flash_SaveSnData) for flash_SaveSnData
    flash.o(i.flash_SnDataInit) refers to flash.o(.bss) for g_snNeedSaved
    spi.o(i.SPI1_Init) refers to n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Enable) for RCC_AHB_Peripheral_Clock_Enable
    spi.o(i.SPI1_Init) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Clock_Enable) for RCC_APB2_Peripheral_Clock_Enable
    spi.o(i.SPI1_Init) refers to n32g401_gpio.o(i.GPIO_Structure_Initialize) for GPIO_Structure_Initialize
    spi.o(i.SPI1_Init) refers to n32g401_gpio.o(i.GPIO_Peripheral_Initialize) for GPIO_Peripheral_Initialize
    spi.o(i.SPI1_Init) refers to n32g401_gpio.o(i.GPIO_Pins_Set) for GPIO_Pins_Set
    spi.o(i.SPI1_Init) refers to n32g401_spi.o(i.SPI_I2S_Reset) for SPI_I2S_Reset
    spi.o(i.SPI1_Init) refers to n32g401_spi.o(i.SPI_Initializes_Structure) for SPI_Initializes_Structure
    spi.o(i.SPI1_Init) refers to n32g401_spi.o(i.SPI_Initializes) for SPI_Initializes
    spi.o(i.SPI1_Init) refers to n32g401_spi.o(i.SPI_Set_Nss_Level) for SPI_Set_Nss_Level
    spi.o(i.SPI1_Init) refers to n32g401_spi.o(i.SPI_CRC_Disable) for SPI_CRC_Disable
    spi.o(i.SPI1_Init) refers to n32g401_spi.o(i.SPI_ON) for SPI_ON
    spi.o(i.SPI1_ReadNByte) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    spi.o(i.SPI1_ReadWriteByte) refers to n32g401_spi.o(i.SPI_I2S_Flag_Status_Get) for SPI_I2S_Flag_Status_Get
    spi.o(i.SPI1_ReadWriteByte) refers to n32g401_spi.o(i.SPI_I2S_Data_Transmit) for SPI_I2S_Data_Transmit
    spi.o(i.SPI1_ReadWriteByte) refers to n32g401_spi.o(i.SPI_I2S_Data_Get) for SPI_I2S_Data_Get
    spi.o(i.SPI1_ReadWriteByte) refers to wdt.o(i.WDT_Week) for WDT_Week
    spi.o(i.SPI1_ReadWriteNByte) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    spi.o(i.SPI1_WriteNByte) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    spi.o(i.VI4302_CsHigh) refers to n32g401_gpio.o(i.GPIO_Pins_Set) for GPIO_Pins_Set
    spi.o(i.VI4302_CsLow) refers to n32g401_gpio.o(i.GPIO_Pins_Reset) for GPIO_Pins_Reset
    timer.o(i.TIM6_Configuration) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Clock_Enable) for RCC_APB1_Peripheral_Clock_Enable
    timer.o(i.TIM6_Configuration) refers to n32g401_tim.o(i.TIM_Base_Struct_Initialize) for TIM_Base_Struct_Initialize
    timer.o(i.TIM6_Configuration) refers to n32g401_tim.o(i.TIM_Base_Initialize) for TIM_Base_Initialize
    timer.o(i.TIM6_Configuration) refers to n32g401_tim.o(i.TIM_Base_Reload_Mode_Set) for TIM_Base_Reload_Mode_Set
    timer.o(i.TIM6_Configuration) refers to GPIO.o(i.NVIC_Configuration) for NVIC_Configuration
    timer.o(i.TIM6_Configuration) refers to n32g401_tim.o(i.TIM_Interrupt_Enable) for TIM_Interrupt_Enable
    timer.o(i.TIM6_Configuration) refers to n32g401_tim.o(i.TIM_On) for TIM_On
    usart.o(i.DMA_Channel3_IRQHandler) refers to n32g401_dma.o(i.DMA_Interrupt_Status_Get) for DMA_Interrupt_Status_Get
    usart.o(i.DMA_Channel3_IRQHandler) refers to n32g401_dma.o(i.DMA_Interrupt_Status_Clear) for DMA_Interrupt_Status_Clear
    usart.o(i.DMA_Channel3_IRQHandler) refers to n32g401_dma.o(i.DMA_Channel_Disable) for DMA_Channel_Disable
    usart.o(i.DMA_Channel3_IRQHandler) refers to uartfifo.o(i.uart1outfifo_HaveData) for uart1outfifo_HaveData
    usart.o(i.DMA_Channel3_IRQHandler) refers to uartfifo.o(i.uart1outfifo_DataOut) for uart1outfifo_DataOut
    usart.o(i.DMA_Channel3_IRQHandler) refers to n32g401_dma.o(i.DMA_Buffer_Size_Config) for DMA_Buffer_Size_Config
    usart.o(i.DMA_Channel3_IRQHandler) refers to n32g401_dma.o(i.DMA_Channel_Enable) for DMA_Channel_Enable
    usart.o(i.DMA_Channel3_IRQHandler) refers to usart.o(.bss) for UART2_SendBuff
    usart.o(i.DMA_Channel3_IRQHandler) refers to usart.o(.data) for UART2_SendFlag
    usart.o(i.UART2_SendData) refers to uartfifo.o(i.uart1outfifo_DataIn) for uart1outfifo_DataIn
    usart.o(i.UART2_SendData) refers to uartfifo.o(i.uart1outfifo_HaveData) for uart1outfifo_HaveData
    usart.o(i.UART2_SendData) refers to n32g401_dma.o(i.DMA_Channel_Disable) for DMA_Channel_Disable
    usart.o(i.UART2_SendData) refers to n32g401_dma.o(i.DMA_Interrupt_Status_Clear) for DMA_Interrupt_Status_Clear
    usart.o(i.UART2_SendData) refers to uartfifo.o(i.uart1outfifo_DataOut) for uart1outfifo_DataOut
    usart.o(i.UART2_SendData) refers to n32g401_dma.o(i.DMA_Buffer_Size_Config) for DMA_Buffer_Size_Config
    usart.o(i.UART2_SendData) refers to n32g401_dma.o(i.DMA_Channel_Enable) for DMA_Channel_Enable
    usart.o(i.UART2_SendData) refers to usart.o(.data) for UART2_SendFlag
    usart.o(i.UART2_SendData) refers to usart.o(.bss) for UART2_SendBuff
    usart.o(i.USART2_DmaConfig) refers to n32g401_dma.o(i.DMA_Reset) for DMA_Reset
    usart.o(i.USART2_DmaConfig) refers to n32g401_dma.o(i.DMA_Initializes) for DMA_Initializes
    usart.o(i.USART2_DmaConfig) refers to n32g401_dma.o(i.DMA_Channel_Request_Remap) for DMA_Channel_Request_Remap
    usart.o(i.USART2_DmaConfig) refers to n32g401_dma.o(i.DMA_Channel_Enable) for DMA_Channel_Enable
    usart.o(i.USART2_DmaConfig) refers to n32g401_dma.o(i.DMA_Channel_Disable) for DMA_Channel_Disable
    usart.o(i.USART2_DmaConfig) refers to n32g401_dma.o(i.DMA_Interrupts_Enable) for DMA_Interrupts_Enable
    usart.o(i.USART2_DmaConfig) refers to GPIO.o(i.NVIC_Configuration) for NVIC_Configuration
    usart.o(i.USART2_DmaConfig) refers to usart.o(.bss) for UART2_SendBuff
    usart.o(i.USART2_IRQHandler) refers to n32g401_usart.o(i.USART_Interrupt_Status_Get) for USART_Interrupt_Status_Get
    usart.o(i.USART2_IRQHandler) refers to n32g401_usart.o(i.USART_Interrupt_Status_Clear) for USART_Interrupt_Status_Clear
    usart.o(i.USART2_IRQHandler) refers to n32g401_usart.o(i.USART_Data_Receive) for USART_Data_Receive
    usart.o(i.USART2_IRQHandler) refers to n32g401_dma.o(i.DMA_Channel_Disable) for DMA_Channel_Disable
    usart.o(i.USART2_IRQHandler) refers to n32g401_dma.o(i.DMA_Current_Data_Transfer_Number_Get) for DMA_Current_Data_Transfer_Number_Get
    usart.o(i.USART2_IRQHandler) refers to n32g401_dma.o(i.DMA_Buffer_Size_Config) for DMA_Buffer_Size_Config
    usart.o(i.USART2_IRQHandler) refers to n32g401_dma.o(i.DMA_Channel_Enable) for DMA_Channel_Enable
    usart.o(i.USART2_IRQHandler) refers to uartfifo.o(i.uart1infifo_DataIn) for uart1infifo_DataIn
    usart.o(i.USART2_IRQHandler) refers to usart.o(.bss) for UART2_RecieveBuff
    usart.o(i.USART2_Init) refers to uartfifo.o(i.uart1infifo_Clear) for uart1infifo_Clear
    usart.o(i.USART2_Init) refers to uartfifo.o(i.uart1outfifo_Clear) for uart1outfifo_Clear
    usart.o(i.USART2_Init) refers to n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Enable) for RCC_AHB_Peripheral_Clock_Enable
    usart.o(i.USART2_Init) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Clock_Enable) for RCC_APB2_Peripheral_Clock_Enable
    usart.o(i.USART2_Init) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Clock_Enable) for RCC_APB1_Peripheral_Clock_Enable
    usart.o(i.USART2_Init) refers to n32g401_gpio.o(i.GPIO_Structure_Initialize) for GPIO_Structure_Initialize
    usart.o(i.USART2_Init) refers to n32g401_gpio.o(i.GPIO_Peripheral_Initialize) for GPIO_Peripheral_Initialize
    usart.o(i.USART2_Init) refers to n32g401_usart.o(i.USART_Initializes) for USART_Initializes
    usart.o(i.USART2_Init) refers to usart.o(i.USART2_DmaConfig) for USART2_DmaConfig
    usart.o(i.USART2_Init) refers to n32g401_usart.o(i.USART_DMA_Transfer_Enable) for USART_DMA_Transfer_Enable
    usart.o(i.USART2_Init) refers to n32g401_usart.o(i.USART_Interrput_Enable) for USART_Interrput_Enable
    usart.o(i.USART2_Init) refers to GPIO.o(i.NVIC_Configuration) for NVIC_Configuration
    usart.o(i.USART2_Init) refers to n32g401_usart.o(i.USART_Enable) for USART_Enable
    usart.o(i.fputc) refers to usart.o(i.UART2_SendData) for UART2_SendData
    startup_n32g401.o(RESET) refers to startup_n32g401.o(STACK) for __initial_sp
    startup_n32g401.o(RESET) refers to startup_n32g401.o(.text) for Reset_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.NMI_Handler) for NMI_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.SVC_Handler) for SVC_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_n32g401.o(RESET) refers to n32g401_it.o(i.RCC_IRQHandler) for RCC_IRQHandler
    startup_n32g401.o(RESET) refers to main.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_n32g401.o(RESET) refers to main.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_n32g401.o(RESET) refers to usart.o(i.DMA_Channel3_IRQHandler) for DMA_Channel3_IRQHandler
    startup_n32g401.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_n32g401.o(RESET) refers to main.o(i.TIM6_IRQHandler) for TIM6_IRQHandler
    startup_n32g401.o(.text) refers to system_n32g401.o(i.System_Initializes) for System_Initializes
    startup_n32g401.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_n32g401.o(i.Get_NVR_Value) refers to system_n32g401.o(.ARM.__AT_0x08002118) for const_data1
    system_n32g401.o(i.Get_NVR_Value) refers to system_n32g401.o(.ARM.__AT_0x08002918) for const_data2
    system_n32g401.o(i.PLL_TrimValueLoad) refers to system_n32g401.o(i.Get_NVR_Value) for Get_NVR_Value
    system_n32g401.o(i.System_Clock_Frequency_Update) refers to system_n32g401.o(.data) for SystemClockFrequency
    system_n32g401.o(i.System_Clock_Frequency_Update) refers to system_n32g401.o(.constdata) for AHBPrescTable
    system_n32g401.o(i.System_Clock_Set) refers to system_n32g401.o(i.PLL_TrimValueLoad) for PLL_TrimValueLoad
    system_n32g401.o(i.System_Clock_Set) refers to system_n32g401.o(.data) for SystemClockFrequency
    system_n32g401.o(i.System_Initializes) refers to system_n32g401.o(i.System_Clock_Set) for System_Clock_Set
    n32g401_adc.o(i.ADC_Clock_Mode_Config) refers to n32g401_rcc.o(i.RCC_ADC_Hclk_Enable) for RCC_ADC_Hclk_Enable
    n32g401_adc.o(i.ADC_Clock_Mode_Config) refers to n32g401_rcc.o(i.RCC_ADC_PLL_Clock_Disable) for RCC_ADC_PLL_Clock_Disable
    n32g401_adc.o(i.ADC_Clock_Mode_Config) refers to n32g401_rcc.o(i.RCC_ADC_Hclk_Config) for RCC_ADC_Hclk_Config
    n32g401_adc.o(i.ADC_Clock_Mode_Config) refers to n32g401_adc.o(i.ADC_AHB_Clock_Mode_Config) for ADC_AHB_Clock_Mode_Config
    n32g401_adc.o(i.ADC_Clock_Mode_Config) refers to n32g401_rcc.o(i.RCC_ADC_PLL_Clock_Prescaler_Enable) for RCC_ADC_PLL_Clock_Prescaler_Enable
    n32g401_adc.o(i.ADC_Clock_Mode_Config) refers to n32g401_adc.o(i.ADC_PLL_Clock_Mode_Config) for ADC_PLL_Clock_Mode_Config
    n32g401_adc.o(i.ADC_Initializes) refers to n32g401_adc.o(i.ADC_Multichannels_Enable) for ADC_Multichannels_Enable
    n32g401_adc.o(i.ADC_Initializes) refers to n32g401_adc.o(i.ADC_Multichannels_Disable) for ADC_Multichannels_Disable
    n32g401_adc.o(i.ADC_Initializes) refers to n32g401_adc.o(i.ADC_Continue_Conversion_Enable) for ADC_Continue_Conversion_Enable
    n32g401_adc.o(i.ADC_Initializes) refers to n32g401_adc.o(i.ADC_Continue_Conversion_Disable) for ADC_Continue_Conversion_Disable
    n32g401_adc.o(i.ADC_Initializes) refers to n32g401_adc.o(i.ADC_Regular_Group_External_Trigger_Source_Config) for ADC_Regular_Group_External_Trigger_Source_Config
    n32g401_adc.o(i.ADC_Initializes) refers to n32g401_adc.o(i.ADC_Data_Alignment_Config) for ADC_Data_Alignment_Config
    n32g401_adc.o(i.ADC_Initializes) refers to n32g401_adc.o(i.ADC_Regular_Channels_Number_Config) for ADC_Regular_Channels_Number_Config
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Vbat_Monitor_Enable) for ADC_Vbat_Monitor_Enable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Vbat_Monitor_Disable) for ADC_Vbat_Monitor_Disable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Deep_Power_Mode_Enable) for ADC_Deep_Power_Mode_Enable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Deep_Power_Mode_Disable) for ADC_Deep_Power_Mode_Disable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Interrupts_Enable) for ADC_Interrupts_Enable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Interrupts_Disable) for ADC_Interrupts_Disable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_PLL_Clock_Mode_Config) for ADC_PLL_Clock_Mode_Config
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_AHB_Clock_Mode_Config) for ADC_AHB_Clock_Mode_Config
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Calibration_Auto_Load_Enable) for ADC_Calibration_Auto_Load_Enable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Calibration_Auto_Load_Disable) for ADC_Calibration_Auto_Load_Disable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Differential_Mode_Enable) for ADC_Differential_Mode_Enable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Differential_Mode_Disable) for ADC_Differential_Mode_Disable
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Data_Resolution_Config) for ADC_Data_Resolution_Config
    n32g401_adc.o(i.ADC_Initializes_Ex) refers to n32g401_adc.o(i.ADC_Sample_Time_Level_Config) for ADC_Sample_Time_Level_Config
    n32g401_adc.o(i.ADC_Reset) refers to n32g401_rcc.o(i.RCC_AHB_Peripheral_Reset) for RCC_AHB_Peripheral_Reset
    n32g401_adc.o(i.ADC_Vrefint_Get) refers to system_n32g401.o(i.Get_NVR_Value) for Get_NVR_Value
    n32g401_adc.o(i.ADC_Vrefint_Get) refers to n32g401_adc.o(.data) for vrefint_value
    n32g401_beeper.o(i.BEEPER_Initialize) refers to n32g401_beeper.o(i.BEEPER_Clock_Select) for BEEPER_Clock_Select
    n32g401_beeper.o(i.BEEPER_Initialize) refers to n32g401_beeper.o(i.BEEPER_APB_Clock_Prescale_Set) for BEEPER_APB_Clock_Prescale_Set
    n32g401_beeper.o(i.BEEPER_Initialize) refers to n32g401_beeper.o(i.BEEPER_Div_Factor_Select) for BEEPER_Div_Factor_Select
    n32g401_beeper.o(i.BEEPER_Reset) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Reset) for RCC_APB2_Peripheral_Reset
    n32g401_comp.o(i.COMP_Filter_Control_Config) refers to n32g401_comp.o(i.COMP_Filter_SampWindow_Config) for COMP_Filter_SampWindow_Config
    n32g401_comp.o(i.COMP_Filter_Control_Config) refers to n32g401_comp.o(i.COMP_Filter_Threshold_Config) for COMP_Filter_Threshold_Config
    n32g401_comp.o(i.COMP_Filter_Control_Config) refers to n32g401_comp.o(i.COMP_Filter_Enable) for COMP_Filter_Enable
    n32g401_comp.o(i.COMP_Filter_Control_Config) refers to n32g401_comp.o(i.COMP_Filter_Disable) for COMP_Filter_Disable
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Filter_SampWindow_Config) for COMP_Filter_SampWindow_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Filter_Threshold_Config) for COMP_Filter_Threshold_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Filter_Enable) for COMP_Filter_Enable
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Filter_Disable) for COMP_Filter_Disable
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Filter_Clock_Prescale_Config) for COMP_Filter_Clock_Prescale_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Blking_Soucre_Config) for COMP_Blking_Soucre_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Hysteresis_Level_Config) for COMP_Hysteresis_Level_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Output_Polarity_Config) for COMP_Output_Polarity_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_InpSel_Config) for COMP_InpSel_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_InmSel_Config) for COMP_InmSel_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_Output_Trigger_Config) for COMP_Output_Trigger_Config
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_ON) for COMP_ON
    n32g401_comp.o(i.COMP_Initializes) refers to n32g401_comp.o(i.COMP_OFF) for COMP_OFF
    n32g401_comp.o(i.COMP_Reset) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Reset) for RCC_APB1_Peripheral_Reset
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Peripheral_Address_Config) for DMA_Peripheral_Address_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Memory_Address_Config) for DMA_Memory_Address_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Destination_Config) for DMA_Destination_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Buffer_Size_Config) for DMA_Buffer_Size_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Peripheral_Addr_Increment_Config) for DMA_Peripheral_Addr_Increment_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Memory_Addr_Increment_Config) for DMA_Memory_Addr_Increment_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Peripheral_Data_Width_Config) for DMA_Peripheral_Data_Width_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Memory_Data_Width_Config) for DMA_Memory_Data_Width_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Circular_Mode_Config) for DMA_Circular_Mode_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Priority_Config) for DMA_Priority_Config
    n32g401_dma.o(i.DMA_Initializes) refers to n32g401_dma.o(i.DMA_Memory_2_Memory_Config) for DMA_Memory_2_Memory_Config
    n32g401_exti.o(i.EXTI_Peripheral_Initializes) refers to n32g401_exti.o(i.EXTI_Work_Mode_Config) for EXTI_Work_Mode_Config
    n32g401_exti.o(i.EXTI_Peripheral_Initializes) refers to n32g401_exti.o(i.EXTI_Trigger_Config) for EXTI_Trigger_Config
    n32g401_exti.o(i.EXTI_Peripheral_Initializes) refers to n32g401_exti.o(i.EXTI_LineCmd_Disable) for EXTI_LineCmd_Disable
    n32g401_flash.o(i.FLASH_Last_Operation_Wait) refers to n32g401_flash.o(i.FLASH_Status_Get) for FLASH_Status_Get
    n32g401_flash.o(i.FLASH_Mass_Erase) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Mass_Erase) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_One_Page_Erase) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_One_Page_Erase) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Option_Bytes_DATA_Program) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Option_Bytes_DATA_Program) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Option_Bytes_DATA_Program) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get) for FLASH_Read_Out_Protection_L2_Status_Get
    n32g401_flash.o(i.FLASH_Option_Bytes_DATA_Program) refers to n32g401_flash.o(i.Option_Bytes_Unlock) for Option_Bytes_Unlock
    n32g401_flash.o(i.FLASH_Option_Bytes_Erase) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get) for FLASH_Read_Out_Protection_L2_Status_Get
    n32g401_flash.o(i.FLASH_Option_Bytes_Erase) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Option_Bytes_Erase) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Option_Bytes_Erase) refers to n32g401_flash.o(i.Option_Bytes_Unlock) for Option_Bytes_Unlock
    n32g401_flash.o(i.FLASH_Option_Bytes_User2_RDP2_Program) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get) for FLASH_Read_Out_Protection_L2_Status_Get
    n32g401_flash.o(i.FLASH_Option_Bytes_User2_RDP2_Program) refers to n32g401_flash.o(i.Option_Bytes_Unlock) for Option_Bytes_Unlock
    n32g401_flash.o(i.FLASH_Option_Bytes_User2_RDP2_Program) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Option_Bytes_User2_RDP2_Program) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Option_Bytes_User_RDP1_Program) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get) for FLASH_Read_Out_Protection_L2_Status_Get
    n32g401_flash.o(i.FLASH_Option_Bytes_User_RDP1_Program) refers to n32g401_flash.o(i.Option_Bytes_Unlock) for Option_Bytes_Unlock
    n32g401_flash.o(i.FLASH_Option_Bytes_User_RDP1_Program) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Option_Bytes_User_RDP1_Program) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Disable) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get) for FLASH_Read_Out_Protection_L2_Status_Get
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Disable) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Disable) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Disable) refers to n32g401_flash.o(i.Option_Bytes_Unlock) for Option_Bytes_Unlock
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Enable) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get) for FLASH_Read_Out_Protection_L2_Status_Get
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Enable) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Enable) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Enable) refers to n32g401_flash.o(i.Option_Bytes_Unlock) for Option_Bytes_Unlock
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Enable) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_Status_Get) for FLASH_Read_Out_Protection_Status_Get
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Enable) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get) for FLASH_Read_Out_Protection_L2_Status_Get
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Enable) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Enable) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Enable) refers to n32g401_flash.o(i.Option_Bytes_Unlock) for Option_Bytes_Unlock
    n32g401_flash.o(i.FLASH_Word_Program) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Word_Program) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Write_Protection_Enable) refers to n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get) for FLASH_Read_Out_Protection_L2_Status_Get
    n32g401_flash.o(i.FLASH_Write_Protection_Enable) refers to n32g401_flash.o(i.FLASH_Flag_Status_Clear) for FLASH_Flag_Status_Clear
    n32g401_flash.o(i.FLASH_Write_Protection_Enable) refers to n32g401_flash.o(i.FLASH_Last_Operation_Wait) for FLASH_Last_Operation_Wait
    n32g401_flash.o(i.FLASH_Write_Protection_Enable) refers to n32g401_flash.o(i.Option_Bytes_Unlock) for Option_Bytes_Unlock
    n32g401_gpio.o(i.GPIO_ALLPin_Reset) refers to n32g401_gpio.o(i.GPIOA_Pin_Reset) for GPIOA_Pin_Reset
    n32g401_gpio.o(i.GPIO_ALLPin_Reset) refers to n32g401_gpio.o(i.GPIOB_Pin_Reset) for GPIOB_Pin_Reset
    n32g401_gpio.o(i.GPIO_ALLPin_Reset) refers to n32g401_gpio.o(i.GPIOC_Pin_Reset) for GPIOC_Pin_Reset
    n32g401_gpio.o(i.GPIO_ALLPin_Reset) refers to n32g401_gpio.o(i.GPIOD_Pin_Reset) for GPIOD_Pin_Reset
    n32g401_gpio.o(i.GPIO_Alternate_Function_Reset) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Reset) for RCC_APB2_Peripheral_Reset
    n32g401_gpio.o(i.GPIO_Peripheral_Initialize) refers to n32g401_gpio.o(i.GPIO_Alternate_Set) for GPIO_Alternate_Set
    n32g401_gpio.o(i.GPIO_Peripheral_Initialize) refers to n32g401_gpio.o(i.GPIO_Mode_Set) for GPIO_Mode_Set
    n32g401_gpio.o(i.GPIO_Peripheral_Initialize) refers to n32g401_gpio.o(i.GPIO_Pull_Set) for GPIO_Pull_Set
    n32g401_gpio.o(i.GPIO_Peripheral_Initialize) refers to n32g401_gpio.o(i.GPIO_SlewRate_Set) for GPIO_SlewRate_Set
    n32g401_gpio.o(i.GPIO_Peripheral_Initialize) refers to n32g401_gpio.o(i.GPIO_Driver_Set) for GPIO_Driver_Set
    n32g401_gpio.o(i.GPIO_Reset) refers to n32g401_rcc.o(i.RCC_AHB_Peripheral_Reset) for RCC_AHB_Peripheral_Reset
    n32g401_i2c.o(i.I2C_Clock_Speed_Config) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    n32g401_i2c.o(i.I2C_Initializes) refers to n32g401_i2c.o(i.I2C_Clock_Speed_Config) for I2C_Clock_Speed_Config
    n32g401_i2c.o(i.I2C_Initializes) refers to n32g401_i2c.o(i.I2C_Bus_Mode_Config) for I2C_Bus_Mode_Config
    n32g401_i2c.o(i.I2C_Initializes) refers to n32g401_i2c.o(i.I2C_Acknowledgement_Config) for I2C_Acknowledgement_Config
    n32g401_i2c.o(i.I2C_Initializes) refers to n32g401_i2c.o(i.I2C_Addressing_Mode_Config) for I2C_Addressing_Mode_Config
    n32g401_i2c.o(i.I2C_Initializes) refers to n32g401_i2c.o(i.I2C_Own_Addr1_Config) for I2C_Own_Addr1_Config
    n32g401_i2c.o(i.I2C_Reset) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Reset) for RCC_APB1_Peripheral_Reset
    n32g401_lptim.o(i.LPTIM_Initializes) refers to n32g401_lptim.o(i.LPTIM_Clock_Source_Set) for LPTIM_Clock_Source_Set
    n32g401_lptim.o(i.LPTIM_Initializes) refers to n32g401_lptim.o(i.LPTIM_Prescaler_Set) for LPTIM_Prescaler_Set
    n32g401_lptim.o(i.LPTIM_Initializes) refers to n32g401_lptim.o(i.LPTIM_Waveform_Set) for LPTIM_Waveform_Set
    n32g401_lptim.o(i.LPTIM_Initializes) refers to n32g401_lptim.o(i.LPTIM_Polarity_Set) for LPTIM_Polarity_Set
    n32g401_lptim.o(i.LPTIM_Reset) refers to n32g401_rcc.o(i.RCC_LPTIM_Reset) for RCC_LPTIM_Reset
    n32g401_pwr.o(i.PWR_Reset) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Reset) for RCC_APB1_Peripheral_Reset
    n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) refers to n32g401_rcc.o(.constdata) for APBAHBPresTable
    n32g401_rcc.o(i.RCC_HSE_Stable_Wait) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    n32g401_rcc.o(i.RCC_HSE_Stable_Wait) refers to n32g401_rcc.o(i.RCC_Flag_Status_Get) for RCC_Flag_Status_Get
    n32g401_rcc.o(i.RCC_HSI_Stable_Wait) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    n32g401_rcc.o(i.RCC_HSI_Stable_Wait) refers to n32g401_rcc.o(i.RCC_Flag_Status_Get) for RCC_Flag_Status_Get
    n32g401_rcc.o(i.RCC_LPTIM_Clock_Config) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Clock_Enable) for RCC_APB1_Peripheral_Clock_Enable
    n32g401_rcc.o(i.RCC_LSE_Config) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Clock_Enable) for RCC_APB1_Peripheral_Clock_Enable
    n32g401_rcc.o(i.RCC_LSE_Config) refers to n32g401_rcc.o(i.RCC_LSE_Trim_Config) for RCC_LSE_Trim_Config
    n32g401_rcc.o(i.RCC_LSE_Stable_Wait) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    n32g401_rcc.o(i.RCC_LSE_Stable_Wait) refers to n32g401_rcc.o(i.RCC_Flag_Status_Get) for RCC_Flag_Status_Get
    n32g401_rcc.o(i.RCC_LSI_Stable_Wait) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    n32g401_rcc.o(i.RCC_LSI_Stable_Wait) refers to n32g401_rcc.o(i.RCC_Flag_Status_Get) for RCC_Flag_Status_Get
    n32g401_rtc.o(i.RTC_Alarm_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Alarm_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Alarm_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Alarm_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Alarm_Get) refers to n32g401_rtc.o(i.RTC_Bcd2_To_Byte) for RTC_Bcd2_To_Byte
    n32g401_rtc.o(i.RTC_Alarm_Set) refers to n32g401_rtc.o(i.RTC_Byte_To_Bcd2) for RTC_Byte_To_Bcd2
    n32g401_rtc.o(i.RTC_Alarm_Set) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Alarm_Set) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Alarm_SubSecond_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Alarm_SubSecond_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Bypass_Shadow_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Bypass_Shadow_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Bypass_Shadow_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Bypass_Shadow_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(i.RTC_Byte_To_Bcd2) for RTC_Byte_To_Bcd2
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(i.RTC_Initialization_Mode_Enter) for RTC_Initialization_Mode_Enter
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(i.RTC_Hour_Format_Set) for RTC_Hour_Format_Set
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(i.RTC_Prescale_Config) for RTC_Prescale_Config
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(i.RTC_Initialization_Mode_Exit) for RTC_Initialization_Mode_Exit
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(i.RTC_Wait_For_Synchronization) for RTC_Wait_For_Synchronization
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to n32g401_rtc.o(.data) for first_init_flag
    n32g401_rtc.o(i.RTC_Calendar_Initializes) refers to system_n32g401.o(.data) for SystemClockFrequency
    n32g401_rtc.o(i.RTC_Calibration_Output_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Calibration_Output_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Calibration_Output_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Calibration_Output_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Calibration_Output_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Calibration_Output_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Date_Get) refers to n32g401_rtc.o(i.RTC_Bcd2_To_Byte) for RTC_Bcd2_To_Byte
    n32g401_rtc.o(i.RTC_Day_Light_Saving_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Day_Light_Saving_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Deinitializes) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Deinitializes) refers to n32g401_rtc.o(i.RTC_Initialization_Mode_Enter) for RTC_Initialization_Mode_Enter
    n32g401_rtc.o(i.RTC_Deinitializes) refers to n32g401_rtc.o(i.RTC_Registers_Reset) for RTC_Registers_Reset
    n32g401_rtc.o(i.RTC_Deinitializes) refers to n32g401_rtc.o(i.RTC_Wait_For_Synchronization) for RTC_Wait_For_Synchronization
    n32g401_rtc.o(i.RTC_Deinitializes) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Interrupts_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Interrupts_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Interrupts_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Interrupts_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Output_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Output_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Reference_Clock_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Reference_Clock_Disable) refers to n32g401_rtc.o(i.RTC_Initialization_Mode_Enter) for RTC_Initialization_Mode_Enter
    n32g401_rtc.o(i.RTC_Reference_Clock_Disable) refers to n32g401_rtc.o(i.RTC_Initialization_Mode_Exit) for RTC_Initialization_Mode_Exit
    n32g401_rtc.o(i.RTC_Reference_Clock_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Reference_Clock_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Reference_Clock_Enable) refers to n32g401_rtc.o(i.RTC_Initialization_Mode_Enter) for RTC_Initialization_Mode_Enter
    n32g401_rtc.o(i.RTC_Reference_Clock_Enable) refers to n32g401_rtc.o(i.RTC_Initialization_Mode_Exit) for RTC_Initialization_Mode_Exit
    n32g401_rtc.o(i.RTC_Reference_Clock_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Smooth_Calibration_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Smooth_Calibration_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_Synchronization_Shift_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Synchronization_Shift_Config) refers to n32g401_rtc.o(i.RTC_Wait_For_Synchronization) for RTC_Wait_For_Synchronization
    n32g401_rtc.o(i.RTC_Synchronization_Shift_Config) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_TimeStamp_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_TimeStamp_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_TimeStamp_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_TimeStamp_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_TimeStamp_Get) refers to n32g401_rtc.o(i.RTC_Bcd2_To_Byte) for RTC_Bcd2_To_Byte
    n32g401_rtc.o(i.RTC_Time_Get) refers to n32g401_rtc.o(i.RTC_Bcd2_To_Byte) for RTC_Bcd2_To_Byte
    n32g401_rtc.o(i.RTC_Wait_For_Synchronization) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_Wait_For_Synchronization) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_WakeUp_Clock_Select) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_WakeUp_Clock_Select) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_WakeUp_Counter_Set) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_WakeUp_Counter_Set) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_WakeUp_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_WakeUp_Disable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_rtc.o(i.RTC_WakeUp_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Disable) for RTC_Write_Protection_Disable
    n32g401_rtc.o(i.RTC_WakeUp_Enable) refers to n32g401_rtc.o(i.RTC_Write_Protection_Enable) for RTC_Write_Protection_Enable
    n32g401_spi.o(i.I2S_AudioFrequency_Config) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    n32g401_spi.o(i.I2S_Initializes) refers to n32g401_spi.o(i.I2S_Mode_Config) for I2S_Mode_Config
    n32g401_spi.o(i.I2S_Initializes) refers to n32g401_spi.o(i.I2S_Standard_Config) for I2S_Standard_Config
    n32g401_spi.o(i.I2S_Initializes) refers to n32g401_spi.o(i.I2S_DataFormat_Config) for I2S_DataFormat_Config
    n32g401_spi.o(i.I2S_Initializes) refers to n32g401_spi.o(i.I2S_MCLK_Enable) for I2S_MCLK_Enable
    n32g401_spi.o(i.I2S_Initializes) refers to n32g401_spi.o(i.I2S_MCLK_Disable) for I2S_MCLK_Disable
    n32g401_spi.o(i.I2S_Initializes) refers to n32g401_spi.o(i.I2S_AudioFrequency_Config) for I2S_AudioFrequency_Config
    n32g401_spi.o(i.I2S_Initializes) refers to n32g401_spi.o(i.I2S_CLKPOL_Config) for I2S_CLKPOL_Config
    n32g401_spi.o(i.SPI_I2S_Reset) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Reset) for RCC_APB2_Peripheral_Reset
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_DataDirection_Config) for SPI_DataDirection_Config
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_SpiMode_Config) for SPI_SpiMode_Config
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_DataLen_Config) for SPI_DataLen_Config
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_CLKPHA_Config) for SPI_CLKPHA_Config
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_CLKPOL_Config) for SPI_CLKPOL_Config
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_NSS_Config) for SPI_NSS_Config
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_BaudRatePres_Config) for SPI_BaudRatePres_Config
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_FirstBit_Config) for SPI_FirstBit_Config
    n32g401_spi.o(i.SPI_Initializes) refers to n32g401_spi.o(i.SPI_CRC_Polynomial_Set) for SPI_CRC_Polynomial_Set
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Count_Mode_Set) for TIM_Base_Count_Mode_Set
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Clock_Division_Set) for TIM_Clock_Division_Set
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Auto_Reload_Set) for TIM_Base_Auto_Reload_Set
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Prescaler_Set) for TIM_Base_Prescaler_Set
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Repeat_Count_Set) for TIM_Base_Repeat_Count_Set
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Reload_Mode_Set) for TIM_Base_Reload_Mode_Set
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Channel1) for TIM_Base_Channel1
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_OCrefClear) for TIM_Base_OCrefClear
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Channel2) for TIM_Base_Channel2
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Channel3) for TIM_Base_Channel3
    n32g401_tim.o(i.TIM_Base_Initialize) refers to n32g401_tim.o(i.TIM_Base_Channel4) for TIM_Base_Channel4
    n32g401_tim.o(i.TIM_Break_And_Dead_Time_Set) refers to n32g401_tim.o(i.TIM_IOM_Comp_Break) for TIM_IOM_Comp_Break
    n32g401_tim.o(i.TIM_Break_And_Dead_Time_Set) refers to n32g401_tim.o(i.TIM_Lock_Up_Break_Enable) for TIM_Lock_Up_Break_Enable
    n32g401_tim.o(i.TIM_Break_And_Dead_Time_Set) refers to n32g401_tim.o(i.TIM_Lock_Up_Break_Disable) for TIM_Lock_Up_Break_Disable
    n32g401_tim.o(i.TIM_Break_And_Dead_Time_Set) refers to n32g401_tim.o(i.TIM_Pvd_Break_Enable) for TIM_Pvd_Break_Enable
    n32g401_tim.o(i.TIM_Break_And_Dead_Time_Set) refers to n32g401_tim.o(i.TIM_Pvd_Break_Disable) for TIM_Pvd_Break_Disable
    n32g401_tim.o(i.TIM_External_Clock_Mode1_Set) refers to n32g401_tim.o(i.TIM_External_Trigger_Filter_Set) for TIM_External_Trigger_Filter_Set
    n32g401_tim.o(i.TIM_External_Clock_Mode1_Set) refers to n32g401_tim.o(i.TIM_External_Trigger_Polarity_Set) for TIM_External_Trigger_Polarity_Set
    n32g401_tim.o(i.TIM_External_Clock_Mode1_Set) refers to n32g401_tim.o(i.TIM_External_Trigger_Prescaler_Set) for TIM_External_Trigger_Prescaler_Set
    n32g401_tim.o(i.TIM_External_Clock_Mode2_Set) refers to n32g401_tim.o(i.TIM_External_Trigger_Filter_Set) for TIM_External_Trigger_Filter_Set
    n32g401_tim.o(i.TIM_External_Clock_Mode2_Set) refers to n32g401_tim.o(i.TIM_External_Trigger_Polarity_Set) for TIM_External_Trigger_Polarity_Set
    n32g401_tim.o(i.TIM_External_Clock_Mode2_Set) refers to n32g401_tim.o(i.TIM_External_Trigger_Prescaler_Set) for TIM_External_Trigger_Prescaler_Set
    n32g401_tim.o(i.TIM_Input_Channel_Initialize) refers to n32g401_tim.o(i.Input_Channel1_Config) for Input_Channel1_Config
    n32g401_tim.o(i.TIM_Input_Channel_Initialize) refers to n32g401_tim.o(i.TIM_Input_Capture1_Prescaler_Set) for TIM_Input_Capture1_Prescaler_Set
    n32g401_tim.o(i.TIM_Input_Channel_Initialize) refers to n32g401_tim.o(i.Input_Channel2_Config) for Input_Channel2_Config
    n32g401_tim.o(i.TIM_Input_Channel_Initialize) refers to n32g401_tim.o(i.TIM_Input_Capture2_Prescaler_Set) for TIM_Input_Capture2_Prescaler_Set
    n32g401_tim.o(i.TIM_Input_Channel_Initialize) refers to n32g401_tim.o(i.Input_Channel3_Config) for Input_Channel3_Config
    n32g401_tim.o(i.TIM_Input_Channel_Initialize) refers to n32g401_tim.o(i.TIM_Input_Capture3_Prescaler_Set) for TIM_Input_Capture3_Prescaler_Set
    n32g401_tim.o(i.TIM_Input_Channel_Initialize) refers to n32g401_tim.o(i.Input_Channel4_Config) for Input_Channel4_Config
    n32g401_tim.o(i.TIM_Input_Channel_Initialize) refers to n32g401_tim.o(i.TIM_Input_Capture4_Prescaler_Set) for TIM_Input_Capture4_Prescaler_Set
    n32g401_tim.o(i.TIM_Internal_Trig_To_Ext_Set) refers to n32g401_tim.o(i.TIM_Trigger_Source_Select) for TIM_Trigger_Source_Select
    n32g401_tim.o(i.TIM_Output_Channel1_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Disable) for TIM_Capture_Compare_Ch_Disable
    n32g401_tim.o(i.TIM_Output_Channel1_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_Polarity_Set) for TIM_Output_Channel_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel1_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Enable) for TIM_Capture_Compare_Ch_Enable
    n32g401_tim.o(i.TIM_Output_Channel1_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_N_Polarity_Set) for TIM_Output_Channel_N_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel1_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Enable) for TIM_Capture_Compare_Ch_N_Enable
    n32g401_tim.o(i.TIM_Output_Channel1_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Disable) for TIM_Capture_Compare_Ch_N_Disable
    n32g401_tim.o(i.TIM_Output_Channel2_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Disable) for TIM_Capture_Compare_Ch_Disable
    n32g401_tim.o(i.TIM_Output_Channel2_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_Polarity_Set) for TIM_Output_Channel_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel2_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Enable) for TIM_Capture_Compare_Ch_Enable
    n32g401_tim.o(i.TIM_Output_Channel2_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_N_Polarity_Set) for TIM_Output_Channel_N_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel2_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Enable) for TIM_Capture_Compare_Ch_N_Enable
    n32g401_tim.o(i.TIM_Output_Channel2_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Disable) for TIM_Capture_Compare_Ch_N_Disable
    n32g401_tim.o(i.TIM_Output_Channel3_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Disable) for TIM_Capture_Compare_Ch_Disable
    n32g401_tim.o(i.TIM_Output_Channel3_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_Polarity_Set) for TIM_Output_Channel_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel3_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Enable) for TIM_Capture_Compare_Ch_Enable
    n32g401_tim.o(i.TIM_Output_Channel3_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_N_Polarity_Set) for TIM_Output_Channel_N_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel3_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Enable) for TIM_Capture_Compare_Ch_N_Enable
    n32g401_tim.o(i.TIM_Output_Channel3_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Disable) for TIM_Capture_Compare_Ch_N_Disable
    n32g401_tim.o(i.TIM_Output_Channel4_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Disable) for TIM_Capture_Compare_Ch_Disable
    n32g401_tim.o(i.TIM_Output_Channel4_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_Polarity_Set) for TIM_Output_Channel_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel4_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Enable) for TIM_Capture_Compare_Ch_Enable
    n32g401_tim.o(i.TIM_Output_Channel4_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_N_Polarity_Set) for TIM_Output_Channel_N_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel4_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Enable) for TIM_Capture_Compare_Ch_N_Enable
    n32g401_tim.o(i.TIM_Output_Channel4_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Disable) for TIM_Capture_Compare_Ch_N_Disable
    n32g401_tim.o(i.TIM_Output_Channel5_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Disable) for TIM_Capture_Compare_Ch_Disable
    n32g401_tim.o(i.TIM_Output_Channel5_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_Polarity_Set) for TIM_Output_Channel_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel5_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Enable) for TIM_Capture_Compare_Ch_Enable
    n32g401_tim.o(i.TIM_Output_Channel6_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Disable) for TIM_Capture_Compare_Ch_Disable
    n32g401_tim.o(i.TIM_Output_Channel6_Initialize) refers to n32g401_tim.o(i.TIM_Output_Channel_Polarity_Set) for TIM_Output_Channel_Polarity_Set
    n32g401_tim.o(i.TIM_Output_Channel6_Initialize) refers to n32g401_tim.o(i.TIM_Capture_Compare_Ch_Enable) for TIM_Capture_Compare_Ch_Enable
    n32g401_tim.o(i.TIM_PWM_Input_Channel_Config) refers to n32g401_tim.o(i.Input_Channel1_Config) for Input_Channel1_Config
    n32g401_tim.o(i.TIM_PWM_Input_Channel_Config) refers to n32g401_tim.o(i.TIM_Input_Capture1_Prescaler_Set) for TIM_Input_Capture1_Prescaler_Set
    n32g401_tim.o(i.TIM_PWM_Input_Channel_Config) refers to n32g401_tim.o(i.Input_Channel2_Config) for Input_Channel2_Config
    n32g401_tim.o(i.TIM_PWM_Input_Channel_Config) refers to n32g401_tim.o(i.TIM_Input_Capture2_Prescaler_Set) for TIM_Input_Capture2_Prescaler_Set
    n32g401_tim.o(i.TIM_Reset) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Reset) for RCC_APB2_Peripheral_Reset
    n32g401_tim.o(i.TIM_Reset) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Reset) for RCC_APB1_Peripheral_Reset
    n32g401_tim.o(i.TIM_Trigger_As_External_Clock) refers to n32g401_tim.o(i.Input_Channel2_Config) for Input_Channel2_Config
    n32g401_tim.o(i.TIM_Trigger_As_External_Clock) refers to n32g401_tim.o(i.Input_Channel1_Config) for Input_Channel1_Config
    n32g401_tim.o(i.TIM_Trigger_As_External_Clock) refers to n32g401_tim.o(i.TIM_Trigger_Source_Select) for TIM_Trigger_Source_Select
    n32g401_usart.o(i.USART_Baud_Rate_Config) refers to n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get) for RCC_Clocks_Frequencies_Value_Get
    n32g401_usart.o(i.USART_Clock_Initializes) refers to n32g401_usart.o(i.USART_Clock_Config) for USART_Clock_Config
    n32g401_usart.o(i.USART_Clock_Initializes) refers to n32g401_usart.o(i.USART_Polarity_Config) for USART_Polarity_Config
    n32g401_usart.o(i.USART_Clock_Initializes) refers to n32g401_usart.o(i.USART_Phase_Config) for USART_Phase_Config
    n32g401_usart.o(i.USART_Clock_Initializes) refers to n32g401_usart.o(i.USART_Last_Bit_Config) for USART_Last_Bit_Config
    n32g401_usart.o(i.USART_Initializes) refers to n32g401_usart.o(i.USART_Baud_Rate_Config) for USART_Baud_Rate_Config
    n32g401_usart.o(i.USART_Initializes) refers to n32g401_usart.o(i.USART_Word_Length_Config) for USART_Word_Length_Config
    n32g401_usart.o(i.USART_Initializes) refers to n32g401_usart.o(i.USART_Stop_Bits_Config) for USART_Stop_Bits_Config
    n32g401_usart.o(i.USART_Initializes) refers to n32g401_usart.o(i.USART_Parity_Config) for USART_Parity_Config
    n32g401_usart.o(i.USART_Initializes) refers to n32g401_usart.o(i.USART_Mode_Config) for USART_Mode_Config
    n32g401_usart.o(i.USART_Initializes) refers to n32g401_usart.o(i.USART_Hardware_Flow_Control_Config) for USART_Hardware_Flow_Control_Config
    n32g401_usart.o(i.USART_Reset) refers to n32g401_rcc.o(i.RCC_APB2_Peripheral_Reset) for RCC_APB2_Peripheral_Reset
    n32g401_usart.o(i.USART_Reset) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Reset) for RCC_APB1_Peripheral_Reset
    n32g401_wwdg.o(i.WWDG_Reset) refers to n32g401_rcc.o(i.RCC_APB1_Peripheral_Reset) for RCC_APB1_Peripheral_Reset
    proj_version.o(i.get_proj_version) refers to memcpya.o(.text) for __aeabi_memcpy
    proj_version.o(i.get_proj_version) refers to proj_version.o(.constdata) for sst_proj_version
    VI4302_Handle.o(i.VI4302_AllInit) refers to spi.o(i.SPI1_Init) for SPI1_Init
    VI4302_Handle.o(i.VI4302_AllInit) refers to uartfifo.o(i.VI4302_InfifoClear) for VI4302_InfifoClear
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_Handle.o(i.user_driver_init) for user_driver_init
    VI4302_Handle.o(i.VI4302_AllInit) refers to delay.o(i.delay_ms) for delay_ms
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Pin_Init) for VI4302_Pin_Init
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Enable_DcDc) for VI4302_Enable_DcDc
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Reg_Init) for VI4302_Reg_Init
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_BVD_Calculate) for VI4302_BVD_Calculate
    VI4302_Handle.o(i.VI4302_AllInit) refers to fw_44_00_00_80_R00.o(i.Get_Fw_bytes) for Get_Fw_bytes
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Update_Config) for VI4302_Update_Config
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Update_firmware) for VI4302_Update_firmware
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Update_Succcess_Fail) for VI4302_Update_Succcess_Fail
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_TDC_Cal) for VI4302_TDC_Cal
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Bvd_Cal) for VI4302_Bvd_Cal
    VI4302_Handle.o(i.VI4302_AllInit) refers to adc.o(i.NTC_TempGet) for NTC_TempGet
    VI4302_Handle.o(i.VI4302_AllInit) refers to flash.o(i.flash_SaveFmData) for flash_SaveFmData
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Write_Reg) for VI4302_Write_Reg
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.Adaptation_REG_and_ALGO) for Adaptation_REG_and_ALGO
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(i.VI4302_Frame_Rate_AutoCtrl) for VI4302_Frame_Rate_AutoCtrl
    VI4302_Handle.o(i.VI4302_AllInit) refers to GPIO.o(i.TX_En) for TX_En
    VI4302_Handle.o(i.VI4302_AllInit) refers to cmd_handle.o(.bss) for g_ControlPara
    VI4302_Handle.o(i.VI4302_AllInit) refers to fw_44_00_00_80_R00.o(.constdata) for vis_sensor_fw
    VI4302_Handle.o(i.VI4302_AllInit) refers to flash.o(.bss) for g_fmNeedSaved
    VI4302_Handle.o(i.VI4302_AllInit) refers to VI4302_System.o(.data) for Bvd_Cal
    VI4302_Handle.o(i.VI4302_AllInit) refers to cmd_handle.o(.data) for opt_code
    VI4302_Handle.o(i.spi_write_data) refers to spi.o(i.VI4302_CsLow) for VI4302_CsLow
    VI4302_Handle.o(i.spi_write_data) refers to spi.o(i.SPI1_WriteNByte) for SPI1_WriteNByte
    VI4302_Handle.o(i.spi_write_data) refers to spi.o(i.VI4302_CsHigh) for VI4302_CsHigh
    VI4302_Handle.o(i.user_driver_init) refers to VI4302_Handle.o(i.vi4302_HW_set_demo) for vi4302_HW_set_demo
    VI4302_Handle.o(i.user_driver_init) refers to User_Driver.o(.data) for Chip_En
    VI4302_Handle.o(i.user_driver_init) refers to VI4302_Handle.o(i.vi4302_read_register) for vi4302_read_register
    VI4302_Handle.o(i.user_driver_init) refers to VI4302_Handle.o(i.vi4302_write_register) for vi4302_write_register
    VI4302_Handle.o(i.user_driver_init) refers to VI4302_Handle.o(i.vi4302_read_mul_reg) for vi4302_read_mul_reg
    VI4302_Handle.o(i.user_driver_init) refers to VI4302_Handle.o(i.vi4302_write_cmd) for vi4302_write_cmd
    VI4302_Handle.o(i.user_driver_init) refers to VI4302_Handle.o(i.spi_write_data) for spi_write_data
    VI4302_Handle.o(i.vi4302_HW_set_demo) refers to GPIO.o(i.VI4302_Disen) for VI4302_Disen
    VI4302_Handle.o(i.vi4302_HW_set_demo) refers to delay.o(i.delay_ms) for delay_ms
    VI4302_Handle.o(i.vi4302_HW_set_demo) refers to GPIO.o(i.VI4302_En) for VI4302_En
    VI4302_Handle.o(i.vi4302_read_his_reg) refers to spi.o(i.VI4302_CsLow) for VI4302_CsLow
    VI4302_Handle.o(i.vi4302_read_his_reg) refers to spi.o(i.SPI1_WriteNByte) for SPI1_WriteNByte
    VI4302_Handle.o(i.vi4302_read_his_reg) refers to spi.o(i.SPI1_ReadNByte) for SPI1_ReadNByte
    VI4302_Handle.o(i.vi4302_read_his_reg) refers to spi.o(i.VI4302_CsHigh) for VI4302_CsHigh
    VI4302_Handle.o(i.vi4302_read_mul_reg) refers to spi.o(i.VI4302_CsLow) for VI4302_CsLow
    VI4302_Handle.o(i.vi4302_read_mul_reg) refers to spi.o(i.SPI1_WriteNByte) for SPI1_WriteNByte
    VI4302_Handle.o(i.vi4302_read_mul_reg) refers to spi.o(i.SPI1_ReadNByte) for SPI1_ReadNByte
    VI4302_Handle.o(i.vi4302_read_mul_reg) refers to spi.o(i.VI4302_CsHigh) for VI4302_CsHigh
    VI4302_Handle.o(i.vi4302_read_ranging_data_with_firmware) refers to spi.o(i.VI4302_CsLow) for VI4302_CsLow
    VI4302_Handle.o(i.vi4302_read_ranging_data_with_firmware) refers to spi.o(i.SPI1_WriteNByte) for SPI1_WriteNByte
    VI4302_Handle.o(i.vi4302_read_ranging_data_with_firmware) refers to spi.o(i.SPI1_ReadNByte) for SPI1_ReadNByte
    VI4302_Handle.o(i.vi4302_read_ranging_data_with_firmware) refers to spi.o(i.VI4302_CsHigh) for VI4302_CsHigh
    VI4302_Handle.o(i.vi4302_read_register) refers to spi.o(i.VI4302_CsLow) for VI4302_CsLow
    VI4302_Handle.o(i.vi4302_read_register) refers to spi.o(i.SPI1_ReadWriteNByte) for SPI1_ReadWriteNByte
    VI4302_Handle.o(i.vi4302_read_register) refers to spi.o(i.VI4302_CsHigh) for VI4302_CsHigh
    VI4302_Handle.o(i.vi4302_write_cmd) refers to spi.o(i.VI4302_CsLow) for VI4302_CsLow
    VI4302_Handle.o(i.vi4302_write_cmd) refers to spi.o(i.SPI1_WriteNByte) for SPI1_WriteNByte
    VI4302_Handle.o(i.vi4302_write_cmd) refers to spi.o(i.VI4302_CsHigh) for VI4302_CsHigh
    VI4302_Handle.o(i.vi4302_write_register) refers to spi.o(i.VI4302_CsLow) for VI4302_CsLow
    VI4302_Handle.o(i.vi4302_write_register) refers to spi.o(i.SPI1_ReadWriteNByte) for SPI1_ReadWriteNByte
    VI4302_Handle.o(i.vi4302_write_register) refers to spi.o(i.VI4302_CsHigh) for VI4302_CsHigh
    VI4302_Handle.o(i.vi4302_write_register) refers to VI4302_Handle.o(.constdata) for .constdata
    VI4302_System.o(i.Adaptation_REG_and_ALGO) refers to dfltui.o(.text) for __aeabi_ui2d
    VI4302_System.o(i.Adaptation_REG_and_ALGO) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    VI4302_System.o(i.Adaptation_REG_and_ALGO) refers to dfixui.o(.text) for __aeabi_d2uiz
    VI4302_System.o(i.Adaptation_REG_and_ALGO) refers to User_Driver.o(.data) for Spi_Read_Reg
    VI4302_System.o(i.Search_logic_init) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.Search_logic_init) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_BVD_Calculate) refers to VI4302_System.o(i.VI4302_Set_Bvd) for VI4302_Set_Bvd
    VI4302_System.o(i.VI4302_BVD_Calculate) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Bvd_Cal) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_Bvd_Cal) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Enable_DcDc) refers to User_Driver.o(.data) for Spi_Read_Reg
    VI4302_System.o(i.VI4302_Frame_Rate_AutoCtrl) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_Frame_Rate_AutoCtrl) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Frame_Rate_Config) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_Frame_Rate_Config) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Frame_Rate_Config) refers to VI4302_System.o(.data) for VSM_e
    VI4302_System.o(i.VI4302_Get_Frame_Cnt) refers to User_Driver.o(.data) for Spi_Read_Reg
    VI4302_System.o(i.VI4302_Pin_Init) refers to User_Driver.o(.data) for Chip_En
    VI4302_System.o(i.VI4302_Read_His_Config) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_Read_His_Config) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Read_His_Config) refers to VI4302_System.o(.data) for VSM_e
    VI4302_System.o(i.VI4302_Read_Histogram) refers to VI4302_Handle.o(i.vi4302_read_his_reg) for vi4302_read_his_reg
    VI4302_System.o(i.VI4302_Read_Histogram) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Read_Reg) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_Read_Reg) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Reg_Init) refers to delay.o(i.delay_ms) for delay_ms
    VI4302_System.o(i.VI4302_Reg_Init) refers to VI4302_System.o(i.VI4302_Write_Reg) for VI4302_Write_Reg
    VI4302_System.o(i.VI4302_Reg_Init) refers to VI4302_Config.o(.constdata) for S_regist_buffer_default
    VI4302_System.o(i.VI4302_Reg_Init) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Set_Bvd) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Set_Frame_Data_Format) refers to User_Driver.o(.data) for Spi_Read_Reg
    VI4302_System.o(i.VI4302_Set_Mp_All) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Set_Mp_Openonly) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to memseta.o(.text) for __aeabi_memclr4
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to VI4302_System.o(i.VI4302_Stream_On) for VI4302_Stream_On
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to VI4302_System.o(i.VI4302_Set_Frame_Data_Format) for VI4302_Set_Frame_Data_Format
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to VI4302_System.o(i.VI4302_Set_Mp_Openonly) for VI4302_Set_Mp_Openonly
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to VI4302_System.o(i.VI4302_Get_Frame_Cnt) for VI4302_Get_Frame_Cnt
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to VI4302_System.o(i.VI4302_read_frame) for VI4302_read_frame
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to VI4302_System.o(i.VI4302_Set_Mp_All) for VI4302_Set_Mp_All
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_SinglePixel_Output) refers to VI4302_Handle.o(.data) for EXTI_FLAG
    VI4302_System.o(i.VI4302_Start_Ranging) refers to VI4302_System.o(i.VI4302_Stop_Ranging) for VI4302_Stop_Ranging
    VI4302_System.o(i.VI4302_Start_Ranging) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_Start_Ranging) refers to delay.o(i.delay_ms) for delay_ms
    VI4302_System.o(i.VI4302_Start_Ranging) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Start_Ranging) refers to VI4302_System.o(.data) for VSM_e
    VI4302_System.o(i.VI4302_Start_Ranging) refers to cmd_handle.o(.bss) for g_ControlPara
    VI4302_System.o(i.VI4302_Stop_Ranging) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_Stop_Ranging) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Stop_Ranging) refers to VI4302_System.o(.data) for VSM_e
    VI4302_System.o(i.VI4302_Stop_Ranging) refers to cmd_handle.o(.bss) for g_ControlPara
    VI4302_System.o(i.VI4302_Stream_On) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_TDC_Cal) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_TDC_Cal) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Temp_Bvd) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Update_Config) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_Update_Succcess_Fail) refers to User_Driver.o(.data) for Spi_Read_Reg
    VI4302_System.o(i.VI4302_Update_firmware) refers to malloc.o(i.malloc) for malloc
    VI4302_System.o(i.VI4302_Update_firmware) refers to memcpya.o(.text) for __aeabi_memcpy
    VI4302_System.o(i.VI4302_Update_firmware) refers to memseta.o(.text) for __aeabi_memclr
    VI4302_System.o(i.VI4302_Update_firmware) refers to wdt.o(i.WDT_Week) for WDT_Week
    VI4302_System.o(i.VI4302_Update_firmware) refers to malloc.o(i.free) for free
    VI4302_System.o(i.VI4302_Update_firmware) refers to User_Driver.o(.data) for Spi_Write_Mul_Reg
    VI4302_System.o(i.VI4302_Write_Reg) refers to VI4302_System.o(i.vi4302_read_status_from_firmware) for vi4302_read_status_from_firmware
    VI4302_System.o(i.VI4302_Write_Reg) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.VI4302_read_frame) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.vi4302_read_otp) refers to User_Driver.o(.data) for Spi_Write_Reg
    VI4302_System.o(i.vi4302_read_status_from_firmware) refers to delay.o(i.delay_ms) for delay_ms
    VI4302_System.o(i.vi4302_read_status_from_firmware) refers to User_Driver.o(.data) for Spi_Read_Reg
    data_handle.o(i.Get_Temperature_Rectified_Tof) refers to flash.o(.bss) for g_fmNeedSaved
    data_handle.o(i.Refresh_Tof_With_MidFilter) refers to data_handle.o(i.Get_Mid_Position) for Get_Mid_Position
    data_handle.o(i.Refresh_Tof_With_MidFilter) refers to data_handle.o(.bss) for data_temp_buf
    data_handle.o(i.cal_final_tof) refers to data_handle.o(i.Get_Temperature_Rectified_Tof) for Get_Temperature_Rectified_Tof
    data_handle.o(i.cal_final_tof) refers to data_handle.o(i.vis_tof_compensation) for vis_tof_compensation
    data_handle.o(i.cal_final_tof) refers to VI4302_System.o(.data) for Al_Pa
    data_handle.o(i.vis_tof_compensation) refers to dflti.o(.text) for __aeabi_i2d
    data_handle.o(i.vis_tof_compensation) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    data_handle.o(i.vis_tof_compensation) refers to dadd.o(.text) for __aeabi_dadd
    data_handle.o(i.vis_tof_compensation) refers to dfixi.o(.text) for __aeabi_d2iz
    data_handle.o(i.vis_tof_compensation) refers to flash.o(.bss) for g_fmNeedSaved
    cmd_handle.o(i.ALL_ParaInit) refers to cmd_handle.o(.bss) for g_ControlPara
    cmd_handle.o(i.Execute_instruction) refers to memseta.o(.text) for __aeabi_memclr4
    cmd_handle.o(i.Execute_instruction) refers to usart.o(i.UART2_SendData) for UART2_SendData
    cmd_handle.o(i.Execute_instruction) refers to VI4302_System.o(i.VI4302_Read_Histogram) for VI4302_Read_Histogram
    cmd_handle.o(i.Execute_instruction) refers to adc.o(i.NTC_TempGet) for NTC_TempGet
    cmd_handle.o(i.Execute_instruction) refers to wdt.o(i.WDT_Week) for WDT_Week
    cmd_handle.o(i.Execute_instruction) refers to VI4302_Handle.o(i.vi4302_read_ranging_data_with_firmware) for vi4302_read_ranging_data_with_firmware
    cmd_handle.o(i.Execute_instruction) refers to VI4302_System.o(i.VI4302_Temp_Bvd) for VI4302_Temp_Bvd
    cmd_handle.o(i.Execute_instruction) refers to data_handle.o(i.Noise_To_Confidence) for Noise_To_Confidence
    cmd_handle.o(i.Execute_instruction) refers to data_handle.o(i.cal_final_tof) for cal_final_tof
    cmd_handle.o(i.Execute_instruction) refers to uartfifo.o(i.VI4302_InfifoDataIn) for VI4302_InfifoDataIn
    cmd_handle.o(i.Execute_instruction) refers to uartfifo.o(i.VI4302_InfifoDataOut) for VI4302_InfifoDataOut
    cmd_handle.o(i.Execute_instruction) refers to cmd_handle.o(i.UART_CmdAckSend) for UART_CmdAckSend
    cmd_handle.o(i.Execute_instruction) refers to cmd_handle.o(.data) for opt_code
    cmd_handle.o(i.Execute_instruction) refers to cmd_handle.o(.constdata) for .constdata
    cmd_handle.o(i.Execute_instruction) refers to VI4302_Handle.o(.data) for gpio0_int_cnt
    cmd_handle.o(i.Execute_instruction) refers to usart.o(.data) for UART2_SendFlag
    cmd_handle.o(i.Execute_instruction) refers to cmd_handle.o(.bss) for g_ControlPara
    cmd_handle.o(i.Execute_instruction) refers to VI4302_System.o(.data) for Bvd_Cal
    cmd_handle.o(i.Execute_instruction) refers to data_handle.o(.data) for MuL_TOF
    cmd_handle.o(i.Execute_instruction) refers to flash.o(.bss) for g_dataNeedSaved
    cmd_handle.o(i.HEART_AutoSend) refers to cmd_handle.o(i.YC_UART_CmdAckSend) for YC_UART_CmdAckSend
    cmd_handle.o(i.UART_AllHandle) refers to memseta.o(.text) for __aeabi_memclr4
    cmd_handle.o(i.UART_AllHandle) refers to uartfifo.o(i.uart1infifo_HaveData) for uart1infifo_HaveData
    cmd_handle.o(i.UART_AllHandle) refers to uartfifo.o(i.uart1infifo_DataOut) for uart1infifo_DataOut
    cmd_handle.o(i.UART_AllHandle) refers to wdt.o(i.WDT_Week) for WDT_Week
    cmd_handle.o(i.UART_AllHandle) refers to cmd_handle.o(i.XSJ_UART_AllHandle) for XSJ_UART_AllHandle
    cmd_handle.o(i.UART_CmdAckSend) refers to memseta.o(.text) for __aeabi_memclr4
    cmd_handle.o(i.UART_CmdAckSend) refers to cmd_handle.o(i.CMD_SumNegateCheck) for CMD_SumNegateCheck
    cmd_handle.o(i.UART_CmdAckSend) refers to usart.o(i.UART2_SendData) for UART2_SendData
    cmd_handle.o(i.UART_CmdAckSend) refers to cmd_handle.o(.constdata) for .constdata
    cmd_handle.o(i.XSJ_DealProtocol) refers to memseta.o(.text) for __aeabi_memclr4
    cmd_handle.o(i.XSJ_DealProtocol) refers to GPIO.o(i.TX_Disen) for TX_Disen
    cmd_handle.o(i.XSJ_DealProtocol) refers to VI4302_Handle.o(i.vi4302_HW_set_demo) for vi4302_HW_set_demo
    cmd_handle.o(i.XSJ_DealProtocol) refers to VI4302_Handle.o(i.VI4302_AllInit) for VI4302_AllInit
    cmd_handle.o(i.XSJ_DealProtocol) refers to VI4302_System.o(i.VI4302_SinglePixel_Output) for VI4302_SinglePixel_Output
    cmd_handle.o(i.XSJ_DealProtocol) refers to cmd_handle.o(i.UART_CmdAckSend) for UART_CmdAckSend
    cmd_handle.o(i.XSJ_DealProtocol) refers to VI4302_Handle.o(i.vi4302_read_register) for vi4302_read_register
    cmd_handle.o(i.XSJ_DealProtocol) refers to VI4302_Handle.o(i.vi4302_write_register) for vi4302_write_register
    cmd_handle.o(i.XSJ_DealProtocol) refers to VI4302_System.o(i.VI4302_Read_His_Config) for VI4302_Read_His_Config
    cmd_handle.o(i.XSJ_DealProtocol) refers to VI4302_System.o(i.VI4302_Start_Ranging) for VI4302_Start_Ranging
    cmd_handle.o(i.XSJ_DealProtocol) refers to VI4302_System.o(i.VI4302_Stop_Ranging) for VI4302_Stop_Ranging
    cmd_handle.o(i.XSJ_DealProtocol) refers to cmd_handle.o(.data) for opt_code
    cmd_handle.o(i.XSJ_DealProtocol) refers to cmd_handle.o(.bss) for g_ControlPara
    cmd_handle.o(i.XSJ_UART_AllHandle) refers to memseta.o(.text) for __aeabi_memclr4
    cmd_handle.o(i.XSJ_UART_AllHandle) refers to cmd_handle.o(i.XSJ_DealProtocol) for XSJ_DealProtocol
    cmd_handle.o(i.YC_DealProtocol) refers to memseta.o(.text) for __aeabi_memclr4
    cmd_handle.o(i.YC_DealProtocol) refers to cmd_handle.o(i.CMD_SumCheck) for CMD_SumCheck
    cmd_handle.o(i.YC_DealProtocol) refers to cmd_handle.o(i.YC_UART_CmdAckSend) for YC_UART_CmdAckSend
    cmd_handle.o(i.YC_DealProtocol) refers to GPIO.o(i.TX_Disen) for TX_Disen
    cmd_handle.o(i.YC_DealProtocol) refers to VI4302_Handle.o(i.vi4302_HW_set_demo) for vi4302_HW_set_demo
    cmd_handle.o(i.YC_DealProtocol) refers to VI4302_Handle.o(i.VI4302_AllInit) for VI4302_AllInit
    cmd_handle.o(i.YC_DealProtocol) refers to VI4302_System.o(i.VI4302_Start_Ranging) for VI4302_Start_Ranging
    cmd_handle.o(i.YC_DealProtocol) refers to flash.o(i.flash_SaveFmData) for flash_SaveFmData
    cmd_handle.o(i.YC_DealProtocol) refers to flash.o(i.flash_ReadFmData) for flash_ReadFmData
    cmd_handle.o(i.YC_DealProtocol) refers to cmd_handle.o(.constdata) for BANBEN_NumberBuff
    cmd_handle.o(i.YC_DealProtocol) refers to cmd_handle.o(.bss) for g_ControlPara
    cmd_handle.o(i.YC_DealProtocol) refers to flash.o(.bss) for g_fmNeedSaved
    cmd_handle.o(i.YC_DealProtocol) refers to VI4302_System.o(i.VI4302_SinglePixel_Output) for VI4302_SinglePixel_Output
    cmd_handle.o(i.YC_DealProtocol) refers to flash.o(i.flash_SaveSnData) for flash_SaveSnData
    cmd_handle.o(i.YC_DealProtocol) refers to delay.o(i.delay_ms) for delay_ms
    cmd_handle.o(i.YC_DealProtocol) refers to cmd_handle.o(i.SYSTERM_Init) for SYSTERM_Init
    cmd_handle.o(i.YC_DealProtocol) refers to flash.o(i.flash_SaveConfigerData) for flash_SaveConfigerData
    cmd_handle.o(i.YC_UART_AllHandle) refers to memseta.o(.text) for __aeabi_memclr4
    cmd_handle.o(i.YC_UART_AllHandle) refers to cmd_handle.o(i.YC_DealProtocol) for YC_DealProtocol
    cmd_handle.o(i.YC_UART_CmdAckSend) refers to memseta.o(.text) for __aeabi_memclr4
    cmd_handle.o(i.YC_UART_CmdAckSend) refers to cmd_handle.o(i.CMD_SumCheck) for CMD_SumCheck
    cmd_handle.o(i.YC_UART_CmdAckSend) refers to usart.o(i.UART2_SendData) for UART2_SendData
    cmd_handle.o(i.YC_UART_CmdAckSend) refers to cmd_handle.o(.constdata) for .constdata
    cmd_handle.o(i.host16s_to_be) refers to cmd_handle.o(i.host16u_to_be) for host16u_to_be
    cmd_handle.o(i.host16s_to_le) refers to cmd_handle.o(i.host16u_to_le) for host16u_to_le
    cmd_handle.o(i.host32s_to_be) refers to cmd_handle.o(i.host32u_to_be) for host32u_to_be
    cmd_handle.o(i.host32s_to_le) refers to cmd_handle.o(i.host32u_to_le) for host32u_to_le
    uartfifo.o(i.VI4302_InfifoClear) refers to uartfifo.o(.data) for vi4302_infifo_flag
    uartfifo.o(i.VI4302_InfifoDataIn) refers to uartfifo.o(.data) for vi4302_infifo_buffer
    uartfifo.o(i.VI4302_InfifoDataOut) refers to uartfifo.o(.data) for vi4302_infifo_flag
    uartfifo.o(i.uart1infifo_Clear) refers to uartfifo.o(.data) for uart1infifo_front
    uartfifo.o(i.uart1infifo_DataIn) refers to uartfifo.o(.data) for uart1infifo_rear
    uartfifo.o(i.uart1infifo_DataIn) refers to uartfifo.o(.bss) for uart1infifo_buffer
    uartfifo.o(i.uart1infifo_DataOut) refers to uartfifo.o(.data) for uart1infifo_front
    uartfifo.o(i.uart1infifo_DataOut) refers to uartfifo.o(.bss) for uart1infifo_buffer
    uartfifo.o(i.uart1infifo_HaveData) refers to uartfifo.o(.data) for uart1infifo_rear
    uartfifo.o(i.uart1outfifo_Clear) refers to uartfifo.o(.data) for uart1outfifo_front
    uartfifo.o(i.uart1outfifo_DataIn) refers to uartfifo.o(.data) for uart1outfifo_rear
    uartfifo.o(i.uart1outfifo_DataIn) refers to uartfifo.o(.bss) for uart1outfifo_buffer
    uartfifo.o(i.uart1outfifo_DataOut) refers to uartfifo.o(.data) for uart1outfifo_front
    uartfifo.o(i.uart1outfifo_DataOut) refers to uartfifo.o(.bss) for uart1outfifo_buffer
    uartfifo.o(i.uart1outfifo_HaveData) refers to uartfifo.o(.data) for uart1outfifo_rear
    main.o(i.EXTI0_IRQHandler) refers to n32g401_exti.o(i.EXTI_Interrupt_Status_Get) for EXTI_Interrupt_Status_Get
    main.o(i.EXTI0_IRQHandler) refers to n32g401_exti.o(i.EXTI_Interrupt_Status_Clear) for EXTI_Interrupt_Status_Clear
    main.o(i.EXTI0_IRQHandler) refers to VI4302_Handle.o(.data) for gpio0_int_cnt
    main.o(i.EXTI0_IRQHandler) refers to cmd_handle.o(.bss) for g_ControlPara
    main.o(i.EXTI2_IRQHandler) refers to n32g401_exti.o(i.EXTI_Interrupt_Status_Get) for EXTI_Interrupt_Status_Get
    main.o(i.EXTI2_IRQHandler) refers to n32g401_exti.o(i.EXTI_Interrupt_Status_Clear) for EXTI_Interrupt_Status_Clear
    main.o(i.EXTI2_IRQHandler) refers to VI4302_Handle.o(.data) for EXTI_FLAG
    main.o(i.TIM6_IRQHandler) refers to n32g401_tim.o(i.TIM_Interrupt_Status_Get) for TIM_Interrupt_Status_Get
    main.o(i.TIM6_IRQHandler) refers to n32g401_tim.o(i.TIM_Interrupt_Status_Clear) for TIM_Interrupt_Status_Clear
    main.o(i.TIM6_IRQHandler) refers to main.o(.data) for ms_count
    main.o(i.main) refers to misc.o(i.NVIC_Priority_Group_Set) for NVIC_Priority_Group_Set
    main.o(i.main) refers to wdt.o(i.WDT_Init) for WDT_Init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to GPIO.o(i.RCO_OutputInit) for RCO_OutputInit
    main.o(i.main) refers to flash.o(i.flash_DataInit) for flash_DataInit
    main.o(i.main) refers to flash.o(i.flash_SnDataInit) for flash_SnDataInit
    main.o(i.main) refers to flash.o(i.flash_FmDataInit) for flash_FmDataInit
    main.o(i.main) refers to cmd_handle.o(i.ALL_ParaInit) for ALL_ParaInit
    main.o(i.main) refers to usart.o(i.USART2_Init) for USART2_Init
    main.o(i.main) refers to adc.o(i.ADC_AllInit) for ADC_AllInit
    main.o(i.main) refers to GPIO.o(i.GPIO_Init) for GPIO_Init
    main.o(i.main) refers to VI4302_Handle.o(i.VI4302_AllInit) for VI4302_AllInit
    main.o(i.main) refers to VI4302_System.o(i.VI4302_Start_Ranging) for VI4302_Start_Ranging
    main.o(i.main) refers to timer.o(i.TIM6_Configuration) for TIM6_Configuration
    main.o(i.main) refers to cmd_handle.o(i.UART_AllHandle) for UART_AllHandle
    main.o(i.main) refers to cmd_handle.o(i.Execute_instruction) for Execute_instruction
    main.o(i.main) refers to cmd_handle.o(.bss) for g_ControlPara
    n32g401_it.o(i.NMI_Handler) refers to n32g401_rcc.o(i.RCC_Interrupt_Status_Get) for RCC_Interrupt_Status_Get
    n32g401_it.o(i.NMI_Handler) refers to n32g401_rcc.o(i.RCC_HSE_Config) for RCC_HSE_Config
    n32g401_it.o(i.NMI_Handler) refers to n32g401_rcc.o(i.RCC_Interrupt_Enable) for RCC_Interrupt_Enable
    n32g401_it.o(i.NMI_Handler) refers to n32g401_rcc.o(i.RCC_Interrupt_Status_Clear) for RCC_Interrupt_Status_Clear
    n32g401_it.o(i.RCC_IRQHandler) refers to n32g401_rcc.o(i.RCC_Interrupt_Status_Get) for RCC_Interrupt_Status_Get
    n32g401_it.o(i.RCC_IRQHandler) refers to n32g401_rcc.o(i.RCC_Interrupt_Status_Clear) for RCC_Interrupt_Status_Clear
    n32g401_it.o(i.RCC_IRQHandler) refers to n32g401_rcc.o(i.RCC_Flag_Status_Get) for RCC_Flag_Status_Get
    n32g401_it.o(i.RCC_IRQHandler) refers to n32g401_rcc.o(i.RCC_PLL_Enable) for RCC_PLL_Enable
    n32g401_it.o(i.RCC_IRQHandler) refers to n32g401_rcc.o(i.RCC_Sysclk_Config) for RCC_Sysclk_Config
    floor.o(i.__hardfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__hardfp_floor) refers to dadd.o(.text) for __aeabi_dadd
    floor.o(i.__hardfp_floor) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    floor.o(i.__softfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    floor.o(i.floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.floor) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_n32g401.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_n32g401.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_n32g401.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_n32g401.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_n32g401.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_n32g401.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing GPIO.o(.rev16_text), (4 bytes).
    Removing GPIO.o(.revsh_text), (4 bytes).
    Removing GPIO.o(.rrx_text), (6 bytes).
    Removing GPIO.o(i.SetSysClockToPLL), (152 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(i.HAL_Timer_Delay_ms), (12 bytes).
    Removing delay.o(i.HAL_Timer_Delay_us), (12 bytes).
    Removing delay.o(i.delay_us), (76 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(.rrx_text), (6 bytes).
    Removing flash.o(i.STMFLASH_ReadHalfWord), (6 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing timer.o(.rrx_text), (6 bytes).
    Removing timer.o(.data), (2 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.fputc), (26 bytes).
    Removing wdt.o(.rev16_text), (4 bytes).
    Removing wdt.o(.revsh_text), (4 bytes).
    Removing wdt.o(.rrx_text), (6 bytes).
    Removing system_n32g401.o(.rev16_text), (4 bytes).
    Removing system_n32g401.o(.revsh_text), (4 bytes).
    Removing system_n32g401.o(.rrx_text), (6 bytes).
    Removing system_n32g401.o(i.System_Clock_Frequency_Update), (212 bytes).
    Removing system_n32g401.o(.constdata), (16 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_System_LowPower_Enable), (16 bytes).
    Removing misc.o(i.NVIC_System_Low_Power_Disable), (16 bytes).
    Removing misc.o(i.NVIC_Vector_Table_Set), (20 bytes).
    Removing misc.o(i.SysTick_Clock_Source_Set), (40 bytes).
    Removing n32g401_adc.o(.rev16_text), (4 bytes).
    Removing n32g401_adc.o(.revsh_text), (4 bytes).
    Removing n32g401_adc.o(.rrx_text), (6 bytes).
    Removing n32g401_adc.o(i.ADC_Analog_Watchdog_Disable), (16 bytes).
    Removing n32g401_adc.o(i.ADC_Analog_Watchdog_Enable), (16 bytes).
    Removing n32g401_adc.o(i.ADC_Analog_Watchdog_HighThresholds_Config), (12 bytes).
    Removing n32g401_adc.o(i.ADC_Analog_Watchdog_LowThresholds_Config), (12 bytes).
    Removing n32g401_adc.o(i.ADC_Analog_Watchdog_Mode_Channel_Config), (56 bytes).
    Removing n32g401_adc.o(i.ADC_Bypass_Calibration_Disable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Bypass_Calibration_Enable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Calibration_Auto_Load_Disable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Calibration_Auto_Load_Enable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_DMA_Transfer_Disable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Data_Resolution_Config), (28 bytes).
    Removing n32g401_adc.o(i.ADC_Deep_Power_Mode_Disable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Deep_Power_Mode_Enable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Differential_Channels_Config), (32 bytes).
    Removing n32g401_adc.o(i.ADC_Differential_Mode_Disable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Differential_Mode_Enable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Discontinuous_Mode_Channel_Count_Config), (28 bytes).
    Removing n32g401_adc.o(i.ADC_Discontinuous_Mode_Config), (80 bytes).
    Removing n32g401_adc.o(i.ADC_External_Trigger_Conversion_Config), (80 bytes).
    Removing n32g401_adc.o(i.ADC_Flag_Status_Clear), (16 bytes).
    Removing n32g401_adc.o(i.ADC_INTFlag_Status_Clear), (16 bytes).
    Removing n32g401_adc.o(i.ADC_INTFlag_Status_Get), (24 bytes).
    Removing n32g401_adc.o(i.ADC_Initializes_Ex), (134 bytes).
    Removing n32g401_adc.o(i.ADC_Initializes_StructureEx), (26 bytes).
    Removing n32g401_adc.o(i.ADC_Injected_Channels_Number_Config), (28 bytes).
    Removing n32g401_adc.o(i.ADC_Injected_Channels_Offset_Config), (28 bytes).
    Removing n32g401_adc.o(i.ADC_Injected_Channels_Software_Conversion_Operation), (72 bytes).
    Removing n32g401_adc.o(i.ADC_Injected_Group_Autoconversion_Disable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Injected_Group_Autoconversion_Enable), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Injected_Group_Conversion_Data_Get), (36 bytes).
    Removing n32g401_adc.o(i.ADC_Injected_Group_External_Trigger_Source_Config), (28 bytes).
    Removing n32g401_adc.o(i.ADC_Injected_Sequence_Conversion_Number_Config), (44 bytes).
    Removing n32g401_adc.o(i.ADC_Interrupts_Disable), (36 bytes).
    Removing n32g401_adc.o(i.ADC_Interrupts_Enable), (36 bytes).
    Removing n32g401_adc.o(i.ADC_OFF), (20 bytes).
    Removing n32g401_adc.o(i.ADC_Regular_Group_Conversion_Data_Get), (12 bytes).
    Removing n32g401_adc.o(i.ADC_Reset), (12 bytes).
    Removing n32g401_adc.o(i.ADC_Sample_Time_Level_Config), (28 bytes).
    Removing n32g401_adc.o(i.ADC_Temperature_Sensor_And_Vrefint_Channel_Disable), (40 bytes).
    Removing n32g401_adc.o(i.ADC_Vbat_Monitor_Disable), (40 bytes).
    Removing n32g401_adc.o(i.ADC_Vbat_Monitor_Enable), (40 bytes).
    Removing n32g401_adc.o(i.ADC_Vrefint_Get), (24 bytes).
    Removing n32g401_adc.o(.data), (4 bytes).
    Removing n32g401_beeper.o(.rev16_text), (4 bytes).
    Removing n32g401_beeper.o(.revsh_text), (4 bytes).
    Removing n32g401_beeper.o(.rrx_text), (6 bytes).
    Removing n32g401_beeper.o(i.BEEPER_APB_Clock_Prescale_Set), (28 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Bypass_Clock_Signal), (20 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Clock_Select), (28 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Disable), (20 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Div_Factor_Select), (32 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Enable), (20 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Initialize), (28 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Inverted_Disable), (20 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Inverted_Enable), (20 bytes).
    Removing n32g401_beeper.o(i.BEEPER_Reset), (10 bytes).
    Removing n32g401_comp.o(.rev16_text), (4 bytes).
    Removing n32g401_comp.o(.revsh_text), (4 bytes).
    Removing n32g401_comp.o(.rrx_text), (6 bytes).
    Removing n32g401_comp.o(i.COMP2_Output_Xor_Disable), (12 bytes).
    Removing n32g401_comp.o(i.COMP2_Output_Xor_Enable), (12 bytes).
    Removing n32g401_comp.o(i.COMP_Blking_Soucre_Config), (96 bytes).
    Removing n32g401_comp.o(i.COMP_Filter_Clock_Prescale_Config), (84 bytes).
    Removing n32g401_comp.o(i.COMP_Filter_Control_Config), (46 bytes).
    Removing n32g401_comp.o(i.COMP_Filter_Disable), (72 bytes).
    Removing n32g401_comp.o(i.COMP_Filter_Enable), (64 bytes).
    Removing n32g401_comp.o(i.COMP_Filter_SampWindow_Config), (108 bytes).
    Removing n32g401_comp.o(i.COMP_Filter_Threshold_Config), (108 bytes).
    Removing n32g401_comp.o(i.COMP_Hysteresis_Level_Config), (96 bytes).
    Removing n32g401_comp.o(i.COMP_Initializes), (120 bytes).
    Removing n32g401_comp.o(i.COMP_Initializes_Structure), (26 bytes).
    Removing n32g401_comp.o(i.COMP_InmSel_Config), (96 bytes).
    Removing n32g401_comp.o(i.COMP_InpSel_Config), (96 bytes).
    Removing n32g401_comp.o(i.COMP_Interrupt_Disable), (16 bytes).
    Removing n32g401_comp.o(i.COMP_Interrupt_Enable), (16 bytes).
    Removing n32g401_comp.o(i.COMP_Interrupt_Status_Get), (12 bytes).
    Removing n32g401_comp.o(i.COMP_Interrupt_Status_OneComp_Clear), (20 bytes).
    Removing n32g401_comp.o(i.COMP_Interrupt_Status_OneComp_Get), (28 bytes).
    Removing n32g401_comp.o(i.COMP_Lock_Config), (16 bytes).
    Removing n32g401_comp.o(i.COMP_OFF), (72 bytes).
    Removing n32g401_comp.o(i.COMP_ON), (72 bytes).
    Removing n32g401_comp.o(i.COMP_Output_Polarity_Config), (96 bytes).
    Removing n32g401_comp.o(i.COMP_Output_Status_Get), (48 bytes).
    Removing n32g401_comp.o(i.COMP_Output_Trigger_Config), (96 bytes).
    Removing n32g401_comp.o(i.COMP_Reset), (16 bytes).
    Removing n32g401_comp.o(i.COMP_Voltage_Reference_Config), (92 bytes).
    Removing n32g401_comp.o(i.COMP_Window_Mode_Disable), (12 bytes).
    Removing n32g401_comp.o(i.COMP_Window_Mode_Enable), (12 bytes).
    Removing n32g401_crc.o(.rev16_text), (4 bytes).
    Removing n32g401_crc.o(.revsh_text), (4 bytes).
    Removing n32g401_crc.o(.rrx_text), (6 bytes).
    Removing n32g401_crc.o(i.CRC16_Big_Endian_Format_Set), (20 bytes).
    Removing n32g401_crc.o(i.CRC16_Buffer_Calculate), (40 bytes).
    Removing n32g401_crc.o(i.CRC16_Calculate), (16 bytes).
    Removing n32g401_crc.o(i.CRC16_Get), (12 bytes).
    Removing n32g401_crc.o(i.CRC16_Little_Endian_Format_Set), (20 bytes).
    Removing n32g401_crc.o(i.CRC16_Set), (12 bytes).
    Removing n32g401_crc.o(i.CRC16_Value_Clean_Disable), (20 bytes).
    Removing n32g401_crc.o(i.CRC16_Value_Clean_Enable), (20 bytes).
    Removing n32g401_crc.o(i.CRC32_Buffer_Calculate), (36 bytes).
    Removing n32g401_crc.o(i.CRC32_Calculate), (16 bytes).
    Removing n32g401_crc.o(i.CRC32_Get), (12 bytes).
    Removing n32g401_crc.o(i.CRC32_Independent_Data_Get), (12 bytes).
    Removing n32g401_crc.o(i.CRC32_Independent_Data_Save), (12 bytes).
    Removing n32g401_crc.o(i.CRC32_Reset), (12 bytes).
    Removing n32g401_crc.o(i.CRC_LRC_Get), (12 bytes).
    Removing n32g401_crc.o(i.CRC_LRC_Set), (12 bytes).
    Removing n32g401_dbg.o(.rev16_text), (4 bytes).
    Removing n32g401_dbg.o(.revsh_text), (4 bytes).
    Removing n32g401_dbg.o(.rrx_text), (6 bytes).
    Removing n32g401_dbg.o(i.DBG_Device_Number_Get), (24 bytes).
    Removing n32g401_dbg.o(i.DBG_Peripheral_OFF), (16 bytes).
    Removing n32g401_dbg.o(i.DBG_Peripheral_ON), (16 bytes).
    Removing n32g401_dbg.o(i.DBG_Revision_Number_Get), (12 bytes).
    Removing n32g401_dbg.o(i.UCID_Get), (164 bytes).
    Removing n32g401_dbg.o(i.UID_Get), (164 bytes).
    Removing n32g401_dma.o(.rev16_text), (4 bytes).
    Removing n32g401_dma.o(.revsh_text), (4 bytes).
    Removing n32g401_dma.o(.rrx_text), (6 bytes).
    Removing n32g401_dma.o(i.DMA_Current_Data_Transfer_Number_Set), (4 bytes).
    Removing n32g401_dma.o(i.DMA_Flag_Status_Clear), (4 bytes).
    Removing n32g401_dma.o(i.DMA_Flag_Status_Get), (16 bytes).
    Removing n32g401_dma.o(i.DMA_Interrupts_Disable), (8 bytes).
    Removing n32g401_dma.o(i.DMA_Structure_Initializes), (26 bytes).
    Removing n32g401_exti.o(.rev16_text), (4 bytes).
    Removing n32g401_exti.o(.revsh_text), (4 bytes).
    Removing n32g401_exti.o(.rrx_text), (6 bytes).
    Removing n32g401_exti.o(i.EXTI_Flag_Status_Clear), (12 bytes).
    Removing n32g401_exti.o(i.EXTI_Flag_Status_Get), (24 bytes).
    Removing n32g401_exti.o(i.EXTI_RTC_Time_Stamp_Select), (24 bytes).
    Removing n32g401_exti.o(i.EXTI_Reset), (36 bytes).
    Removing n32g401_exti.o(i.EXTI_Software_Interrupt_Trigger), (16 bytes).
    Removing n32g401_exti.o(i.EXTI_Structure_Initializes), (16 bytes).
    Removing n32g401_flash.o(.rev16_text), (4 bytes).
    Removing n32g401_flash.o(.revsh_text), (4 bytes).
    Removing n32g401_flash.o(.rrx_text), (6 bytes).
    Removing n32g401_flash.o(i.FLASH_Cache_LOCK_Cancel), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Cache_LOCK_Start), (28 bytes).
    Removing n32g401_flash.o(i.FLASH_Cache_LOCK_Status_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Cache_LOCK_Stop), (16 bytes).
    Removing n32g401_flash.o(i.FLASH_Flag_Status_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_ICache_Disable), (20 bytes).
    Removing n32g401_flash.o(i.FLASH_ICache_Enable), (20 bytes).
    Removing n32g401_flash.o(i.FLASH_ICache_Reset), (20 bytes).
    Removing n32g401_flash.o(i.FLASH_ICache_Status_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Interrupt_Disable), (16 bytes).
    Removing n32g401_flash.o(i.FLASH_Interrupt_Enable), (16 bytes).
    Removing n32g401_flash.o(i.FLASH_Latency_Get), (16 bytes).
    Removing n32g401_flash.o(i.FLASH_Latency_Set), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Mass_Erase), (76 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes2_User_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_DATA_Program), (96 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_Data0_Get), (16 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_Data1_Get), (16 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_Erase), (120 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_Flag_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_User2_RDP2_Program), (116 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_User_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_User_RDP1_Program), (140 bytes).
    Removing n32g401_flash.o(i.FLASH_Option_Bytes_Write_Protection_Get), (12 bytes).
    Removing n32g401_flash.o(i.FLASH_Prefetch_Buffer_Disable), (20 bytes).
    Removing n32g401_flash.o(i.FLASH_Prefetch_Buffer_Enable), (20 bytes).
    Removing n32g401_flash.o(i.FLASH_Prefetch_Buffer_Status_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Disable), (176 bytes).
    Removing n32g401_flash.o(i.FLASH_Read_Out_Protection_L1_Enable), (176 bytes).
    Removing n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Enable), (216 bytes).
    Removing n32g401_flash.o(i.FLASH_Read_Out_Protection_L2_Status_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Read_Out_Protection_Status_Get), (24 bytes).
    Removing n32g401_flash.o(i.FLASH_Write_Protection_Enable), (120 bytes).
    Removing n32g401_flash.o(i.Option_Bytes_Lock), (20 bytes).
    Removing n32g401_flash.o(i.Option_Bytes_Lock_Status_Get), (24 bytes).
    Removing n32g401_flash.o(i.Option_Bytes_Unlock), (24 bytes).
    Removing n32g401_gpio.o(.rev16_text), (4 bytes).
    Removing n32g401_gpio.o(.revsh_text), (4 bytes).
    Removing n32g401_gpio.o(.rrx_text), (6 bytes).
    Removing n32g401_gpio.o(i.AFIO_5V_Tolerance_Disable), (16 bytes).
    Removing n32g401_gpio.o(i.AFIO_5V_Tolerance_Enable), (16 bytes).
    Removing n32g401_gpio.o(i.AFIO_ADC_External_Trigger_Remap_Set), (80 bytes).
    Removing n32g401_gpio.o(i.AFIO_Digital_EFT_Disable), (136 bytes).
    Removing n32g401_gpio.o(i.AFIO_Digital_EFT_Enable), (136 bytes).
    Removing n32g401_gpio.o(i.AFIO_EFT_Disable), (136 bytes).
    Removing n32g401_gpio.o(i.AFIO_EFT_Enable), (136 bytes).
    Removing n32g401_gpio.o(i.AFIO_EXTI_Reset), (68 bytes).
    Removing n32g401_gpio.o(i.AFIO_Filter_Stage_Ctrl), (12 bytes).
    Removing n32g401_gpio.o(i.AFIO_SPI_NSS_Mode_Set), (32 bytes).
    Removing n32g401_gpio.o(i.GPIOA_Pin_Reset), (336 bytes).
    Removing n32g401_gpio.o(i.GPIOB_Pin_Reset), (288 bytes).
    Removing n32g401_gpio.o(i.GPIOC_Pin_Reset), (172 bytes).
    Removing n32g401_gpio.o(i.GPIOD_Pin_Reset), (248 bytes).
    Removing n32g401_gpio.o(i.GPIO_ALLPin_Reset), (36 bytes).
    Removing n32g401_gpio.o(i.GPIO_Alternate_Function_Reset), (10 bytes).
    Removing n32g401_gpio.o(i.GPIO_Input_Data_Get), (8 bytes).
    Removing n32g401_gpio.o(i.GPIO_Input_Pin_Data_Get), (16 bytes).
    Removing n32g401_gpio.o(i.GPIO_Output_Data_Get), (8 bytes).
    Removing n32g401_gpio.o(i.GPIO_Output_Pin_Data_Get), (16 bytes).
    Removing n32g401_gpio.o(i.GPIO_PBC_Pins_Reset), (4 bytes).
    Removing n32g401_gpio.o(i.GPIO_PBSC_Pins_Reset), (8 bytes).
    Removing n32g401_gpio.o(i.GPIO_Pin_Lock_Set), (18 bytes).
    Removing n32g401_gpio.o(i.GPIO_Pin_Remap_Set), (112 bytes).
    Removing n32g401_gpio.o(i.GPIO_Pin_Toggle), (8 bytes).
    Removing n32g401_gpio.o(i.GPIO_Reset), (84 bytes).
    Removing n32g401_gpio.o(i.GPIO_Write), (4 bytes).
    Removing n32g401_i2c.o(.rev16_text), (4 bytes).
    Removing n32g401_i2c.o(.revsh_text), (4 bytes).
    Removing n32g401_i2c.o(.rrx_text), (6 bytes).
    Removing n32g401_i2c.o(i.I2C_7bit_Addr_Send), (18 bytes).
    Removing n32g401_i2c.o(i.I2C_ARP_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_ARP_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Acknowledg_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Acknowledg_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Acknowledgement_Config), (16 bytes).
    Removing n32g401_i2c.o(i.I2C_Addressing_Mode_Config), (16 bytes).
    Removing n32g401_i2c.o(i.I2C_Bus_Mode_Config), (16 bytes).
    Removing n32g401_i2c.o(i.I2C_Clock_Speed_Config), (268 bytes).
    Removing n32g401_i2c.o(i.I2C_DMA_Last_Transfer_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_DMA_Last_Transfer_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_DMA_Transfer_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_DMA_Transfer_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Data_Recv), (8 bytes).
    Removing n32g401_i2c.o(i.I2C_Data_Send), (4 bytes).
    Removing n32g401_i2c.o(i.I2C_Dual_Addr_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Dual_Addr_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Event_Check), (40 bytes).
    Removing n32g401_i2c.o(i.I2C_Extend_Clock_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Extend_Clock_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Fast_Mode_Duty_Cycle_Set), (26 bytes).
    Removing n32g401_i2c.o(i.I2C_Flag_Status_Clear), (12 bytes).
    Removing n32g401_i2c.o(i.I2C_Flag_Status_Get), (56 bytes).
    Removing n32g401_i2c.o(i.I2C_General_Call_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_General_Call_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Generate_Start_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Generate_Start_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Generate_Stop_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Generate_Stop_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Initializes), (50 bytes).
    Removing n32g401_i2c.o(i.I2C_Initializes_Structure), (30 bytes).
    Removing n32g401_i2c.o(i.I2C_Interrupt_Statu_Clear), (12 bytes).
    Removing n32g401_i2c.o(i.I2C_Interrupt_Status_Get), (36 bytes).
    Removing n32g401_i2c.o(i.I2C_Interrupts_Disable), (8 bytes).
    Removing n32g401_i2c.o(i.I2C_Interrupts_Enable), (8 bytes).
    Removing n32g401_i2c.o(i.I2C_Last_Event_Get), (22 bytes).
    Removing n32g401_i2c.o(i.I2C_NACK_Position_Set), (26 bytes).
    Removing n32g401_i2c.o(i.I2C_OFF), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_ON), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Own_Addr1_Config), (16 bytes).
    Removing n32g401_i2c.o(i.I2C_Own_Addr2_Set), (20 bytes).
    Removing n32g401_i2c.o(i.I2C_PEC_Compute_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_PEC_Compute_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_PEC_Get), (8 bytes).
    Removing n32g401_i2c.o(i.I2C_PEC_Position_Set), (26 bytes).
    Removing n32g401_i2c.o(i.I2C_PEC_Send_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_PEC_Send_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Register_Value_Get), (22 bytes).
    Removing n32g401_i2c.o(i.I2C_Reset), (36 bytes).
    Removing n32g401_i2c.o(i.I2C_SCL_Analog_Filter_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_SCL_Analog_Filter_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_SCL_Analog_Filter_Width_Set), (18 bytes).
    Removing n32g401_i2c.o(i.I2C_SCL_Digital_Filter_Width_Set), (18 bytes).
    Removing n32g401_i2c.o(i.I2C_SDA_Analog_Filter_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_SDA_Analog_Filter_Enable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_SDA_Analog_Filter_Width_Set), (16 bytes).
    Removing n32g401_i2c.o(i.I2C_SDA_Digital_Filter_Width_Set), (18 bytes).
    Removing n32g401_i2c.o(i.I2C_SMBus_Alert_Pin_Set), (26 bytes).
    Removing n32g401_i2c.o(i.I2C_Software_Reset_Disable), (10 bytes).
    Removing n32g401_i2c.o(i.I2C_Software_Reset_Enable), (10 bytes).
    Removing n32g401_iwdg.o(.rev16_text), (4 bytes).
    Removing n32g401_iwdg.o(.revsh_text), (4 bytes).
    Removing n32g401_iwdg.o(.rrx_text), (6 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Counter_Reload), (12 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Freeze_Enable), (16 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Key_Reload), (16 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Prescaler_Division_Set), (12 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Restore_From_Freeze), (16 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Status_Get), (24 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Write_Protection_Disable), (16 bytes).
    Removing n32g401_iwdg.o(i.IWDG_Write_Protection_Enable), (12 bytes).
    Removing n32g401_lptim.o(.rev16_text), (4 bytes).
    Removing n32g401_lptim.o(.revsh_text), (4 bytes).
    Removing n32g401_lptim.o(.rrx_text), (6 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Auto_Reload_Get), (8 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Auto_Reload_Set), (4 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Clock_Config), (22 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Clock_Filter_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Clock_Polarity_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Clock_Source_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Clock_Source_Set), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Compare_Get), (8 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Compare_Set), (4 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Counter_Get), (8 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Counter_Mode_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Counter_Mode_Set), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Counter_Start), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Encoder_Mode_Check), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Encoder_Mode_Disable), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Encoder_Mode_Enable), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Encoder_Mode_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Encoder_Mode_Set), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_FLAG_Clear), (8 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Flag_Get), (18 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Initializes), (56 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Initializes_Structure), (12 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Input1_Option), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Input2_Option), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Interrupt_Check), (18 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Interrupt_Disable), (8 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Interrupt_Enable), (8 bytes).
    Removing n32g401_lptim.o(i.LPTIM_NoEncoder_Mode_Disable), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_NoEncoder_Mode_Enable), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_OFF), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_ON), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Output_Config), (22 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Polarity_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Polarity_Set), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Prescaler_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Prescaler_Set), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Reset), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Software_Trigger), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Timeout_Disable), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Timeout_Enable), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Timeout_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Trigger_Config), (28 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Trigger_Filter_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Trigger_Polarity_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Trigger_Source_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Update_Mode_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Update_Mode_Set), (16 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Waveform_Get), (10 bytes).
    Removing n32g401_lptim.o(i.LPTIM_Waveform_Set), (16 bytes).
    Removing n32g401_pwr.o(.rev16_text), (4 bytes).
    Removing n32g401_pwr.o(.revsh_text), (4 bytes).
    Removing n32g401_pwr.o(.rrx_text), (6 bytes).
    Removing n32g401_pwr.o(i.PWR_Flag_Status_Clear), (36 bytes).
    Removing n32g401_pwr.o(i.PWR_Flag_Status_Get), (24 bytes).
    Removing n32g401_pwr.o(i.PWR_PVD_Disable), (12 bytes).
    Removing n32g401_pwr.o(i.PWR_PVD_Enable), (12 bytes).
    Removing n32g401_pwr.o(i.PWR_PVD_Level_Config), (24 bytes).
    Removing n32g401_pwr.o(i.PWR_RTC_Backup_Access_Disable), (12 bytes).
    Removing n32g401_pwr.o(i.PWR_RTC_Backup_Access_Enable), (12 bytes).
    Removing n32g401_pwr.o(i.PWR_RTC_Wakeup_Disable), (12 bytes).
    Removing n32g401_pwr.o(i.PWR_RTC_Wakeup_Enable), (12 bytes).
    Removing n32g401_pwr.o(i.PWR_Reset), (12 bytes).
    Removing n32g401_pwr.o(i.PWR_SLEEP_Mode_Enter), (64 bytes).
    Removing n32g401_pwr.o(i.PWR_STANDBY_Mode_Enter), (72 bytes).
    Removing n32g401_pwr.o(i.PWR_STOP0_Mode_Enter), (68 bytes).
    Removing n32g401_pwr.o(i.PWR_STOP2_Mode_Enter), (72 bytes).
    Removing n32g401_pwr.o(i.PWR_Wakeup_Pin_Disable), (60 bytes).
    Removing n32g401_pwr.o(i.PWR_Wakeup_Pin_Enable), (84 bytes).
    Removing n32g401_rcc.o(.rev16_text), (4 bytes).
    Removing n32g401_rcc.o(.revsh_text), (4 bytes).
    Removing n32g401_rcc.o(.rrx_text), (6 bytes).
    Removing n32g401_rcc.o(i.RCC_ADC_Hclk_Disable), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Disable), (16 bytes).
    Removing n32g401_rcc.o(i.RCC_AHB_Peripheral_Reset), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_APB1_Peripheral_Clock_Disable), (16 bytes).
    Removing n32g401_rcc.o(i.RCC_APB1_Peripheral_Reset), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_APB2_Peripheral_Clock_Disable), (16 bytes).
    Removing n32g401_rcc.o(i.RCC_Backup_Reset), (16 bytes).
    Removing n32g401_rcc.o(i.RCC_Clock_Security_System_Disable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_Clock_Security_System_Enable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_HSE_Stable_Wait), (84 bytes).
    Removing n32g401_rcc.o(i.RCC_HSI_Calibration_Value_Set), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_HSI_Disable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_HSI_Enable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_HSI_Stable_Wait), (84 bytes).
    Removing n32g401_rcc.o(i.RCC_Hclk_Config), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_Interrupt_Disable), (16 bytes).
    Removing n32g401_rcc.o(i.RCC_LPTIM_Clock_Config), (56 bytes).
    Removing n32g401_rcc.o(i.RCC_LPTIM_Disable), (20 bytes).
    Removing n32g401_rcc.o(i.RCC_LPTIM_Enable), (20 bytes).
    Removing n32g401_rcc.o(i.RCC_LPTIM_Reset), (28 bytes).
    Removing n32g401_rcc.o(i.RCC_LSE_Clock_Security_System_Disable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_LSE_Clock_Security_System_Enable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_LSE_Clock_Security_System_Status_Get), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_LSE_Config), (96 bytes).
    Removing n32g401_rcc.o(i.RCC_LSE_Stable_Wait), (84 bytes).
    Removing n32g401_rcc.o(i.RCC_LSE_Trim_Config), (44 bytes).
    Removing n32g401_rcc.o(i.RCC_LSI_Disable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_LSI_Enable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_LSI_Stable_Wait), (84 bytes).
    Removing n32g401_rcc.o(i.RCC_MCO_PLL_Prescaler_Config), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_PLL_Config), (56 bytes).
    Removing n32g401_rcc.o(i.RCC_PLL_Disable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_Pclk1_Config), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_Pclk2_Config), (24 bytes).
    Removing n32g401_rcc.o(i.RCC_RTC_Clock_Config), (28 bytes).
    Removing n32g401_rcc.o(i.RCC_RTC_Clock_Disable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_RTC_Clock_Enable), (12 bytes).
    Removing n32g401_rcc.o(i.RCC_Reset), (100 bytes).
    Removing n32g401_rcc.o(i.RCC_Reset_Flag_Clear), (28 bytes).
    Removing n32g401_rcc.o(i.RCC_Sysclk_Source_Get), (16 bytes).
    Removing n32g401_rcc.o(i.RCC_TIM1_8_Clock_Config), (24 bytes).
    Removing n32g401_rtc.o(.rev16_text), (4 bytes).
    Removing n32g401_rtc.o(.revsh_text), (4 bytes).
    Removing n32g401_rtc.o(.rrx_text), (6 bytes).
    Removing n32g401_rtc.o(i.RTC_Alarm_Disable), (80 bytes).
    Removing n32g401_rtc.o(i.RTC_Alarm_Enable), (32 bytes).
    Removing n32g401_rtc.o(i.RTC_Alarm_Get), (116 bytes).
    Removing n32g401_rtc.o(i.RTC_Alarm_Set), (176 bytes).
    Removing n32g401_rtc.o(i.RTC_Alarm_Struct_Initializes), (22 bytes).
    Removing n32g401_rtc.o(i.RTC_Alarm_SubSecond_Config), (44 bytes).
    Removing n32g401_rtc.o(i.RTC_Alarm_SubSecond_Get), (36 bytes).
    Removing n32g401_rtc.o(i.RTC_Backup_Register_Read), (20 bytes).
    Removing n32g401_rtc.o(i.RTC_Backup_Register_Write), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Bcd2_To_Byte), (22 bytes).
    Removing n32g401_rtc.o(i.RTC_Bypass_Shadow_Disable), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Bypass_Shadow_Enable), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Byte_To_Bcd2), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Calendar_Initializes), (428 bytes).
    Removing n32g401_rtc.o(i.RTC_Calibration_Output_Config), (40 bytes).
    Removing n32g401_rtc.o(i.RTC_Calibration_Output_Disable), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Calibration_Output_Enable), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Date_Get), (76 bytes).
    Removing n32g401_rtc.o(i.RTC_Date_Struct_Initializes), (14 bytes).
    Removing n32g401_rtc.o(i.RTC_Day_Light_Saving_Config), (52 bytes).
    Removing n32g401_rtc.o(i.RTC_Deinitializes), (116 bytes).
    Removing n32g401_rtc.o(i.RTC_Flag_Clear), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Flag_Status_Get), (36 bytes).
    Removing n32g401_rtc.o(i.RTC_Hour_Format_Set), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Initialization_Mode_Enter), (112 bytes).
    Removing n32g401_rtc.o(i.RTC_Initialization_Mode_Exit), (20 bytes).
    Removing n32g401_rtc.o(i.RTC_Interrupt_Status_Clear), (32 bytes).
    Removing n32g401_rtc.o(i.RTC_Interrupt_Status_Get), (76 bytes).
    Removing n32g401_rtc.o(i.RTC_Interrupts_Disable), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Interrupts_Enable), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Output_Config), (52 bytes).
    Removing n32g401_rtc.o(i.RTC_Output_Mode_Config), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Prescale_Config), (20 bytes).
    Removing n32g401_rtc.o(i.RTC_Reference_Clock_Disable), (48 bytes).
    Removing n32g401_rtc.o(i.RTC_Reference_Clock_Enable), (48 bytes).
    Removing n32g401_rtc.o(i.RTC_Registers_Reset), (84 bytes).
    Removing n32g401_rtc.o(i.RTC_Smooth_Calibration_Config), (84 bytes).
    Removing n32g401_rtc.o(i.RTC_Store_Operation_Get), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Structure_Initializes), (14 bytes).
    Removing n32g401_rtc.o(i.RTC_SubSecond_Get), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Synchronization_Shift_Config), (108 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Backup_Register_Clear_Disable), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Backup_Register_Clear_Enable), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Disable), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Enable), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Filter_Config), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Interrput_Disable), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Interrput_Enable), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Pins_Precharge_Duration), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Precharge_Disable), (20 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Precharge_Enable), (20 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Sampling_Frequency_Config), (28 bytes).
    Removing n32g401_rtc.o(i.RTC_Tamper_Trigger_Config), (36 bytes).
    Removing n32g401_rtc.o(i.RTC_TimeStamp_Disable), (36 bytes).
    Removing n32g401_rtc.o(i.RTC_TimeStamp_Enable), (44 bytes).
    Removing n32g401_rtc.o(i.RTC_TimeStamp_Get), (156 bytes).
    Removing n32g401_rtc.o(i.RTC_TimeStamp_On_Tamper_Detection_Disable), (20 bytes).
    Removing n32g401_rtc.o(i.RTC_TimeStamp_On_Tamper_Detection_Enable), (20 bytes).
    Removing n32g401_rtc.o(i.RTC_TimeStamp_SubSecond_Get), (12 bytes).
    Removing n32g401_rtc.o(i.RTC_Time_Get), (88 bytes).
    Removing n32g401_rtc.o(i.RTC_Time_Struct_Initializes), (12 bytes).
    Removing n32g401_rtc.o(i.RTC_Wait_For_Synchronization), (76 bytes).
    Removing n32g401_rtc.o(i.RTC_WakeUp_Clock_Select), (40 bytes).
    Removing n32g401_rtc.o(i.RTC_WakeUp_Counter_Get), (12 bytes).
    Removing n32g401_rtc.o(i.RTC_WakeUp_Counter_Set), (48 bytes).
    Removing n32g401_rtc.o(i.RTC_WakeUp_Disable), (80 bytes).
    Removing n32g401_rtc.o(i.RTC_WakeUp_Enable), (32 bytes).
    Removing n32g401_rtc.o(i.RTC_Write_Protection_Disable), (16 bytes).
    Removing n32g401_rtc.o(i.RTC_Write_Protection_Enable), (12 bytes).
    Removing n32g401_rtc.o(.data), (8 bytes).
    Removing n32g401_spi.o(.rev16_text), (4 bytes).
    Removing n32g401_spi.o(.revsh_text), (4 bytes).
    Removing n32g401_spi.o(.rrx_text), (6 bytes).
    Removing n32g401_spi.o(i.I2S_AudioFrequency_Config), (170 bytes).
    Removing n32g401_spi.o(i.I2S_CLKPOL_Config), (16 bytes).
    Removing n32g401_spi.o(i.I2S_DataFormat_Config), (16 bytes).
    Removing n32g401_spi.o(i.I2S_Initializes), (70 bytes).
    Removing n32g401_spi.o(i.I2S_Initializes_Structure), (20 bytes).
    Removing n32g401_spi.o(i.I2S_MCLK_Disable), (10 bytes).
    Removing n32g401_spi.o(i.I2S_MCLK_Enable), (10 bytes).
    Removing n32g401_spi.o(i.I2S_Mode_Config), (16 bytes).
    Removing n32g401_spi.o(i.I2S_OFF), (10 bytes).
    Removing n32g401_spi.o(i.I2S_ON), (10 bytes).
    Removing n32g401_spi.o(i.I2S_Standard_Config), (16 bytes).
    Removing n32g401_spi.o(i.SPI_CRC_Data_Get), (14 bytes).
    Removing n32g401_spi.o(i.SPI_CRC_Enable), (10 bytes).
    Removing n32g401_spi.o(i.SPI_CRC_Polynomial_Get), (6 bytes).
    Removing n32g401_spi.o(i.SPI_I2S_Clear_Flag_Status), (52 bytes).
    Removing n32g401_spi.o(i.SPI_I2S_DMA_Transfer_Disable), (8 bytes).
    Removing n32g401_spi.o(i.SPI_I2S_DMA_Transfer_Enable), (8 bytes).
    Removing n32g401_spi.o(i.SPI_I2S_Interrupt_Flag_Status_Get), (122 bytes).
    Removing n32g401_spi.o(i.SPI_I2S_Interrupts_Disable), (8 bytes).
    Removing n32g401_spi.o(i.SPI_I2S_Interrupts_Enable), (8 bytes).
    Removing n32g401_spi.o(i.SPI_I2S_Mode_Select), (16 bytes).
    Removing n32g401_spi.o(i.SPI_Next_Transmit_CRC), (10 bytes).
    Removing n32g401_spi.o(i.SPI_OFF), (10 bytes).
    Removing n32g401_spi.o(i.SPI_SS_Output_Disable), (10 bytes).
    Removing n32g401_spi.o(i.SPI_SS_Output_Enable), (10 bytes).
    Removing n32g401_tim.o(.rev16_text), (4 bytes).
    Removing n32g401_tim.o(.revsh_text), (4 bytes).
    Removing n32g401_tim.o(.rrx_text), (6 bytes).
    Removing n32g401_tim.o(i.Input_Channel1_Config), (124 bytes).
    Removing n32g401_tim.o(i.Input_Channel2_Config), (136 bytes).
    Removing n32g401_tim.o(i.Input_Channel3_Config), (132 bytes).
    Removing n32g401_tim.o(i.Input_Channel4_Config), (140 bytes).
    Removing n32g401_tim.o(i.TIM_Asymmetric_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Asymmetric_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Auto_Reload_Get), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Auto_Reload_Preload_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Auto_Reload_Preload_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Base_Center_Aligned_Mode_OC4_7_8_9_Trigger_Set), (16 bytes).
    Removing n32g401_tim.o(i.TIM_Base_Count_Get), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Base_Count_Set), (4 bytes).
    Removing n32g401_tim.o(i.TIM_Base_Prescaler_Get), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Break_And_Dead_Time_Set), (96 bytes).
    Removing n32g401_tim.o(i.TIM_Break_And_Dead_Time_Struct_Initialize), (24 bytes).
    Removing n32g401_tim.o(i.TIM_Break_Filter_Config), (52 bytes).
    Removing n32g401_tim.o(i.TIM_Break_Filter_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Break_Filter_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_CCEN_Status_Get), (16 bytes).
    Removing n32g401_tim.o(i.TIM_Capture_Compare_Ch_Disable), (12 bytes).
    Removing n32g401_tim.o(i.TIM_Capture_Compare_Ch_Enable), (12 bytes).
    Removing n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Disable), (12 bytes).
    Removing n32g401_tim.o(i.TIM_Capture_Compare_Ch_N_Enable), (12 bytes).
    Removing n32g401_tim.o(i.TIM_Capture_Compare_Control_Preload_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Capture_Compare_Control_Preload_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Channel1_Filter_Config), (68 bytes).
    Removing n32g401_tim.o(i.TIM_Channel1_Filter_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Channel1_Filter_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Channel2_Filter_Config), (68 bytes).
    Removing n32g401_tim.o(i.TIM_Channel2_Filter_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Channel2_Filter_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Channel3_Filter_Config), (68 bytes).
    Removing n32g401_tim.o(i.TIM_Channel3_Filter_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Channel3_Filter_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Channel4_Filter_Config), (68 bytes).
    Removing n32g401_tim.o(i.TIM_Channel4_Filter_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Channel4_Filter_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Commutation_Event_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Commutation_Event_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Compare1_D_Set), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Compare1_Set), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Compare2_D_Set), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Compare2_Set), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Compare3_D_Set), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Compare3_Set), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Compare4_D_Set), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Compare4_Set), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Compare5_Set), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Compare6_Set), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Compare7_Set), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Compare8_Set), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Compare9_Set), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture1_D_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture1_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture2_D_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture2_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture3_D_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture3_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture4_D_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture4_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture5_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture6_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture7_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture8_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Compare_Capture9_Get), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Dma_Config), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Dma_Disable), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Dma_Enable), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Encoder_Interface_Set), (64 bytes).
    Removing n32g401_tim.o(i.TIM_Event_Generate), (4 bytes).
    Removing n32g401_tim.o(i.TIM_External_Clock_Mode1_Set), (66 bytes).
    Removing n32g401_tim.o(i.TIM_External_Clock_Mode2_Set), (44 bytes).
    Removing n32g401_tim.o(i.TIM_External_Trigger_Filter_Set), (24 bytes).
    Removing n32g401_tim.o(i.TIM_External_Trigger_Polarity_Set), (26 bytes).
    Removing n32g401_tim.o(i.TIM_External_Trigger_Prescaler_Set), (16 bytes).
    Removing n32g401_tim.o(i.TIM_Flag_Clear), (6 bytes).
    Removing n32g401_tim.o(i.TIM_Flag_Status_Get), (16 bytes).
    Removing n32g401_tim.o(i.TIM_Forced_Output_Channel1_Set), (18 bytes).
    Removing n32g401_tim.o(i.TIM_Forced_Output_Channel2_Set), (26 bytes).
    Removing n32g401_tim.o(i.TIM_Forced_Output_Channel3_Set), (20 bytes).
    Removing n32g401_tim.o(i.TIM_Forced_Output_Channel4_Set), (28 bytes).
    Removing n32g401_tim.o(i.TIM_Forced_Output_Channel5_Set), (22 bytes).
    Removing n32g401_tim.o(i.TIM_Forced_Output_Channel6_Set), (30 bytes).
    Removing n32g401_tim.o(i.TIM_Hall_Sensor_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Hall_Sensor_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_IOM_Comp_Break), (22 bytes).
    Removing n32g401_tim.o(i.TIM_Input_Capture1_Prescaler_Set), (18 bytes).
    Removing n32g401_tim.o(i.TIM_Input_Capture2_Prescaler_Set), (26 bytes).
    Removing n32g401_tim.o(i.TIM_Input_Capture3_Prescaler_Set), (18 bytes).
    Removing n32g401_tim.o(i.TIM_Input_Capture4_Prescaler_Set), (20 bytes).
    Removing n32g401_tim.o(i.TIM_Input_Channel_Initialize), (110 bytes).
    Removing n32g401_tim.o(i.TIM_Input_Struct_Initialize), (16 bytes).
    Removing n32g401_tim.o(i.TIM_Internal_Clock_Set), (12 bytes).
    Removing n32g401_tim.o(i.TIM_Internal_Trig_To_Ext_Set), (24 bytes).
    Removing n32g401_tim.o(i.TIM_Interrupt_Disable), (8 bytes).
    Removing n32g401_tim.o(i.TIM_Lock_Up_Break_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Lock_Up_Break_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Master_Slave_Mode_Set), (18 bytes).
    Removing n32g401_tim.o(i.TIM_OC4REF_Trigger_To_ADC_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_OC4REF_Trigger_To_ADC_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_OC7REF_Trigger_To_ADC_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_OC7REF_Trigger_To_ADC_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_OC8REF_Trigger_To_ADC_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_OC8REF_Trigger_To_ADC_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_OC9REF_Trigger_To_ADC_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_OC9REF_Trigger_To_ADC_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Off), (10 bytes).
    Removing n32g401_tim.o(i.TIM_One_Pulse_Mode_Select), (16 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel1_Fast_Set), (18 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel1_Initialize), (148 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel1_Preload_Set), (18 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel1_Reference_Clear), (18 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel2_Fast_Set), (26 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel2_Initialize), (160 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel2_Preload_Set), (26 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel2_Reference_Clear), (24 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel3_Fast_Set), (20 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel3_Initialize), (156 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel3_Preload_Set), (20 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel3_Reference_Clear), (20 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel4_Fast_Set), (28 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel4_Initialize), (176 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel4_Preload_Set), (28 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel4_Reference_Clear), (26 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel5_Fast_Set), (22 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel5_Initialize), (112 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel5_Preload_Set), (22 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel5_Reference_Clear), (22 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel6_Fast_Set), (30 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel6_Initialize), (120 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel6_Preload_Set), (30 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel6_Reference_Clear), (28 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel7_Preload_Set), (26 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel8_Preload_Set), (26 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel9_Preload_Set), (26 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel_Mode_Set), (86 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel_N_Polarity_Set), (22 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel_Polarity_Set), (22 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Channel_Struct_Initialize), (20 bytes).
    Removing n32g401_tim.o(i.TIM_Output_Trigger_Select), (20 bytes).
    Removing n32g401_tim.o(i.TIM_PWM_Input_Channel_Config), (120 bytes).
    Removing n32g401_tim.o(i.TIM_PWM_Output_Disable), (14 bytes).
    Removing n32g401_tim.o(i.TIM_PWM_Output_Enable), (14 bytes).
    Removing n32g401_tim.o(i.TIM_Pvd_Break_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Pvd_Break_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Reset), (128 bytes).
    Removing n32g401_tim.o(i.TIM_Slave_Mode_Select), (18 bytes).
    Removing n32g401_tim.o(i.TIM_Trigger_As_External_Clock), (58 bytes).
    Removing n32g401_tim.o(i.TIM_Trigger_Source_Select), (18 bytes).
    Removing n32g401_tim.o(i.TIM_Update_Event_Disable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Update_Event_Enable), (10 bytes).
    Removing n32g401_tim.o(i.TIM_Update_Request_Source_Set), (22 bytes).
    Removing n32g401_usart.o(.rev16_text), (4 bytes).
    Removing n32g401_usart.o(.revsh_text), (4 bytes).
    Removing n32g401_usart.o(.rrx_text), (6 bytes).
    Removing n32g401_usart.o(i.USART_Address_Set), (16 bytes).
    Removing n32g401_usart.o(i.USART_Break_Frame_Send), (10 bytes).
    Removing n32g401_usart.o(i.USART_Clock_Config), (16 bytes).
    Removing n32g401_usart.o(i.USART_Clock_Initializes), (40 bytes).
    Removing n32g401_usart.o(i.USART_Clock_Structure_Initializes), (12 bytes).
    Removing n32g401_usart.o(i.USART_DMA_Transfer_Disable), (8 bytes).
    Removing n32g401_usart.o(i.USART_Data_Send), (8 bytes).
    Removing n32g401_usart.o(i.USART_Disable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Flag_Clear), (6 bytes).
    Removing n32g401_usart.o(i.USART_Flag_Status_Get), (16 bytes).
    Removing n32g401_usart.o(i.USART_Guard_Time_Set), (16 bytes).
    Removing n32g401_usart.o(i.USART_Half_Duplex_Disable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Half_Duplex_Enable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Interrput_Disable), (54 bytes).
    Removing n32g401_usart.o(i.USART_IrDA_Disable), (10 bytes).
    Removing n32g401_usart.o(i.USART_IrDA_Enable), (10 bytes).
    Removing n32g401_usart.o(i.USART_IrDA_Mode_Set), (16 bytes).
    Removing n32g401_usart.o(i.USART_LIN_Break_Detect_Length_Set), (16 bytes).
    Removing n32g401_usart.o(i.USART_LIN_Disable), (10 bytes).
    Removing n32g401_usart.o(i.USART_LIN_Enable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Last_Bit_Config), (16 bytes).
    Removing n32g401_usart.o(i.USART_Phase_Config), (16 bytes).
    Removing n32g401_usart.o(i.USART_Polarity_Config), (16 bytes).
    Removing n32g401_usart.o(i.USART_Prescaler_Set), (16 bytes).
    Removing n32g401_usart.o(i.USART_Receiver_Wakeup_Disable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Receiver_Wakeup_Enable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Reset), (84 bytes).
    Removing n32g401_usart.o(i.USART_Smart_Card_Disable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Smart_Card_Enable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Smart_Card_NACK_Disable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Smart_Card_NACK_Enable), (10 bytes).
    Removing n32g401_usart.o(i.USART_Structure_Initializes), (24 bytes).
    Removing n32g401_usart.o(i.USART_WakeUp_Mode_Set), (16 bytes).
    Removing n32g401_wwdg.o(.rev16_text), (4 bytes).
    Removing n32g401_wwdg.o(.revsh_text), (4 bytes).
    Removing n32g401_wwdg.o(.rrx_text), (6 bytes).
    Removing n32g401_wwdg.o(i.WWDG_Counter_Value_Set), (16 bytes).
    Removing n32g401_wwdg.o(i.WWDG_EWINTF_Flag_Clear), (12 bytes).
    Removing n32g401_wwdg.o(i.WWDG_EWINTF_Flag_Get), (12 bytes).
    Removing n32g401_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing n32g401_wwdg.o(i.WWDG_Interrupt_Enable), (12 bytes).
    Removing n32g401_wwdg.o(i.WWDG_Prescaler_Division_Set), (24 bytes).
    Removing n32g401_wwdg.o(i.WWDG_Reset), (12 bytes).
    Removing n32g401_wwdg.o(i.WWDG_Window_Value_Set), (40 bytes).
    Removing proj_version.o(.rev16_text), (4 bytes).
    Removing proj_version.o(.revsh_text), (4 bytes).
    Removing proj_version.o(.rrx_text), (6 bytes).
    Removing proj_version.o(i.get_proj_version), (20 bytes).
    Removing proj_version.o(.constdata), (16 bytes).
    Removing fw_44_00_00_80_R00.o(.rev16_text), (4 bytes).
    Removing fw_44_00_00_80_R00.o(.revsh_text), (4 bytes).
    Removing fw_44_00_00_80_R00.o(.rrx_text), (6 bytes).
    Removing A2_Configurable.o(.rev16_text), (4 bytes).
    Removing A2_Configurable.o(.revsh_text), (4 bytes).
    Removing A2_Configurable.o(.rrx_text), (6 bytes).
    Removing User_Driver.o(.rev16_text), (4 bytes).
    Removing User_Driver.o(.revsh_text), (4 bytes).
    Removing User_Driver.o(.rrx_text), (6 bytes).
    Removing VI4302_Config.o(.rev16_text), (4 bytes).
    Removing VI4302_Config.o(.revsh_text), (4 bytes).
    Removing VI4302_Config.o(.rrx_text), (6 bytes).
    Removing VI4302_Handle.o(.rev16_text), (4 bytes).
    Removing VI4302_Handle.o(.revsh_text), (4 bytes).
    Removing VI4302_Handle.o(.rrx_text), (6 bytes).
    Removing VI4302_System.o(.rev16_text), (4 bytes).
    Removing VI4302_System.o(.revsh_text), (4 bytes).
    Removing VI4302_System.o(.rrx_text), (6 bytes).
    Removing VI4302_System.o(i.Search_logic_init), (72 bytes).
    Removing VI4302_System.o(i.VI4302_Frame_Rate_Config), (80 bytes).
    Removing VI4302_System.o(i.VI4302_Read_Reg), (92 bytes).
    Removing VI4302_System.o(i.vi4302_read_otp), (72 bytes).
    Removing data_handle.o(.rev16_text), (4 bytes).
    Removing data_handle.o(.revsh_text), (4 bytes).
    Removing data_handle.o(.rrx_text), (6 bytes).
    Removing data_handle.o(i.Get_AttenNoise_Rectified_Tof), (212 bytes).
    Removing data_handle.o(i.Get_Mid_Position), (66 bytes).
    Removing data_handle.o(i.Refresh_Tof_With_MidFilter), (168 bytes).
    Removing data_handle.o(i.cal_final_tof_simulation), (2 bytes).
    Removing data_handle.o(.bss), (32 bytes).
    Removing cmd_handle.o(.rev16_text), (4 bytes).
    Removing cmd_handle.o(.revsh_text), (4 bytes).
    Removing cmd_handle.o(.rrx_text), (6 bytes).
    Removing cmd_handle.o(i.CMD_SumCheck), (30 bytes).
    Removing cmd_handle.o(i.HEART_AutoSend), (18 bytes).
    Removing cmd_handle.o(i.SYSTERM_Init), (68 bytes).
    Removing cmd_handle.o(i.YC_DealProtocol), (2992 bytes).
    Removing cmd_handle.o(i.YC_UART_AllHandle), (142 bytes).
    Removing cmd_handle.o(i.YC_UART_CmdAckSend), (112 bytes).
    Removing cmd_handle.o(i.host16s_to_be), (16 bytes).
    Removing cmd_handle.o(i.host16s_to_le), (16 bytes).
    Removing cmd_handle.o(i.host16u_to_be), (12 bytes).
    Removing cmd_handle.o(i.host16u_to_le), (12 bytes).
    Removing cmd_handle.o(i.host32s_to_be), (16 bytes).
    Removing cmd_handle.o(i.host32s_to_le), (16 bytes).
    Removing cmd_handle.o(i.host32u_to_be), (20 bytes).
    Removing cmd_handle.o(i.host32u_to_le), (20 bytes).
    Removing uartfifo.o(.rev16_text), (4 bytes).
    Removing uartfifo.o(.revsh_text), (4 bytes).
    Removing uartfifo.o(.rrx_text), (6 bytes).
    Removing work_mode.o(.rev16_text), (4 bytes).
    Removing work_mode.o(.revsh_text), (4 bytes).
    Removing work_mode.o(.rrx_text), (6 bytes).
    Removing work_mode.o(.bss), (24 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.NVIC_SetVectorTable), (16 bytes).
    Removing n32g401_it.o(.rev16_text), (4 bytes).
    Removing n32g401_it.o(.revsh_text), (4 bytes).
    Removing n32g401_it.o(.rrx_text), (6 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing cdcmple.o(.text), (48 bytes).

809 unused section(s) (total 26482 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl.c           0x00000000   Number         0  __dczerorl.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/floor.c                       0x00000000   Number         0  floor.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    .\..\bsp\src\GPIO.c                      0x00000000   Number         0  GPIO.o ABSOLUTE
    .\..\bsp\src\adc.c                       0x00000000   Number         0  adc.o ABSOLUTE
    .\..\bsp\src\delay.c                     0x00000000   Number         0  delay.o ABSOLUTE
    .\..\bsp\src\flash.c                     0x00000000   Number         0  flash.o ABSOLUTE
    .\..\bsp\src\spi.c                       0x00000000   Number         0  spi.o ABSOLUTE
    .\..\bsp\src\timer.c                     0x00000000   Number         0  timer.o ABSOLUTE
    .\..\bsp\src\usart.c                     0x00000000   Number         0  usart.o ABSOLUTE
    .\..\bsp\src\wdt.c                       0x00000000   Number         0  wdt.o ABSOLUTE
    .\..\firmware\CMSIS\device\startup\startup_n32g401.s 0x00000000   Number         0  startup_n32g401.o ABSOLUTE
    .\..\firmware\CMSIS\device\system_n32g401.c 0x00000000   Number         0  system_n32g401.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_adc.c 0x00000000   Number         0  n32g401_adc.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_beeper.c 0x00000000   Number         0  n32g401_beeper.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_comp.c 0x00000000   Number         0  n32g401_comp.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_crc.c 0x00000000   Number         0  n32g401_crc.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_dbg.c 0x00000000   Number         0  n32g401_dbg.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_dma.c 0x00000000   Number         0  n32g401_dma.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_exti.c 0x00000000   Number         0  n32g401_exti.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_flash.c 0x00000000   Number         0  n32g401_flash.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_gpio.c 0x00000000   Number         0  n32g401_gpio.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_i2c.c 0x00000000   Number         0  n32g401_i2c.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_iwdg.c 0x00000000   Number         0  n32g401_iwdg.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_lptim.c 0x00000000   Number         0  n32g401_lptim.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_pwr.c 0x00000000   Number         0  n32g401_pwr.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_rcc.c 0x00000000   Number         0  n32g401_rcc.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_rtc.c 0x00000000   Number         0  n32g401_rtc.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_spi.c 0x00000000   Number         0  n32g401_spi.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_tim.c 0x00000000   Number         0  n32g401_tim.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_usart.c 0x00000000   Number         0  n32g401_usart.o ABSOLUTE
    .\..\firmware\n32g401_std_periph_driver\src\n32g401_wwdg.c 0x00000000   Number         0  n32g401_wwdg.o ABSOLUTE
    .\..\utils\src\proj_version.c            0x00000000   Number         0  proj_version.o ABSOLUTE
    .\VI4302 API\BIN\fw_44_00_00_80_R00.c    0x00000000   Number         0  fw_44_00_00_80_R00.o ABSOLUTE
    .\VI4302 API\src\A2_Configurable.c       0x00000000   Number         0  A2_Configurable.o ABSOLUTE
    .\VI4302 API\src\User_Driver.c           0x00000000   Number         0  User_Driver.o ABSOLUTE
    .\VI4302 API\src\VI4302_Config.c         0x00000000   Number         0  VI4302_Config.o ABSOLUTE
    .\VI4302 API\src\VI4302_Handle.c         0x00000000   Number         0  VI4302_Handle.o ABSOLUTE
    .\VI4302 API\src\VI4302_System.c         0x00000000   Number         0  VI4302_System.o ABSOLUTE
    .\VI4302 API\src\data_handle.c           0x00000000   Number         0  data_handle.o ABSOLUTE
    .\\..\\bsp\\src\\GPIO.c                  0x00000000   Number         0  GPIO.o ABSOLUTE
    .\\..\\bsp\\src\\adc.c                   0x00000000   Number         0  adc.o ABSOLUTE
    .\\..\\bsp\\src\\delay.c                 0x00000000   Number         0  delay.o ABSOLUTE
    .\\..\\bsp\\src\\flash.c                 0x00000000   Number         0  flash.o ABSOLUTE
    .\\..\\bsp\\src\\spi.c                   0x00000000   Number         0  spi.o ABSOLUTE
    .\\..\\bsp\\src\\timer.c                 0x00000000   Number         0  timer.o ABSOLUTE
    .\\..\\bsp\\src\\usart.c                 0x00000000   Number         0  usart.o ABSOLUTE
    .\\..\\bsp\\src\\wdt.c                   0x00000000   Number         0  wdt.o ABSOLUTE
    .\\..\\firmware\\CMSIS\\device\\system_n32g401.c 0x00000000   Number         0  system_n32g401.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.c 0x00000000   Number         0  n32g401_adc.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.c 0x00000000   Number         0  n32g401_beeper.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.c 0x00000000   Number         0  n32g401_comp.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.c 0x00000000   Number         0  n32g401_crc.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.c 0x00000000   Number         0  n32g401_dbg.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.c 0x00000000   Number         0  n32g401_dma.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.c 0x00000000   Number         0  n32g401_exti.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.c 0x00000000   Number         0  n32g401_flash.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.c 0x00000000   Number         0  n32g401_gpio.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.c 0x00000000   Number         0  n32g401_i2c.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.c 0x00000000   Number         0  n32g401_iwdg.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.c 0x00000000   Number         0  n32g401_lptim.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.c 0x00000000   Number         0  n32g401_pwr.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.c 0x00000000   Number         0  n32g401_rcc.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.c 0x00000000   Number         0  n32g401_rtc.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.c 0x00000000   Number         0  n32g401_spi.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.c 0x00000000   Number         0  n32g401_tim.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.c 0x00000000   Number         0  n32g401_usart.o ABSOLUTE
    .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.c 0x00000000   Number         0  n32g401_wwdg.o ABSOLUTE
    .\\..\\utils\\src\\proj_version.c        0x00000000   Number         0  proj_version.o ABSOLUTE
    .\\VI4302 API\\BIN\\fw_44_00_00_80_R00.c 0x00000000   Number         0  fw_44_00_00_80_R00.o ABSOLUTE
    .\\VI4302 API\\src\\A2_Configurable.c    0x00000000   Number         0  A2_Configurable.o ABSOLUTE
    .\\VI4302 API\\src\\User_Driver.c        0x00000000   Number         0  User_Driver.o ABSOLUTE
    .\\VI4302 API\\src\\VI4302_Config.c      0x00000000   Number         0  VI4302_Config.o ABSOLUTE
    .\\VI4302 API\\src\\VI4302_Handle.c      0x00000000   Number         0  VI4302_Handle.o ABSOLUTE
    .\\VI4302 API\\src\\VI4302_System.c      0x00000000   Number         0  VI4302_System.o ABSOLUTE
    .\\VI4302 API\\src\\data_handle.c        0x00000000   Number         0  data_handle.o ABSOLUTE
    .\\app\\cmd_handle\\cmd_handle.c         0x00000000   Number         0  cmd_handle.o ABSOLUTE
    .\\app\\uartfifo\\uartfifo.C             0x00000000   Number         0  uartfifo.o ABSOLUTE
    .\\app\\work_mode\\work_mode.c           0x00000000   Number         0  work_mode.o ABSOLUTE
    .\\user\\src\\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    .\\user\\src\\n32g401_it.c               0x00000000   Number         0  n32g401_it.o ABSOLUTE
    .\app\cmd_handle\cmd_handle.c            0x00000000   Number         0  cmd_handle.o ABSOLUTE
    .\app\uartfifo\uartfifo.C                0x00000000   Number         0  uartfifo.o ABSOLUTE
    .\app\work_mode\work_mode.c              0x00000000   Number         0  work_mode.o ABSOLUTE
    .\user\src\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    .\user\src\n32g401_it.c                  0x00000000   Number         0  n32g401_it.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      276  startup_n32g401.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000114   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000114   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000118   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x0800011c   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x0800011c   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x0800011c   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000124   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000124   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000124   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000124   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000128   Section       36  startup_n32g401.o(.text)
    $v0                                      0x08000128   Number         0  startup_n32g401.o(.text)
    .text                                    0x0800014c   Section        0  memcpya.o(.text)
    .text                                    0x08000170   Section        0  memseta.o(.text)
    .text                                    0x08000194   Section        0  dadd.o(.text)
    .text                                    0x080002e2   Section        0  dflti.o(.text)
    .text                                    0x08000304   Section        0  dfltui.o(.text)
    .text                                    0x0800031e   Section        0  dfixi.o(.text)
    .text                                    0x0800035c   Section        0  dfixui.o(.text)
    .text                                    0x0800038e   Section        0  llshl.o(.text)
    .text                                    0x080003ac   Section        0  llushr.o(.text)
    .text                                    0x080003cc   Section        0  llsshr.o(.text)
    .text                                    0x080003f0   Section        0  depilogue.o(.text)
    .text                                    0x080003f0   Section        0  iusefp.o(.text)
    .text                                    0x080004aa   Section        0  dmul.o(.text)
    .text                                    0x0800058e   Section        0  ddiv.o(.text)
    .text                                    0x0800066c   Section        0  dscalb.o(.text)
    .text                                    0x0800069c   Section       48  cdrcmple.o(.text)
    .text                                    0x080006cc   Section       36  init.o(.text)
    .text                                    0x080006f0   Section        0  dsqrt.o(.text)
    .text                                    0x08000792   Section        0  __dczerorl.o(.text)
    i.ADC_AHB_Clock_Mode_Config              0x080007cc   Section        0  n32g401_adc.o(i.ADC_AHB_Clock_Mode_Config)
    i.ADC_AllInit                            0x080007e0   Section        0  adc.o(i.ADC_AllInit)
    i.ADC_Calibration_Operation              0x080008f0   Section        0  n32g401_adc.o(i.ADC_Calibration_Operation)
    i.ADC_Channel_Sample_Time_Config         0x08000938   Section        0  n32g401_adc.o(i.ADC_Channel_Sample_Time_Config)
    i.ADC_Clock_Mode_Config                  0x080009c4   Section        0  n32g401_adc.o(i.ADC_Clock_Mode_Config)
    i.ADC_Continue_Conversion_Disable        0x080009ec   Section        0  n32g401_adc.o(i.ADC_Continue_Conversion_Disable)
    i.ADC_Continue_Conversion_Enable         0x08000a00   Section        0  n32g401_adc.o(i.ADC_Continue_Conversion_Enable)
    i.ADC_DMA_Transfer_Enable                0x08000a14   Section        0  n32g401_adc.o(i.ADC_DMA_Transfer_Enable)
    i.ADC_Data_Alignment_Config              0x08000a28   Section        0  n32g401_adc.o(i.ADC_Data_Alignment_Config)
    i.ADC_Flag_Status_Get                    0x08000a44   Section        0  n32g401_adc.o(i.ADC_Flag_Status_Get)
    i.ADC_Initializes                        0x08000a70   Section        0  n32g401_adc.o(i.ADC_Initializes)
    i.ADC_Initializes_Structure              0x08000ab4   Section        0  n32g401_adc.o(i.ADC_Initializes_Structure)
    i.ADC_Multichannels_Disable              0x08000ac4   Section        0  n32g401_adc.o(i.ADC_Multichannels_Disable)
    i.ADC_Multichannels_Enable               0x08000ad8   Section        0  n32g401_adc.o(i.ADC_Multichannels_Enable)
    i.ADC_ON                                 0x08000aec   Section        0  n32g401_adc.o(i.ADC_ON)
    i.ADC_PLL_Clock_Mode_Config              0x08000b00   Section        0  n32g401_adc.o(i.ADC_PLL_Clock_Mode_Config)
    i.ADC_Regular_Channels_Number_Config     0x08000b14   Section        0  n32g401_adc.o(i.ADC_Regular_Channels_Number_Config)
    i.ADC_Regular_Channels_Software_Conversion_Operation 0x08000b30   Section        0  n32g401_adc.o(i.ADC_Regular_Channels_Software_Conversion_Operation)
    i.ADC_Regular_Group_External_Trigger_Source_Config 0x08000b78   Section        0  n32g401_adc.o(i.ADC_Regular_Group_External_Trigger_Source_Config)
    i.ADC_Regular_Sequence_Conversion_Number_Config 0x08000b94   Section        0  n32g401_adc.o(i.ADC_Regular_Sequence_Conversion_Number_Config)
    i.ADC_Temperature_Sensor_And_Vrefint_Channel_Enable 0x08000c2c   Section        0  n32g401_adc.o(i.ADC_Temperature_Sensor_And_Vrefint_Channel_Enable)
    i.ALL_ParaInit                           0x08000c54   Section        0  cmd_handle.o(i.ALL_ParaInit)
    i.Adaptation_REG_and_ALGO                0x08000c60   Section        0  VI4302_System.o(i.Adaptation_REG_and_ALGO)
    i.BusFault_Handler                       0x08000d9c   Section        0  n32g401_it.o(i.BusFault_Handler)
    i.CMD_SumNegateCheck                     0x08000da0   Section        0  cmd_handle.o(i.CMD_SumNegateCheck)
    i.DBG_SysTick_Config                     0x08000dc4   Section        0  delay.o(i.DBG_SysTick_Config)
    DBG_SysTick_Config                       0x08000dc5   Thumb Code    86  delay.o(i.DBG_SysTick_Config)
    i.DMA_Buffer_Size_Config                 0x08000e24   Section        0  n32g401_dma.o(i.DMA_Buffer_Size_Config)
    i.DMA_Channel3_IRQHandler                0x08000e28   Section        0  usart.o(i.DMA_Channel3_IRQHandler)
    i.DMA_Channel_Disable                    0x08000ea0   Section        0  n32g401_dma.o(i.DMA_Channel_Disable)
    i.DMA_Channel_Enable                     0x08000eaa   Section        0  n32g401_dma.o(i.DMA_Channel_Enable)
    i.DMA_Channel_Request_Remap              0x08000eb4   Section        0  n32g401_dma.o(i.DMA_Channel_Request_Remap)
    i.DMA_Circular_Mode_Config               0x08000eb8   Section        0  n32g401_dma.o(i.DMA_Circular_Mode_Config)
    i.DMA_Current_Data_Transfer_Number_Get   0x08000ec8   Section        0  n32g401_dma.o(i.DMA_Current_Data_Transfer_Number_Get)
    i.DMA_Destination_Config                 0x08000ed0   Section        0  n32g401_dma.o(i.DMA_Destination_Config)
    i.DMA_Initializes                        0x08000ee0   Section        0  n32g401_dma.o(i.DMA_Initializes)
    i.DMA_Interrupt_Status_Clear             0x08000f40   Section        0  n32g401_dma.o(i.DMA_Interrupt_Status_Clear)
    i.DMA_Interrupt_Status_Get               0x08000f44   Section        0  n32g401_dma.o(i.DMA_Interrupt_Status_Get)
    i.DMA_Interrupts_Enable                  0x08000f54   Section        0  n32g401_dma.o(i.DMA_Interrupts_Enable)
    i.DMA_Memory_2_Memory_Config             0x08000f5c   Section        0  n32g401_dma.o(i.DMA_Memory_2_Memory_Config)
    i.DMA_Memory_Addr_Increment_Config       0x08000f6c   Section        0  n32g401_dma.o(i.DMA_Memory_Addr_Increment_Config)
    i.DMA_Memory_Address_Config              0x08000f7c   Section        0  n32g401_dma.o(i.DMA_Memory_Address_Config)
    i.DMA_Memory_Data_Width_Config           0x08000f80   Section        0  n32g401_dma.o(i.DMA_Memory_Data_Width_Config)
    i.DMA_Peripheral_Addr_Increment_Config   0x08000f90   Section        0  n32g401_dma.o(i.DMA_Peripheral_Addr_Increment_Config)
    i.DMA_Peripheral_Address_Config          0x08000fa0   Section        0  n32g401_dma.o(i.DMA_Peripheral_Address_Config)
    i.DMA_Peripheral_Data_Width_Config       0x08000fa4   Section        0  n32g401_dma.o(i.DMA_Peripheral_Data_Width_Config)
    i.DMA_Priority_Config                    0x08000fb4   Section        0  n32g401_dma.o(i.DMA_Priority_Config)
    i.DMA_Reset                              0x08000fc4   Section        0  n32g401_dma.o(i.DMA_Reset)
    i.DebugMon_Handler                       0x08001074   Section        0  n32g401_it.o(i.DebugMon_Handler)
    i.EXTI0_IRQHandler                       0x08001078   Section        0  main.o(i.EXTI0_IRQHandler)
    i.EXTI2_IRQHandler                       0x080010a4   Section        0  main.o(i.EXTI2_IRQHandler)
    i.EXTI_Interrupt_Status_Clear            0x080010c4   Section        0  n32g401_exti.o(i.EXTI_Interrupt_Status_Clear)
    i.EXTI_Interrupt_Status_Get              0x080010d0   Section        0  n32g401_exti.o(i.EXTI_Interrupt_Status_Get)
    i.EXTI_LineCmd_Disable                   0x080010f0   Section        0  n32g401_exti.o(i.EXTI_LineCmd_Disable)
    i.EXTI_Peripheral_Initializes            0x08001108   Section        0  n32g401_exti.o(i.EXTI_Peripheral_Initializes)
    i.EXTI_Trigger_Config                    0x08001128   Section        0  n32g401_exti.o(i.EXTI_Trigger_Config)
    i.EXTI_Work_Mode_Config                  0x08001170   Section        0  n32g401_exti.o(i.EXTI_Work_Mode_Config)
    i.Execute_instruction                    0x0800119c   Section        0  cmd_handle.o(i.Execute_instruction)
    i.FLASH_Flag_Status_Clear                0x080013e0   Section        0  n32g401_flash.o(i.FLASH_Flag_Status_Clear)
    i.FLASH_Last_Operation_Wait              0x080013f0   Section        0  n32g401_flash.o(i.FLASH_Last_Operation_Wait)
    i.FLASH_Lock                             0x08001418   Section        0  n32g401_flash.o(i.FLASH_Lock)
    i.FLASH_One_Page_Erase                   0x0800142c   Section        0  n32g401_flash.o(i.FLASH_One_Page_Erase)
    i.FLASH_Status_Get                       0x0800147c   Section        0  n32g401_flash.o(i.FLASH_Status_Get)
    i.FLASH_Unlock                           0x080014b0   Section        0  n32g401_flash.o(i.FLASH_Unlock)
    i.FLASH_Word_Program                     0x080014c8   Section        0  n32g401_flash.o(i.FLASH_Word_Program)
    i.GPIO_Alternate_Set                     0x08001518   Section        0  n32g401_gpio.o(i.GPIO_Alternate_Set)
    i.GPIO_Driver_Set                        0x08001558   Section        0  n32g401_gpio.o(i.GPIO_Driver_Set)
    i.GPIO_EXTI_Line_Set                     0x08001574   Section        0  n32g401_gpio.o(i.GPIO_EXTI_Line_Set)
    i.GPIO_Init                              0x080015b0   Section        0  GPIO.o(i.GPIO_Init)
    i.GPIO_Mode_Set                          0x08001668   Section        0  n32g401_gpio.o(i.GPIO_Mode_Set)
    i.GPIO_Peripheral_Initialize             0x080016ac   Section        0  n32g401_gpio.o(i.GPIO_Peripheral_Initialize)
    i.GPIO_Pins_Reset                        0x08001702   Section        0  n32g401_gpio.o(i.GPIO_Pins_Reset)
    i.GPIO_Pins_Set                          0x08001706   Section        0  n32g401_gpio.o(i.GPIO_Pins_Set)
    i.GPIO_Pull_Set                          0x0800170a   Section        0  n32g401_gpio.o(i.GPIO_Pull_Set)
    i.GPIO_SlewRate_Set                      0x08001726   Section        0  n32g401_gpio.o(i.GPIO_SlewRate_Set)
    i.GPIO_Structure_Initialize              0x0800173e   Section        0  n32g401_gpio.o(i.GPIO_Structure_Initialize)
    i.Get_Fw_bytes                           0x08001756   Section        0  fw_44_00_00_80_R00.o(i.Get_Fw_bytes)
    i.Get_NVR_Value                          0x0800175c   Section        0  system_n32g401.o(i.Get_NVR_Value)
    i.Get_Temperature_Rectified_Tof          0x08001784   Section        0  data_handle.o(i.Get_Temperature_Rectified_Tof)
    i.HardFault_Handler                      0x080017ac   Section        0  n32g401_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x080017b0   Section        0  n32g401_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080017b4   Section        0  n32g401_it.o(i.NMI_Handler)
    i.NTC_TempGet                            0x080017e0   Section        0  adc.o(i.NTC_TempGet)
    i.NVIC_Configuration                     0x080018a0   Section        0  GPIO.o(i.NVIC_Configuration)
    i.NVIC_Initializes                       0x080018c4   Section        0  misc.o(i.NVIC_Initializes)
    i.NVIC_Priority_Group_Set                0x08001934   Section        0  misc.o(i.NVIC_Priority_Group_Set)
    i.Noise_To_Confidence                    0x08001948   Section        0  data_handle.o(i.Noise_To_Confidence)
    i.PLL_TrimValueLoad                      0x080019d4   Section        0  system_n32g401.o(i.PLL_TrimValueLoad)
    i.RCC_ADC_1M_Clock_Config                0x08001a54   Section        0  n32g401_rcc.o(i.RCC_ADC_1M_Clock_Config)
    i.RCC_ADC_Hclk_Config                    0x08001a70   Section        0  n32g401_rcc.o(i.RCC_ADC_Hclk_Config)
    i.RCC_ADC_Hclk_Enable                    0x08001a88   Section        0  n32g401_rcc.o(i.RCC_ADC_Hclk_Enable)
    i.RCC_ADC_PLL_Clock_Disable              0x08001aa0   Section        0  n32g401_rcc.o(i.RCC_ADC_PLL_Clock_Disable)
    i.RCC_ADC_PLL_Clock_Prescaler_Enable     0x08001ab4   Section        0  n32g401_rcc.o(i.RCC_ADC_PLL_Clock_Prescaler_Enable)
    i.RCC_AHB_Peripheral_Clock_Enable        0x08001acc   Section        0  n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Enable)
    i.RCC_APB1_Peripheral_Clock_Enable       0x08001adc   Section        0  n32g401_rcc.o(i.RCC_APB1_Peripheral_Clock_Enable)
    i.RCC_APB2_Peripheral_Clock_Enable       0x08001aec   Section        0  n32g401_rcc.o(i.RCC_APB2_Peripheral_Clock_Enable)
    i.RCC_APB2_Peripheral_Reset              0x08001afc   Section        0  n32g401_rcc.o(i.RCC_APB2_Peripheral_Reset)
    i.RCC_Clocks_Frequencies_Value_Get       0x08001b14   Section        0  n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get)
    i.RCC_Flag_Status_Get                    0x08001c30   Section        0  n32g401_rcc.o(i.RCC_Flag_Status_Get)
    i.RCC_HSE_Config                         0x08001c70   Section        0  n32g401_rcc.o(i.RCC_HSE_Config)
    i.RCC_IRQHandler                         0x08001cb0   Section        0  n32g401_it.o(i.RCC_IRQHandler)
    i.RCC_Interrupt_Enable                   0x08001cf0   Section        0  n32g401_rcc.o(i.RCC_Interrupt_Enable)
    i.RCC_Interrupt_Status_Clear             0x08001d00   Section        0  n32g401_rcc.o(i.RCC_Interrupt_Status_Clear)
    i.RCC_Interrupt_Status_Get               0x08001d10   Section        0  n32g401_rcc.o(i.RCC_Interrupt_Status_Get)
    i.RCC_MCO_Source_Config                  0x08001d28   Section        0  n32g401_rcc.o(i.RCC_MCO_Source_Config)
    i.RCC_PLL_Enable                         0x08001d40   Section        0  n32g401_rcc.o(i.RCC_PLL_Enable)
    i.RCC_Sysclk_Config                      0x08001d4c   Section        0  n32g401_rcc.o(i.RCC_Sysclk_Config)
    i.RCO_OutputInit                         0x08001d64   Section        0  GPIO.o(i.RCO_OutputInit)
    i.SPI1_Init                              0x08001dd8   Section        0  spi.o(i.SPI1_Init)
    i.SPI1_ReadNByte                         0x08001ea0   Section        0  spi.o(i.SPI1_ReadNByte)
    i.SPI1_ReadWriteByte                     0x08001ed0   Section        0  spi.o(i.SPI1_ReadWriteByte)
    i.SPI1_ReadWriteNByte                    0x08001f34   Section        0  spi.o(i.SPI1_ReadWriteNByte)
    i.SPI1_WriteNByte                        0x08001f62   Section        0  spi.o(i.SPI1_WriteNByte)
    i.SPI_BaudRatePres_Config                0x08001f8c   Section        0  n32g401_spi.o(i.SPI_BaudRatePres_Config)
    i.SPI_CLKPHA_Config                      0x08001f9c   Section        0  n32g401_spi.o(i.SPI_CLKPHA_Config)
    i.SPI_CLKPOL_Config                      0x08001fac   Section        0  n32g401_spi.o(i.SPI_CLKPOL_Config)
    i.SPI_CRC_Disable                        0x08001fbc   Section        0  n32g401_spi.o(i.SPI_CRC_Disable)
    i.SPI_CRC_Polynomial_Set                 0x08001fc6   Section        0  n32g401_spi.o(i.SPI_CRC_Polynomial_Set)
    i.SPI_DataDirection_Config               0x08001fca   Section        0  n32g401_spi.o(i.SPI_DataDirection_Config)
    i.SPI_DataLen_Config                     0x08001fda   Section        0  n32g401_spi.o(i.SPI_DataLen_Config)
    i.SPI_FirstBit_Config                    0x08001fea   Section        0  n32g401_spi.o(i.SPI_FirstBit_Config)
    i.SPI_I2S_Data_Get                       0x08001ffa   Section        0  n32g401_spi.o(i.SPI_I2S_Data_Get)
    i.SPI_I2S_Data_Transmit                  0x08002000   Section        0  n32g401_spi.o(i.SPI_I2S_Data_Transmit)
    i.SPI_I2S_Flag_Status_Get                0x08002004   Section        0  n32g401_spi.o(i.SPI_I2S_Flag_Status_Get)
    i.SPI_I2S_Reset                          0x08002018   Section        0  n32g401_spi.o(i.SPI_I2S_Reset)
    i.SPI_Initializes                        0x08002044   Section        0  n32g401_spi.o(i.SPI_Initializes)
    i.SPI_Initializes_Structure              0x08002094   Section        0  n32g401_spi.o(i.SPI_Initializes_Structure)
    i.SPI_NSS_Config                         0x080020ac   Section        0  n32g401_spi.o(i.SPI_NSS_Config)
    i.SPI_ON                                 0x080020bc   Section        0  n32g401_spi.o(i.SPI_ON)
    i.SPI_Set_Nss_Level                      0x080020c6   Section        0  n32g401_spi.o(i.SPI_Set_Nss_Level)
    i.SPI_SpiMode_Config                     0x080020e0   Section        0  n32g401_spi.o(i.SPI_SpiMode_Config)
    i.STMFLASH_Read                          0x080020f0   Section        0  flash.o(i.STMFLASH_Read)
    i.STMFLASH_ReadByte                      0x08002110   Section        0  flash.o(i.STMFLASH_ReadByte)
    i.SVC_Handler                            0x08002116   Section        0  n32g401_it.o(i.SVC_Handler)
    .ARM.__AT_0x08002118                     0x08002118   Section        8  system_n32g401.o(.ARM.__AT_0x08002118)
    i.STMFLASH_ReadWord                      0x08002120   Section        0  flash.o(i.STMFLASH_ReadWord)
    i.STMFLASH_Write                         0x08002128   Section        0  flash.o(i.STMFLASH_Write)
    i.SysTick_Handler                        0x08002208   Section        0  n32g401_it.o(i.SysTick_Handler)
    i.System_Clock_Set                       0x0800220c   Section        0  system_n32g401.o(i.System_Clock_Set)
    System_Clock_Set                         0x0800220d   Thumb Code   280  system_n32g401.o(i.System_Clock_Set)
    i.System_Initializes                     0x08002340   Section        0  system_n32g401.o(i.System_Initializes)
    i.TIM6_Configuration                     0x080023f4   Section        0  timer.o(i.TIM6_Configuration)
    i.TIM6_IRQHandler                        0x0800244c   Section        0  main.o(i.TIM6_IRQHandler)
    i.TIM_Base_Auto_Reload_Set               0x08002480   Section        0  n32g401_tim.o(i.TIM_Base_Auto_Reload_Set)
    i.TIM_Base_Channel1                      0x08002484   Section        0  n32g401_tim.o(i.TIM_Base_Channel1)
    i.TIM_Base_Channel2                      0x0800249a   Section        0  n32g401_tim.o(i.TIM_Base_Channel2)
    i.TIM_Base_Channel3                      0x080024b0   Section        0  n32g401_tim.o(i.TIM_Base_Channel3)
    i.TIM_Base_Channel4                      0x080024c6   Section        0  n32g401_tim.o(i.TIM_Base_Channel4)
    i.TIM_Base_Count_Mode_Set                0x080024dc   Section        0  n32g401_tim.o(i.TIM_Base_Count_Mode_Set)
    i.TIM_Base_Initialize                    0x080024ec   Section        0  n32g401_tim.o(i.TIM_Base_Initialize)
    i.TIM_Base_OCrefClear                    0x0800259c   Section        0  n32g401_tim.o(i.TIM_Base_OCrefClear)
    i.TIM_Base_Prescaler_Set                 0x080025b2   Section        0  n32g401_tim.o(i.TIM_Base_Prescaler_Set)
    i.TIM_Base_Reload_Mode_Set               0x080025b6   Section        0  n32g401_tim.o(i.TIM_Base_Reload_Mode_Set)
    i.TIM_Base_Repeat_Count_Set              0x080025ba   Section        0  n32g401_tim.o(i.TIM_Base_Repeat_Count_Set)
    i.TIM_Base_Struct_Initialize             0x080025be   Section        0  n32g401_tim.o(i.TIM_Base_Struct_Initialize)
    i.TIM_Clock_Division_Set                 0x080025dc   Section        0  n32g401_tim.o(i.TIM_Clock_Division_Set)
    i.TIM_Interrupt_Enable                   0x080025ec   Section        0  n32g401_tim.o(i.TIM_Interrupt_Enable)
    i.TIM_Interrupt_Status_Clear             0x080025f4   Section        0  n32g401_tim.o(i.TIM_Interrupt_Status_Clear)
    i.TIM_Interrupt_Status_Get               0x080025fa   Section        0  n32g401_tim.o(i.TIM_Interrupt_Status_Get)
    i.TIM_On                                 0x0800261a   Section        0  n32g401_tim.o(i.TIM_On)
    i.TX_Disen                               0x08002624   Section        0  GPIO.o(i.TX_Disen)
    i.TX_En                                  0x08002638   Section        0  GPIO.o(i.TX_En)
    i.UART2_SendData                         0x0800264c   Section        0  usart.o(i.UART2_SendData)
    i.UART_AllHandle                         0x080026c0   Section        0  cmd_handle.o(i.UART_AllHandle)
    i.UART_CmdAckSend                        0x0800270c   Section        0  cmd_handle.o(i.UART_CmdAckSend)
    i.USART2_DmaConfig                       0x08002790   Section        0  usart.o(i.USART2_DmaConfig)
    i.USART2_Init                            0x08002850   Section        0  usart.o(i.USART2_Init)
    i.USART_DMA_Transfer_Enable              0x08002900   Section        0  n32g401_usart.o(i.USART_DMA_Transfer_Enable)
    i.USART_Data_Receive                     0x08002908   Section        0  n32g401_usart.o(i.USART_Data_Receive)
    i.UsageFault_Handler                     0x08002912   Section        0  n32g401_it.o(i.UsageFault_Handler)
    i.WDT_Init                               0x08002916   Section        0  wdt.o(i.WDT_Init)
    .ARM.__AT_0x08002918                     0x08002918   Section        8  system_n32g401.o(.ARM.__AT_0x08002918)
    i.USART2_IRQHandler                      0x08002920   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART_Baud_Rate_Config                 0x08002a00   Section        0  n32g401_usart.o(i.USART_Baud_Rate_Config)
    i.USART_Enable                           0x08002a80   Section        0  n32g401_usart.o(i.USART_Enable)
    i.USART_Hardware_Flow_Control_Config     0x08002a8a   Section        0  n32g401_usart.o(i.USART_Hardware_Flow_Control_Config)
    i.USART_Initializes                      0x08002a9a   Section        0  n32g401_usart.o(i.USART_Initializes)
    i.USART_Interrput_Enable                 0x08002ad2   Section        0  n32g401_usart.o(i.USART_Interrput_Enable)
    i.USART_Interrupt_Status_Clear           0x08002b08   Section        0  n32g401_usart.o(i.USART_Interrupt_Status_Clear)
    i.USART_Interrupt_Status_Get             0x08002b1c   Section        0  n32g401_usart.o(i.USART_Interrupt_Status_Get)
    i.USART_Mode_Config                      0x08002b66   Section        0  n32g401_usart.o(i.USART_Mode_Config)
    i.USART_Parity_Config                    0x08002b76   Section        0  n32g401_usart.o(i.USART_Parity_Config)
    i.USART_Stop_Bits_Config                 0x08002b86   Section        0  n32g401_usart.o(i.USART_Stop_Bits_Config)
    i.USART_Word_Length_Config               0x08002b96   Section        0  n32g401_usart.o(i.USART_Word_Length_Config)
    i.VI4302_AllInit                         0x08002ba8   Section        0  VI4302_Handle.o(i.VI4302_AllInit)
    i.VI4302_BVD_Calculate                   0x08002ccc   Section        0  VI4302_System.o(i.VI4302_BVD_Calculate)
    i.VI4302_Bvd_Cal                         0x08002dc0   Section        0  VI4302_System.o(i.VI4302_Bvd_Cal)
    i.VI4302_CsHigh                          0x08002e30   Section        0  spi.o(i.VI4302_CsHigh)
    i.VI4302_CsLow                           0x08002e40   Section        0  spi.o(i.VI4302_CsLow)
    i.VI4302_Disen                           0x08002e50   Section        0  GPIO.o(i.VI4302_Disen)
    i.VI4302_En                              0x08002e60   Section        0  GPIO.o(i.VI4302_En)
    i.VI4302_Enable_DcDc                     0x08002e70   Section        0  VI4302_System.o(i.VI4302_Enable_DcDc)
    i.VI4302_Frame_Rate_AutoCtrl             0x08002eac   Section        0  VI4302_System.o(i.VI4302_Frame_Rate_AutoCtrl)
    i.VI4302_Get_Frame_Cnt                   0x08002f1c   Section        0  VI4302_System.o(i.VI4302_Get_Frame_Cnt)
    i.VI4302_InfifoClear                     0x08002f2c   Section        0  uartfifo.o(i.VI4302_InfifoClear)
    i.VI4302_InfifoDataIn                    0x08002f40   Section        0  uartfifo.o(i.VI4302_InfifoDataIn)
    i.VI4302_InfifoDataOut                   0x08002f7c   Section        0  uartfifo.o(i.VI4302_InfifoDataOut)
    i.VI4302_Pin_Init                        0x08002ffc   Section        0  VI4302_System.o(i.VI4302_Pin_Init)
    i.VI4302_Read_His_Config                 0x0800300c   Section        0  VI4302_System.o(i.VI4302_Read_His_Config)
    i.VI4302_Read_Histogram                  0x08003074   Section        0  VI4302_System.o(i.VI4302_Read_Histogram)
    i.VI4302_Reg_Init                        0x08003108   Section        0  VI4302_System.o(i.VI4302_Reg_Init)
    i.VI4302_Set_Bvd                         0x08003188   Section        0  VI4302_System.o(i.VI4302_Set_Bvd)
    i.VI4302_Set_Frame_Data_Format           0x080031ac   Section        0  VI4302_System.o(i.VI4302_Set_Frame_Data_Format)
    i.VI4302_Set_Mp_All                      0x080032ec   Section        0  VI4302_System.o(i.VI4302_Set_Mp_All)
    i.VI4302_Set_Mp_Openonly                 0x0800332c   Section        0  VI4302_System.o(i.VI4302_Set_Mp_Openonly)
    i.VI4302_SinglePixel_Output              0x08003390   Section        0  VI4302_System.o(i.VI4302_SinglePixel_Output)
    i.VI4302_Start_Ranging                   0x080034ec   Section        0  VI4302_System.o(i.VI4302_Start_Ranging)
    i.VI4302_Stop_Ranging                    0x08003550   Section        0  VI4302_System.o(i.VI4302_Stop_Ranging)
    i.VI4302_Stream_On                       0x080035b0   Section        0  VI4302_System.o(i.VI4302_Stream_On)
    i.VI4302_TDC_Cal                         0x08003604   Section        0  VI4302_System.o(i.VI4302_TDC_Cal)
    i.VI4302_Temp_Bvd                        0x08003660   Section        0  VI4302_System.o(i.VI4302_Temp_Bvd)
    i.VI4302_Update_Config                   0x080036bc   Section        0  VI4302_System.o(i.VI4302_Update_Config)
    i.VI4302_Update_Succcess_Fail            0x08003748   Section        0  VI4302_System.o(i.VI4302_Update_Succcess_Fail)
    i.VI4302_Update_firmware                 0x08003784   Section        0  VI4302_System.o(i.VI4302_Update_firmware)
    i.VI4302_Write_Reg                       0x08003834   Section        0  VI4302_System.o(i.VI4302_Write_Reg)
    i.VI4302_read_frame                      0x08003884   Section        0  VI4302_System.o(i.VI4302_read_frame)
    i.WDT_Week                               0x080038fc   Section        0  wdt.o(i.WDT_Week)
    i.XSJ_DealProtocol                       0x08003900   Section        0  cmd_handle.o(i.XSJ_DealProtocol)
    i.XSJ_UART_AllHandle                     0x08003a34   Section        0  cmd_handle.o(i.XSJ_UART_AllHandle)
    i.__ARM_fpclassify                       0x08003ace   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_floor                         0x08003b00   Section        0  floor.o(i.__hardfp_floor)
    i.__hardfp_pow                           0x08003c18   Section        0  pow.o(i.__hardfp_pow)
    i.__kernel_poly                          0x08004868   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x08004960   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x08004990   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x080049a8   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x080049c8   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x080049e8   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x08004a08   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08004a16   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08004a18   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08004a28   Section        0  errno.o(i.__set_errno)
    i.cal_final_tof                          0x08004a34   Section        0  data_handle.o(i.cal_final_tof)
    i.delay_ms                               0x08004b18   Section        0  delay.o(i.delay_ms)
    i.fabs                                   0x08004b66   Section        0  fabs.o(i.fabs)
    i.flash_DataInit                         0x08004b80   Section        0  flash.o(i.flash_DataInit)
    i.flash_FmDataInit                       0x08004bc0   Section        0  flash.o(i.flash_FmDataInit)
    i.flash_ReadConfigerData                 0x08004c94   Section        0  flash.o(i.flash_ReadConfigerData)
    i.flash_ReadFmData                       0x08004cac   Section        0  flash.o(i.flash_ReadFmData)
    i.flash_ReadSnData                       0x08004cc4   Section        0  flash.o(i.flash_ReadSnData)
    i.flash_SaveConfigerData                 0x08004cdc   Section        0  flash.o(i.flash_SaveConfigerData)
    i.flash_SaveFmData                       0x08004cf8   Section        0  flash.o(i.flash_SaveFmData)
    i.flash_SaveSnData                       0x08004d14   Section        0  flash.o(i.flash_SaveSnData)
    i.flash_SnDataInit                       0x08004d30   Section        0  flash.o(i.flash_SnDataInit)
    i.free                                   0x08004d6c   Section        0  malloc.o(i.free)
    i.main                                   0x08004dbc   Section        0  main.o(i.main)
    i.malloc                                 0x08004e20   Section        0  malloc.o(i.malloc)
    i.spi_write_data                         0x08004e8c   Section        0  VI4302_Handle.o(i.spi_write_data)
    i.sqrt                                   0x08004eaa   Section        0  sqrt.o(i.sqrt)
    i.uart1infifo_Clear                      0x08004f18   Section        0  uartfifo.o(i.uart1infifo_Clear)
    i.uart1infifo_DataIn                     0x08004f2c   Section        0  uartfifo.o(i.uart1infifo_DataIn)
    i.uart1infifo_DataOut                    0x08004f68   Section        0  uartfifo.o(i.uart1infifo_DataOut)
    i.uart1infifo_HaveData                   0x08004f8c   Section        0  uartfifo.o(i.uart1infifo_HaveData)
    i.uart1outfifo_Clear                     0x08004fa8   Section        0  uartfifo.o(i.uart1outfifo_Clear)
    i.uart1outfifo_DataIn                    0x08004fbc   Section        0  uartfifo.o(i.uart1outfifo_DataIn)
    i.uart1outfifo_DataOut                   0x0800501c   Section        0  uartfifo.o(i.uart1outfifo_DataOut)
    i.uart1outfifo_HaveData                  0x08005080   Section        0  uartfifo.o(i.uart1outfifo_HaveData)
    i.user_driver_init                       0x0800509c   Section        0  VI4302_Handle.o(i.user_driver_init)
    i.vi4302_HW_set_demo                     0x080050f4   Section        0  VI4302_Handle.o(i.vi4302_HW_set_demo)
    i.vi4302_read_his_reg                    0x08005108   Section        0  VI4302_Handle.o(i.vi4302_read_his_reg)
    i.vi4302_read_mul_reg                    0x08005138   Section        0  VI4302_Handle.o(i.vi4302_read_mul_reg)
    i.vi4302_read_ranging_data_with_firmware 0x0800517e   Section        0  VI4302_Handle.o(i.vi4302_read_ranging_data_with_firmware)
    i.vi4302_read_register                   0x080051b8   Section        0  VI4302_Handle.o(i.vi4302_read_register)
    i.vi4302_read_status_from_firmware       0x080051f4   Section        0  VI4302_System.o(i.vi4302_read_status_from_firmware)
    vi4302_read_status_from_firmware         0x080051f5   Thumb Code    56  VI4302_System.o(i.vi4302_read_status_from_firmware)
    i.vi4302_write_cmd                       0x08005230   Section        0  VI4302_Handle.o(i.vi4302_write_cmd)
    i.vi4302_write_register                  0x08005254   Section        0  VI4302_Handle.o(i.vi4302_write_register)
    i.vis_tof_compensation                   0x08005298   Section        0  data_handle.o(i.vis_tof_compensation)
    .constdata                               0x08005b58   Section     1048  adc.o(.constdata)
    .constdata                               0x08005f70   Section      984  flash.o(.constdata)
    .constdata                               0x08006348   Section       64  n32g401_rcc.o(.constdata)
    APBAHBPresTable                          0x08006348   Data          16  n32g401_rcc.o(.constdata)
    ADCHCLKPresTable                         0x08006358   Data          16  n32g401_rcc.o(.constdata)
    ADCPLLCLKPresTable                       0x08006368   Data          32  n32g401_rcc.o(.constdata)
    .constdata                               0x08006388   Section     7956  fw_44_00_00_80_R00.o(.constdata)
    .constdata                               0x0800829c   Section      140  VI4302_Config.o(.constdata)
    .constdata                               0x08008328   Section        4  VI4302_Handle.o(.constdata)
    .constdata                               0x0800832c   Section       20  cmd_handle.o(.constdata)
    .constdata                               0x08008340   Section      136  pow.o(.constdata)
    bp                                       0x08008340   Data          16  pow.o(.constdata)
    dp_h                                     0x08008350   Data          16  pow.o(.constdata)
    dp_l                                     0x08008360   Data          16  pow.o(.constdata)
    L                                        0x08008370   Data          48  pow.o(.constdata)
    P                                        0x080083a0   Data          40  pow.o(.constdata)
    .constdata                               0x080083c8   Section        8  qnan.o(.constdata)
    .data                                    0x20000000   Section        1  usart.o(.data)
    .data                                    0x20000004   Section        4  system_n32g401.o(.data)
    .data                                    0x20000008   Section       24  User_Driver.o(.data)
    .data                                    0x20000020   Section        5  VI4302_Handle.o(.data)
    .data                                    0x20000026   Section       12  VI4302_System.o(.data)
    .data                                    0x20000032   Section        6  data_handle.o(.data)
    .data                                    0x20000038   Section     2051  cmd_handle.o(.data)
    pixel                                    0x2000083a   Data           1  cmd_handle.o(.data)
    .data                                    0x2000083c   Section       14  uartfifo.o(.data)
    .data                                    0x2000084a   Section        2  main.o(.data)
    ms_count                                 0x2000084a   Data           2  main.o(.data)
    .data                                    0x2000084c   Section        4  mvars.o(.data)
    .data                                    0x20000850   Section        4  mvars.o(.data)
    .data                                    0x20000854   Section        4  errno.o(.data)
    _errno                                   0x20000854   Data           4  errno.o(.data)
    .bss                                     0x20000858   Section       40  adc.o(.bss)
    .bss                                     0x20000880   Section      840  flash.o(.bss)
    .bss                                     0x20000bc8   Section      510  usart.o(.bss)
    .bss                                     0x20000dc6   Section       12  cmd_handle.o(.bss)
    .bss                                     0x20000dd2   Section     4351  uartfifo.o(.bss)
    HEAP                                     0x20001ed8   Section      768  startup_n32g401.o(HEAP)
    STACK                                    0x200021d8   Section     4096  startup_n32g401.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000114   Number         0  startup_n32g401.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_n32g401.o(RESET)
    __Vectors_End                            0x08000114   Data           0  startup_n32g401.o(RESET)
    __main                                   0x08000115   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000115   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000119   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x0800011d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x0800011d   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x0800011d   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x0800011d   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000125   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000125   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000129   Thumb Code     8  startup_n32g401.o(.text)
    PendSV_Handler                           0x0800013f   Thumb Code     2  startup_n32g401.o(.text)
    ADC_IRQHandler                           0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    COMP_1_2_3_IRQHandler                    0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    DMA_Channel1_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    DMA_Channel2_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    DMA_Channel4_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    DMA_Channel5_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    DMA_Channel6_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    DMA_Channel7_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    DMA_Channel8_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    EXTI15_10_IRQHandler                     0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    EXTI1_IRQHandler                         0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    EXTI3_IRQHandler                         0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    EXTI4_IRQHandler                         0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    EXTI9_5_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    FLASH_IRQHandler                         0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    I2C1_ER_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    I2C1_EV_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    I2C2_ER_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    I2C2_EV_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    LPTIM_WKUP_IRQHandler                    0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    MMU_IRQHandler                           0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    PVD_IRQHandler                           0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    RTCAlarm_IRQHandler                      0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    RTC_TAMPER_STAMP_IRQHandler              0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    RTC_WKUP_IRQHandler                      0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    SPI1_IRQHandler                          0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    SPI2_IRQHandler                          0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM1_BRK_IRQHandler                      0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM1_CC_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM1_UP_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM2_IRQHandler                          0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM3_IRQHandler                          0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM4_IRQHandler                          0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM5_IRQHandler                          0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM8_BRK_IRQHandler                      0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM8_CC_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    TIM8_UP_IRQHandler                       0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    UART3_IRQHandler                         0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    UART4_IRQHandler                         0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    USART1_IRQHandler                        0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    WWDG_IRQHandler                          0x08000143   Thumb Code     0  startup_n32g401.o(.text)
    __aeabi_memcpy                           0x0800014d   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800014d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800014d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000171   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000171   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000171   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800017f   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800017f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800017f   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000183   Thumb Code    18  memseta.o(.text)
    __aeabi_dadd                             0x08000195   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080002d7   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080002dd   Thumb Code     6  dadd.o(.text)
    __aeabi_i2d                              0x080002e3   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x08000305   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2iz                             0x0800031f   Thumb Code    62  dfixi.o(.text)
    __aeabi_d2uiz                            0x0800035d   Thumb Code    50  dfixui.o(.text)
    __aeabi_llsl                             0x0800038f   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800038f   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080003ad   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080003ad   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080003cd   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080003cd   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x080003f1   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x080003f1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800040f   Thumb Code   156  depilogue.o(.text)
    __aeabi_dmul                             0x080004ab   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x0800058f   Thumb Code   222  ddiv.o(.text)
    __ARM_scalbn                             0x0800066d   Thumb Code    46  dscalb.o(.text)
    scalbn                                   0x0800066d   Thumb Code     0  dscalb.o(.text)
    __aeabi_cdrcmple                         0x0800069d   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080006cd   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080006cd   Thumb Code     0  init.o(.text)
    _dsqrt                                   0x080006f1   Thumb Code   162  dsqrt.o(.text)
    __decompress                             0x08000793   Thumb Code     0  __dczerorl.o(.text)
    __decompress0                            0x08000793   Thumb Code    58  __dczerorl.o(.text)
    ADC_AHB_Clock_Mode_Config                0x080007cd   Thumb Code    14  n32g401_adc.o(i.ADC_AHB_Clock_Mode_Config)
    ADC_AllInit                              0x080007e1   Thumb Code   254  adc.o(i.ADC_AllInit)
    ADC_Calibration_Operation                0x080008f1   Thumb Code    68  n32g401_adc.o(i.ADC_Calibration_Operation)
    ADC_Channel_Sample_Time_Config           0x08000939   Thumb Code   134  n32g401_adc.o(i.ADC_Channel_Sample_Time_Config)
    ADC_Clock_Mode_Config                    0x080009c5   Thumb Code    40  n32g401_adc.o(i.ADC_Clock_Mode_Config)
    ADC_Continue_Conversion_Disable          0x080009ed   Thumb Code    14  n32g401_adc.o(i.ADC_Continue_Conversion_Disable)
    ADC_Continue_Conversion_Enable           0x08000a01   Thumb Code    14  n32g401_adc.o(i.ADC_Continue_Conversion_Enable)
    ADC_DMA_Transfer_Enable                  0x08000a15   Thumb Code    14  n32g401_adc.o(i.ADC_DMA_Transfer_Enable)
    ADC_Data_Alignment_Config                0x08000a29   Thumb Code    22  n32g401_adc.o(i.ADC_Data_Alignment_Config)
    ADC_Flag_Status_Get                      0x08000a45   Thumb Code    40  n32g401_adc.o(i.ADC_Flag_Status_Get)
    ADC_Initializes                          0x08000a71   Thumb Code    64  n32g401_adc.o(i.ADC_Initializes)
    ADC_Initializes_Structure                0x08000ab5   Thumb Code    14  n32g401_adc.o(i.ADC_Initializes_Structure)
    ADC_Multichannels_Disable                0x08000ac5   Thumb Code    14  n32g401_adc.o(i.ADC_Multichannels_Disable)
    ADC_Multichannels_Enable                 0x08000ad9   Thumb Code    14  n32g401_adc.o(i.ADC_Multichannels_Enable)
    ADC_ON                                   0x08000aed   Thumb Code    14  n32g401_adc.o(i.ADC_ON)
    ADC_PLL_Clock_Mode_Config                0x08000b01   Thumb Code    14  n32g401_adc.o(i.ADC_PLL_Clock_Mode_Config)
    ADC_Regular_Channels_Number_Config       0x08000b15   Thumb Code    22  n32g401_adc.o(i.ADC_Regular_Channels_Number_Config)
    ADC_Regular_Channels_Software_Conversion_Operation 0x08000b31   Thumb Code    66  n32g401_adc.o(i.ADC_Regular_Channels_Software_Conversion_Operation)
    ADC_Regular_Group_External_Trigger_Source_Config 0x08000b79   Thumb Code    22  n32g401_adc.o(i.ADC_Regular_Group_External_Trigger_Source_Config)
    ADC_Regular_Sequence_Conversion_Number_Config 0x08000b95   Thumb Code   148  n32g401_adc.o(i.ADC_Regular_Sequence_Conversion_Number_Config)
    ADC_Temperature_Sensor_And_Vrefint_Channel_Enable 0x08000c2d   Thumb Code    30  n32g401_adc.o(i.ADC_Temperature_Sensor_And_Vrefint_Channel_Enable)
    ALL_ParaInit                             0x08000c55   Thumb Code     8  cmd_handle.o(i.ALL_ParaInit)
    Adaptation_REG_and_ALGO                  0x08000c61   Thumb Code   304  VI4302_System.o(i.Adaptation_REG_and_ALGO)
    BusFault_Handler                         0x08000d9d   Thumb Code     4  n32g401_it.o(i.BusFault_Handler)
    CMD_SumNegateCheck                       0x08000da1   Thumb Code    34  cmd_handle.o(i.CMD_SumNegateCheck)
    DMA_Buffer_Size_Config                   0x08000e25   Thumb Code     4  n32g401_dma.o(i.DMA_Buffer_Size_Config)
    DMA_Channel3_IRQHandler                  0x08000e29   Thumb Code   106  usart.o(i.DMA_Channel3_IRQHandler)
    DMA_Channel_Disable                      0x08000ea1   Thumb Code    10  n32g401_dma.o(i.DMA_Channel_Disable)
    DMA_Channel_Enable                       0x08000eab   Thumb Code    10  n32g401_dma.o(i.DMA_Channel_Enable)
    DMA_Channel_Request_Remap                0x08000eb5   Thumb Code     4  n32g401_dma.o(i.DMA_Channel_Request_Remap)
    DMA_Circular_Mode_Config                 0x08000eb9   Thumb Code    16  n32g401_dma.o(i.DMA_Circular_Mode_Config)
    DMA_Current_Data_Transfer_Number_Get     0x08000ec9   Thumb Code     8  n32g401_dma.o(i.DMA_Current_Data_Transfer_Number_Get)
    DMA_Destination_Config                   0x08000ed1   Thumb Code    16  n32g401_dma.o(i.DMA_Destination_Config)
    DMA_Initializes                          0x08000ee1   Thumb Code    96  n32g401_dma.o(i.DMA_Initializes)
    DMA_Interrupt_Status_Clear               0x08000f41   Thumb Code     4  n32g401_dma.o(i.DMA_Interrupt_Status_Clear)
    DMA_Interrupt_Status_Get                 0x08000f45   Thumb Code    16  n32g401_dma.o(i.DMA_Interrupt_Status_Get)
    DMA_Interrupts_Enable                    0x08000f55   Thumb Code     8  n32g401_dma.o(i.DMA_Interrupts_Enable)
    DMA_Memory_2_Memory_Config               0x08000f5d   Thumb Code    16  n32g401_dma.o(i.DMA_Memory_2_Memory_Config)
    DMA_Memory_Addr_Increment_Config         0x08000f6d   Thumb Code    16  n32g401_dma.o(i.DMA_Memory_Addr_Increment_Config)
    DMA_Memory_Address_Config                0x08000f7d   Thumb Code     4  n32g401_dma.o(i.DMA_Memory_Address_Config)
    DMA_Memory_Data_Width_Config             0x08000f81   Thumb Code    16  n32g401_dma.o(i.DMA_Memory_Data_Width_Config)
    DMA_Peripheral_Addr_Increment_Config     0x08000f91   Thumb Code    16  n32g401_dma.o(i.DMA_Peripheral_Addr_Increment_Config)
    DMA_Peripheral_Address_Config            0x08000fa1   Thumb Code     4  n32g401_dma.o(i.DMA_Peripheral_Address_Config)
    DMA_Peripheral_Data_Width_Config         0x08000fa5   Thumb Code    16  n32g401_dma.o(i.DMA_Peripheral_Data_Width_Config)
    DMA_Priority_Config                      0x08000fb5   Thumb Code    16  n32g401_dma.o(i.DMA_Priority_Config)
    DMA_Reset                                0x08000fc5   Thumb Code   172  n32g401_dma.o(i.DMA_Reset)
    DebugMon_Handler                         0x08001075   Thumb Code     2  n32g401_it.o(i.DebugMon_Handler)
    EXTI0_IRQHandler                         0x08001079   Thumb Code    34  main.o(i.EXTI0_IRQHandler)
    EXTI2_IRQHandler                         0x080010a5   Thumb Code    28  main.o(i.EXTI2_IRQHandler)
    EXTI_Interrupt_Status_Clear              0x080010c5   Thumb Code     6  n32g401_exti.o(i.EXTI_Interrupt_Status_Clear)
    EXTI_Interrupt_Status_Get                0x080010d1   Thumb Code    28  n32g401_exti.o(i.EXTI_Interrupt_Status_Get)
    EXTI_LineCmd_Disable                     0x080010f1   Thumb Code    20  n32g401_exti.o(i.EXTI_LineCmd_Disable)
    EXTI_Peripheral_Initializes              0x08001109   Thumb Code    32  n32g401_exti.o(i.EXTI_Peripheral_Initializes)
    EXTI_Trigger_Config                      0x08001129   Thumb Code    68  n32g401_exti.o(i.EXTI_Trigger_Config)
    EXTI_Work_Mode_Config                    0x08001171   Thumb Code    38  n32g401_exti.o(i.EXTI_Work_Mode_Config)
    Execute_instruction                      0x0800119d   Thumb Code   534  cmd_handle.o(i.Execute_instruction)
    FLASH_Flag_Status_Clear                  0x080013e1   Thumb Code    12  n32g401_flash.o(i.FLASH_Flag_Status_Clear)
    FLASH_Last_Operation_Wait                0x080013f1   Thumb Code    38  n32g401_flash.o(i.FLASH_Last_Operation_Wait)
    FLASH_Lock                               0x08001419   Thumb Code    14  n32g401_flash.o(i.FLASH_Lock)
    FLASH_One_Page_Erase                     0x0800142d   Thumb Code    76  n32g401_flash.o(i.FLASH_One_Page_Erase)
    FLASH_Status_Get                         0x0800147d   Thumb Code    48  n32g401_flash.o(i.FLASH_Status_Get)
    FLASH_Unlock                             0x080014b1   Thumb Code    12  n32g401_flash.o(i.FLASH_Unlock)
    FLASH_Word_Program                       0x080014c9   Thumb Code    76  n32g401_flash.o(i.FLASH_Word_Program)
    GPIO_Alternate_Set                       0x08001519   Thumb Code    64  n32g401_gpio.o(i.GPIO_Alternate_Set)
    GPIO_Driver_Set                          0x08001559   Thumb Code    28  n32g401_gpio.o(i.GPIO_Driver_Set)
    GPIO_EXTI_Line_Set                       0x08001575   Thumb Code    56  n32g401_gpio.o(i.GPIO_EXTI_Line_Set)
    GPIO_Init                                0x080015b1   Thumb Code   174  GPIO.o(i.GPIO_Init)
    GPIO_Mode_Set                            0x08001669   Thumb Code    68  n32g401_gpio.o(i.GPIO_Mode_Set)
    GPIO_Peripheral_Initialize               0x080016ad   Thumb Code    86  n32g401_gpio.o(i.GPIO_Peripheral_Initialize)
    GPIO_Pins_Reset                          0x08001703   Thumb Code     4  n32g401_gpio.o(i.GPIO_Pins_Reset)
    GPIO_Pins_Set                            0x08001707   Thumb Code     4  n32g401_gpio.o(i.GPIO_Pins_Set)
    GPIO_Pull_Set                            0x0800170b   Thumb Code    28  n32g401_gpio.o(i.GPIO_Pull_Set)
    GPIO_SlewRate_Set                        0x08001727   Thumb Code    24  n32g401_gpio.o(i.GPIO_SlewRate_Set)
    GPIO_Structure_Initialize                0x0800173f   Thumb Code    24  n32g401_gpio.o(i.GPIO_Structure_Initialize)
    Get_Fw_bytes                             0x08001757   Thumb Code     6  fw_44_00_00_80_R00.o(i.Get_Fw_bytes)
    Get_NVR_Value                            0x0800175d   Thumb Code    28  system_n32g401.o(i.Get_NVR_Value)
    Get_Temperature_Rectified_Tof            0x08001785   Thumb Code    34  data_handle.o(i.Get_Temperature_Rectified_Tof)
    HardFault_Handler                        0x080017ad   Thumb Code     4  n32g401_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x080017b1   Thumb Code     4  n32g401_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080017b5   Thumb Code    44  n32g401_it.o(i.NMI_Handler)
    NTC_TempGet                              0x080017e1   Thumb Code   182  adc.o(i.NTC_TempGet)
    NVIC_Configuration                       0x080018a1   Thumb Code    34  GPIO.o(i.NVIC_Configuration)
    NVIC_Initializes                         0x080018c5   Thumb Code   100  misc.o(i.NVIC_Initializes)
    NVIC_Priority_Group_Set                  0x08001935   Thumb Code    10  misc.o(i.NVIC_Priority_Group_Set)
    Noise_To_Confidence                      0x08001949   Thumb Code   138  data_handle.o(i.Noise_To_Confidence)
    PLL_TrimValueLoad                        0x080019d5   Thumb Code   112  system_n32g401.o(i.PLL_TrimValueLoad)
    RCC_ADC_1M_Clock_Config                  0x08001a55   Thumb Code    24  n32g401_rcc.o(i.RCC_ADC_1M_Clock_Config)
    RCC_ADC_Hclk_Config                      0x08001a71   Thumb Code    18  n32g401_rcc.o(i.RCC_ADC_Hclk_Config)
    RCC_ADC_Hclk_Enable                      0x08001a89   Thumb Code    18  n32g401_rcc.o(i.RCC_ADC_Hclk_Enable)
    RCC_ADC_PLL_Clock_Disable                0x08001aa1   Thumb Code    14  n32g401_rcc.o(i.RCC_ADC_PLL_Clock_Disable)
    RCC_ADC_PLL_Clock_Prescaler_Enable       0x08001ab5   Thumb Code    18  n32g401_rcc.o(i.RCC_ADC_PLL_Clock_Prescaler_Enable)
    RCC_AHB_Peripheral_Clock_Enable          0x08001acd   Thumb Code    12  n32g401_rcc.o(i.RCC_AHB_Peripheral_Clock_Enable)
    RCC_APB1_Peripheral_Clock_Enable         0x08001add   Thumb Code    12  n32g401_rcc.o(i.RCC_APB1_Peripheral_Clock_Enable)
    RCC_APB2_Peripheral_Clock_Enable         0x08001aed   Thumb Code    12  n32g401_rcc.o(i.RCC_APB2_Peripheral_Clock_Enable)
    RCC_APB2_Peripheral_Reset                0x08001afd   Thumb Code    20  n32g401_rcc.o(i.RCC_APB2_Peripheral_Reset)
    RCC_Clocks_Frequencies_Value_Get         0x08001b15   Thumb Code   250  n32g401_rcc.o(i.RCC_Clocks_Frequencies_Value_Get)
    RCC_Flag_Status_Get                      0x08001c31   Thumb Code    58  n32g401_rcc.o(i.RCC_Flag_Status_Get)
    RCC_HSE_Config                           0x08001c71   Thumb Code    60  n32g401_rcc.o(i.RCC_HSE_Config)
    RCC_IRQHandler                           0x08001cb1   Thumb Code    62  n32g401_it.o(i.RCC_IRQHandler)
    RCC_Interrupt_Enable                     0x08001cf1   Thumb Code    12  n32g401_rcc.o(i.RCC_Interrupt_Enable)
    RCC_Interrupt_Status_Clear               0x08001d01   Thumb Code    12  n32g401_rcc.o(i.RCC_Interrupt_Status_Clear)
    RCC_Interrupt_Status_Get                 0x08001d11   Thumb Code    18  n32g401_rcc.o(i.RCC_Interrupt_Status_Get)
    RCC_MCO_Source_Config                    0x08001d29   Thumb Code    18  n32g401_rcc.o(i.RCC_MCO_Source_Config)
    RCC_PLL_Enable                           0x08001d41   Thumb Code     8  n32g401_rcc.o(i.RCC_PLL_Enable)
    RCC_Sysclk_Config                        0x08001d4d   Thumb Code    18  n32g401_rcc.o(i.RCC_Sysclk_Config)
    RCO_OutputInit                           0x08001d65   Thumb Code   108  GPIO.o(i.RCO_OutputInit)
    SPI1_Init                                0x08001dd9   Thumb Code   190  spi.o(i.SPI1_Init)
    SPI1_ReadNByte                           0x08001ea1   Thumb Code    46  spi.o(i.SPI1_ReadNByte)
    SPI1_ReadWriteByte                       0x08001ed1   Thumb Code    96  spi.o(i.SPI1_ReadWriteByte)
    SPI1_ReadWriteNByte                      0x08001f35   Thumb Code    46  spi.o(i.SPI1_ReadWriteNByte)
    SPI1_WriteNByte                          0x08001f63   Thumb Code    42  spi.o(i.SPI1_WriteNByte)
    SPI_BaudRatePres_Config                  0x08001f8d   Thumb Code    16  n32g401_spi.o(i.SPI_BaudRatePres_Config)
    SPI_CLKPHA_Config                        0x08001f9d   Thumb Code    16  n32g401_spi.o(i.SPI_CLKPHA_Config)
    SPI_CLKPOL_Config                        0x08001fad   Thumb Code    16  n32g401_spi.o(i.SPI_CLKPOL_Config)
    SPI_CRC_Disable                          0x08001fbd   Thumb Code    10  n32g401_spi.o(i.SPI_CRC_Disable)
    SPI_CRC_Polynomial_Set                   0x08001fc7   Thumb Code     4  n32g401_spi.o(i.SPI_CRC_Polynomial_Set)
    SPI_DataDirection_Config                 0x08001fcb   Thumb Code    16  n32g401_spi.o(i.SPI_DataDirection_Config)
    SPI_DataLen_Config                       0x08001fdb   Thumb Code    16  n32g401_spi.o(i.SPI_DataLen_Config)
    SPI_FirstBit_Config                      0x08001feb   Thumb Code    16  n32g401_spi.o(i.SPI_FirstBit_Config)
    SPI_I2S_Data_Get                         0x08001ffb   Thumb Code     6  n32g401_spi.o(i.SPI_I2S_Data_Get)
    SPI_I2S_Data_Transmit                    0x08002001   Thumb Code     4  n32g401_spi.o(i.SPI_I2S_Data_Transmit)
    SPI_I2S_Flag_Status_Get                  0x08002005   Thumb Code    18  n32g401_spi.o(i.SPI_I2S_Flag_Status_Get)
    SPI_I2S_Reset                            0x08002019   Thumb Code    34  n32g401_spi.o(i.SPI_I2S_Reset)
    SPI_Initializes                          0x08002045   Thumb Code    80  n32g401_spi.o(i.SPI_Initializes)
    SPI_Initializes_Structure                0x08002095   Thumb Code    24  n32g401_spi.o(i.SPI_Initializes_Structure)
    SPI_NSS_Config                           0x080020ad   Thumb Code    16  n32g401_spi.o(i.SPI_NSS_Config)
    SPI_ON                                   0x080020bd   Thumb Code    10  n32g401_spi.o(i.SPI_ON)
    SPI_Set_Nss_Level                        0x080020c7   Thumb Code    26  n32g401_spi.o(i.SPI_Set_Nss_Level)
    SPI_SpiMode_Config                       0x080020e1   Thumb Code    16  n32g401_spi.o(i.SPI_SpiMode_Config)
    STMFLASH_Read                            0x080020f1   Thumb Code    32  flash.o(i.STMFLASH_Read)
    STMFLASH_ReadByte                        0x08002111   Thumb Code     6  flash.o(i.STMFLASH_ReadByte)
    SVC_Handler                              0x08002117   Thumb Code     2  n32g401_it.o(i.SVC_Handler)
    const_data1                              0x08002118   Data           8  system_n32g401.o(.ARM.__AT_0x08002118)
    STMFLASH_ReadWord                        0x08002121   Thumb Code     6  flash.o(i.STMFLASH_ReadWord)
    STMFLASH_Write                           0x08002129   Thumb Code   218  flash.o(i.STMFLASH_Write)
    SysTick_Handler                          0x08002209   Thumb Code     2  n32g401_it.o(i.SysTick_Handler)
    System_Initializes                       0x08002341   Thumb Code   150  system_n32g401.o(i.System_Initializes)
    TIM6_Configuration                       0x080023f5   Thumb Code    82  timer.o(i.TIM6_Configuration)
    TIM6_IRQHandler                          0x0800244d   Thumb Code    44  main.o(i.TIM6_IRQHandler)
    TIM_Base_Auto_Reload_Set                 0x08002481   Thumb Code     4  n32g401_tim.o(i.TIM_Base_Auto_Reload_Set)
    TIM_Base_Channel1                        0x08002485   Thumb Code    22  n32g401_tim.o(i.TIM_Base_Channel1)
    TIM_Base_Channel2                        0x0800249b   Thumb Code    22  n32g401_tim.o(i.TIM_Base_Channel2)
    TIM_Base_Channel3                        0x080024b1   Thumb Code    22  n32g401_tim.o(i.TIM_Base_Channel3)
    TIM_Base_Channel4                        0x080024c7   Thumb Code    22  n32g401_tim.o(i.TIM_Base_Channel4)
    TIM_Base_Count_Mode_Set                  0x080024dd   Thumb Code    16  n32g401_tim.o(i.TIM_Base_Count_Mode_Set)
    TIM_Base_Initialize                      0x080024ed   Thumb Code   156  n32g401_tim.o(i.TIM_Base_Initialize)
    TIM_Base_OCrefClear                      0x0800259d   Thumb Code    22  n32g401_tim.o(i.TIM_Base_OCrefClear)
    TIM_Base_Prescaler_Set                   0x080025b3   Thumb Code     4  n32g401_tim.o(i.TIM_Base_Prescaler_Set)
    TIM_Base_Reload_Mode_Set                 0x080025b7   Thumb Code     4  n32g401_tim.o(i.TIM_Base_Reload_Mode_Set)
    TIM_Base_Repeat_Count_Set                0x080025bb   Thumb Code     4  n32g401_tim.o(i.TIM_Base_Repeat_Count_Set)
    TIM_Base_Struct_Initialize               0x080025bf   Thumb Code    30  n32g401_tim.o(i.TIM_Base_Struct_Initialize)
    TIM_Clock_Division_Set                   0x080025dd   Thumb Code    16  n32g401_tim.o(i.TIM_Clock_Division_Set)
    TIM_Interrupt_Enable                     0x080025ed   Thumb Code     8  n32g401_tim.o(i.TIM_Interrupt_Enable)
    TIM_Interrupt_Status_Clear               0x080025f5   Thumb Code     6  n32g401_tim.o(i.TIM_Interrupt_Status_Clear)
    TIM_Interrupt_Status_Get                 0x080025fb   Thumb Code    32  n32g401_tim.o(i.TIM_Interrupt_Status_Get)
    TIM_On                                   0x0800261b   Thumb Code    10  n32g401_tim.o(i.TIM_On)
    TX_Disen                                 0x08002625   Thumb Code    14  GPIO.o(i.TX_Disen)
    TX_En                                    0x08002639   Thumb Code    14  GPIO.o(i.TX_En)
    UART2_SendData                           0x0800264d   Thumb Code   102  usart.o(i.UART2_SendData)
    UART_AllHandle                           0x080026c1   Thumb Code    76  cmd_handle.o(i.UART_AllHandle)
    UART_CmdAckSend                          0x0800270d   Thumb Code   126  cmd_handle.o(i.UART_CmdAckSend)
    USART2_DmaConfig                         0x08002791   Thumb Code   176  usart.o(i.USART2_DmaConfig)
    USART2_Init                              0x08002851   Thumb Code   166  usart.o(i.USART2_Init)
    USART_DMA_Transfer_Enable                0x08002901   Thumb Code     8  n32g401_usart.o(i.USART_DMA_Transfer_Enable)
    USART_Data_Receive                       0x08002909   Thumb Code    10  n32g401_usart.o(i.USART_Data_Receive)
    UsageFault_Handler                       0x08002913   Thumb Code     4  n32g401_it.o(i.UsageFault_Handler)
    WDT_Init                                 0x08002917   Thumb Code     2  wdt.o(i.WDT_Init)
    const_data2                              0x08002918   Data           8  system_n32g401.o(.ARM.__AT_0x08002918)
    USART2_IRQHandler                        0x08002921   Thumb Code   212  usart.o(i.USART2_IRQHandler)
    USART_Baud_Rate_Config                   0x08002a01   Thumb Code   114  n32g401_usart.o(i.USART_Baud_Rate_Config)
    USART_Enable                             0x08002a81   Thumb Code    10  n32g401_usart.o(i.USART_Enable)
    USART_Hardware_Flow_Control_Config       0x08002a8b   Thumb Code    16  n32g401_usart.o(i.USART_Hardware_Flow_Control_Config)
    USART_Initializes                        0x08002a9b   Thumb Code    56  n32g401_usart.o(i.USART_Initializes)
    USART_Interrput_Enable                   0x08002ad3   Thumb Code    54  n32g401_usart.o(i.USART_Interrput_Enable)
    USART_Interrupt_Status_Clear             0x08002b09   Thumb Code    20  n32g401_usart.o(i.USART_Interrupt_Status_Clear)
    USART_Interrupt_Status_Get               0x08002b1d   Thumb Code    74  n32g401_usart.o(i.USART_Interrupt_Status_Get)
    USART_Mode_Config                        0x08002b67   Thumb Code    16  n32g401_usart.o(i.USART_Mode_Config)
    USART_Parity_Config                      0x08002b77   Thumb Code    16  n32g401_usart.o(i.USART_Parity_Config)
    USART_Stop_Bits_Config                   0x08002b87   Thumb Code    16  n32g401_usart.o(i.USART_Stop_Bits_Config)
    USART_Word_Length_Config                 0x08002b97   Thumb Code    16  n32g401_usart.o(i.USART_Word_Length_Config)
    VI4302_AllInit                           0x08002ba9   Thumb Code   268  VI4302_Handle.o(i.VI4302_AllInit)
    VI4302_BVD_Calculate                     0x08002ccd   Thumb Code   236  VI4302_System.o(i.VI4302_BVD_Calculate)
    VI4302_Bvd_Cal                           0x08002dc1   Thumb Code    98  VI4302_System.o(i.VI4302_Bvd_Cal)
    VI4302_CsHigh                            0x08002e31   Thumb Code    12  spi.o(i.VI4302_CsHigh)
    VI4302_CsLow                             0x08002e41   Thumb Code    12  spi.o(i.VI4302_CsLow)
    VI4302_Disen                             0x08002e51   Thumb Code    12  GPIO.o(i.VI4302_Disen)
    VI4302_En                                0x08002e61   Thumb Code    12  GPIO.o(i.VI4302_En)
    VI4302_Enable_DcDc                       0x08002e71   Thumb Code    52  VI4302_System.o(i.VI4302_Enable_DcDc)
    VI4302_Frame_Rate_AutoCtrl               0x08002ead   Thumb Code    98  VI4302_System.o(i.VI4302_Frame_Rate_AutoCtrl)
    VI4302_Get_Frame_Cnt                     0x08002f1d   Thumb Code    12  VI4302_System.o(i.VI4302_Get_Frame_Cnt)
    VI4302_InfifoClear                       0x08002f2d   Thumb Code    12  uartfifo.o(i.VI4302_InfifoClear)
    VI4302_InfifoDataIn                      0x08002f41   Thumb Code    46  uartfifo.o(i.VI4302_InfifoDataIn)
    VI4302_InfifoDataOut                     0x08002f7d   Thumb Code   112  uartfifo.o(i.VI4302_InfifoDataOut)
    VI4302_Pin_Init                          0x08002ffd   Thumb Code    10  VI4302_System.o(i.VI4302_Pin_Init)
    VI4302_Read_His_Config                   0x0800300d   Thumb Code    92  VI4302_System.o(i.VI4302_Read_His_Config)
    VI4302_Read_Histogram                    0x08003075   Thumb Code   140  VI4302_System.o(i.VI4302_Read_Histogram)
    VI4302_Reg_Init                          0x08003109   Thumb Code   116  VI4302_System.o(i.VI4302_Reg_Init)
    VI4302_Set_Bvd                           0x08003189   Thumb Code    32  VI4302_System.o(i.VI4302_Set_Bvd)
    VI4302_Set_Frame_Data_Format             0x080031ad   Thumb Code   312  VI4302_System.o(i.VI4302_Set_Frame_Data_Format)
    VI4302_Set_Mp_All                        0x080032ed   Thumb Code    60  VI4302_System.o(i.VI4302_Set_Mp_All)
    VI4302_Set_Mp_Openonly                   0x0800332d   Thumb Code    94  VI4302_System.o(i.VI4302_Set_Mp_Openonly)
    VI4302_SinglePixel_Output                0x08003391   Thumb Code   338  VI4302_System.o(i.VI4302_SinglePixel_Output)
    VI4302_Start_Ranging                     0x080034ed   Thumb Code    82  VI4302_System.o(i.VI4302_Start_Ranging)
    VI4302_Stop_Ranging                      0x08003551   Thumb Code    78  VI4302_System.o(i.VI4302_Stop_Ranging)
    VI4302_Stream_On                         0x080035b1   Thumb Code    76  VI4302_System.o(i.VI4302_Stream_On)
    VI4302_TDC_Cal                           0x08003605   Thumb Code    78  VI4302_System.o(i.VI4302_TDC_Cal)
    VI4302_Temp_Bvd                          0x08003661   Thumb Code    82  VI4302_System.o(i.VI4302_Temp_Bvd)
    VI4302_Update_Config                     0x080036bd   Thumb Code   128  VI4302_System.o(i.VI4302_Update_Config)
    VI4302_Update_Succcess_Fail              0x08003749   Thumb Code    54  VI4302_System.o(i.VI4302_Update_Succcess_Fail)
    VI4302_Update_firmware                   0x08003785   Thumb Code   172  VI4302_System.o(i.VI4302_Update_firmware)
    VI4302_Write_Reg                         0x08003835   Thumb Code    70  VI4302_System.o(i.VI4302_Write_Reg)
    VI4302_read_frame                        0x08003885   Thumb Code   110  VI4302_System.o(i.VI4302_read_frame)
    WDT_Week                                 0x080038fd   Thumb Code     2  wdt.o(i.WDT_Week)
    XSJ_DealProtocol                         0x08003901   Thumb Code   290  cmd_handle.o(i.XSJ_DealProtocol)
    XSJ_UART_AllHandle                       0x08003a35   Thumb Code   154  cmd_handle.o(i.XSJ_UART_AllHandle)
    __ARM_fpclassify                         0x08003acf   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_floor                           0x08003b01   Thumb Code   252  floor.o(i.__hardfp_floor)
    __hardfp_pow                             0x08003c19   Thumb Code  3072  pow.o(i.__hardfp_pow)
    __kernel_poly                            0x08004869   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x08004961   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x08004991   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x080049a9   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x080049c9   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x080049e9   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x08004a09   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08004a17   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08004a19   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08004a29   Thumb Code     6  errno.o(i.__set_errno)
    cal_final_tof                            0x08004a35   Thumb Code   224  data_handle.o(i.cal_final_tof)
    delay_ms                                 0x08004b19   Thumb Code    78  delay.o(i.delay_ms)
    fabs                                     0x08004b67   Thumb Code    24  fabs.o(i.fabs)
    flash_DataInit                           0x08004b81   Thumb Code    58  flash.o(i.flash_DataInit)
    flash_FmDataInit                         0x08004bc1   Thumb Code   186  flash.o(i.flash_FmDataInit)
    flash_ReadConfigerData                   0x08004c95   Thumb Code    14  flash.o(i.flash_ReadConfigerData)
    flash_ReadFmData                         0x08004cad   Thumb Code    16  flash.o(i.flash_ReadFmData)
    flash_ReadSnData                         0x08004cc5   Thumb Code    14  flash.o(i.flash_ReadSnData)
    flash_SaveConfigerData                   0x08004cdd   Thumb Code    18  flash.o(i.flash_SaveConfigerData)
    flash_SaveFmData                         0x08004cf9   Thumb Code    20  flash.o(i.flash_SaveFmData)
    flash_SaveSnData                         0x08004d15   Thumb Code    18  flash.o(i.flash_SaveSnData)
    flash_SnDataInit                         0x08004d31   Thumb Code    54  flash.o(i.flash_SnDataInit)
    free                                     0x08004d6d   Thumb Code    76  malloc.o(i.free)
    main                                     0x08004dbd   Thumb Code    94  main.o(i.main)
    malloc                                   0x08004e21   Thumb Code    92  malloc.o(i.malloc)
    spi_write_data                           0x08004e8d   Thumb Code    30  VI4302_Handle.o(i.spi_write_data)
    sqrt                                     0x08004eab   Thumb Code   110  sqrt.o(i.sqrt)
    uart1infifo_Clear                        0x08004f19   Thumb Code    12  uartfifo.o(i.uart1infifo_Clear)
    uart1infifo_DataIn                       0x08004f2d   Thumb Code    50  uartfifo.o(i.uart1infifo_DataIn)
    uart1infifo_DataOut                      0x08004f69   Thumb Code    28  uartfifo.o(i.uart1infifo_DataOut)
    uart1infifo_HaveData                     0x08004f8d   Thumb Code    20  uartfifo.o(i.uart1infifo_HaveData)
    uart1outfifo_Clear                       0x08004fa9   Thumb Code    12  uartfifo.o(i.uart1outfifo_Clear)
    uart1outfifo_DataIn                      0x08004fbd   Thumb Code    86  uartfifo.o(i.uart1outfifo_DataIn)
    uart1outfifo_DataOut                     0x0800501d   Thumb Code    92  uartfifo.o(i.uart1outfifo_DataOut)
    uart1outfifo_HaveData                    0x08005081   Thumb Code    20  uartfifo.o(i.uart1outfifo_HaveData)
    user_driver_init                         0x0800509d   Thumb Code    38  VI4302_Handle.o(i.user_driver_init)
    vi4302_HW_set_demo                       0x080050f5   Thumb Code    20  VI4302_Handle.o(i.vi4302_HW_set_demo)
    vi4302_read_his_reg                      0x08005109   Thumb Code    48  VI4302_Handle.o(i.vi4302_read_his_reg)
    vi4302_read_mul_reg                      0x08005139   Thumb Code    70  VI4302_Handle.o(i.vi4302_read_mul_reg)
    vi4302_read_ranging_data_with_firmware   0x0800517f   Thumb Code    58  VI4302_Handle.o(i.vi4302_read_ranging_data_with_firmware)
    vi4302_read_register                     0x080051b9   Thumb Code    58  VI4302_Handle.o(i.vi4302_read_register)
    vi4302_write_cmd                         0x08005231   Thumb Code    34  VI4302_Handle.o(i.vi4302_write_cmd)
    vi4302_write_register                    0x08005255   Thumb Code    64  VI4302_Handle.o(i.vi4302_write_register)
    vis_tof_compensation                     0x08005299   Thumb Code  2236  data_handle.o(i.vis_tof_compensation)
    NTC_ResgistBuff                          0x08005b58   Data        1048  adc.o(.constdata)
    VI4302_NORMAL_QUANJU_PARA                0x08005f70   Data          10  flash.o(.constdata)
    VI4302_ATT_QUANJU_PARA                   0x08005f7a   Data           8  flash.o(.constdata)
    VI4302_NORMAL_PARA                       0x08005f82   Data         230  flash.o(.constdata)
    VI4302_ATT_PARA                          0x08006068   Data         736  flash.o(.constdata)
    vis_sensor_fw                            0x08006388   Data        7956  fw_44_00_00_80_R00.o(.constdata)
    S_regist_buffer_default                  0x0800829c   Data         100  VI4302_Config.o(.constdata)
    R_regist_buffer_default                  0x08008300   Data          40  VI4302_Config.o(.constdata)
    BANBEN_NumberBuff                        0x0800832c   Data           7  cmd_handle.o(.constdata)
    __mathlib_zero                           0x080083c8   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x080083d0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080083f0   Number         0  anon$$obj.o(Region$$Table)
    UART2_SendFlag                           0x20000000   Data           1  usart.o(.data)
    SystemClockFrequency                     0x20000004   Data           4  system_n32g401.o(.data)
    Chip_En                                  0x20000008   Data           4  User_Driver.o(.data)
    Spi_Read_Reg                             0x2000000c   Data           4  User_Driver.o(.data)
    Spi_Write_Reg                            0x20000010   Data           4  User_Driver.o(.data)
    Spi_Read_Mul_Reg                         0x20000014   Data           4  User_Driver.o(.data)
    Spi_Send_Cmd                             0x20000018   Data           4  User_Driver.o(.data)
    Spi_Write_Mul_Reg                        0x2000001c   Data           4  User_Driver.o(.data)
    SPI_Send_Complete                        0x20000020   Data           1  VI4302_Handle.o(.data)
    SPI_Receive_Complete                     0x20000021   Data           1  VI4302_Handle.o(.data)
    gpio0_int_cnt                            0x20000022   Data           1  VI4302_Handle.o(.data)
    EXTI_FLAG                                0x20000023   Data           1  VI4302_Handle.o(.data)
    test                                     0x20000024   Data           1  VI4302_Handle.o(.data)
    VSM_e                                    0x20000026   Data           1  VI4302_System.o(.data)
    Bvd_Cal                                  0x20000027   Data           3  VI4302_System.o(.data)
    Al_Pa                                    0x2000002a   Data           8  VI4302_System.o(.data)
    MuL_TOF                                  0x20000032   Data           6  data_handle.o(.data)
    histogram_data                           0x20000038   Data        2048  cmd_handle.o(.data)
    opt_code                                 0x20000838   Data           1  cmd_handle.o(.data)
    histogram_type                           0x20000839   Data           1  cmd_handle.o(.data)
    uart1infifo_rear                         0x2000083c   Data           2  uartfifo.o(.data)
    uart1infifo_front                        0x2000083e   Data           2  uartfifo.o(.data)
    uart1outfifo_rear                        0x20000840   Data           2  uartfifo.o(.data)
    uart1outfifo_front                       0x20000842   Data           2  uartfifo.o(.data)
    vi4302_infifo_buffer                     0x20000844   Data           4  uartfifo.o(.data)
    vi4302_infifo_rear                       0x20000848   Data           1  uartfifo.o(.data)
    vi4302_infifo_flag                       0x20000849   Data           1  uartfifo.o(.data)
    __microlib_freelist                      0x2000084c   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x20000850   Data           4  mvars.o(.data)
    g_ADCBufferSize                          0x20000858   Data          40  adc.o(.bss)
    g_snNeedSaved                            0x20000880   Data           9  flash.o(.bss)
    g_dataNeedSaved                          0x2000088a   Data          12  flash.o(.bss)
    g_fmNeedSaved                            0x20000896   Data         818  flash.o(.bss)
    UART2_RecieveBuff                        0x20000bc8   Data         255  usart.o(.bss)
    UART2_SendBuff                           0x20000cc7   Data         255  usart.o(.bss)
    g_ControlPara                            0x20000dc6   Data          12  cmd_handle.o(.bss)
    uart1infifo_buffer                       0x20000dd2   Data         255  uartfifo.o(.bss)
    uart1outfifo_buffer                      0x20000ed1   Data        4096  uartfifo.o(.bss)
    __heap_base                              0x20001ed8   Data           0  startup_n32g401.o(HEAP)
    __heap_limit                             0x200021d8   Data           0  startup_n32g401.o(HEAP)
    __initial_sp                             0x200031d8   Data           0  startup_n32g401.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000115

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008c48, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x0000840c])

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x000083f0, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000114   Data   RO          593    RESET               startup_n32g401.o
    0x08000114   0x00000000   Code   RO         6636  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000114   0x00000004   Code   RO         6720    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000118   0x00000004   Code   RO         6723    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x0800011c   0x00000000   Code   RO         6725    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x0800011c   0x00000000   Code   RO         6727    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x0800011c   0x00000008   Code   RO         6728    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000124   0x00000000   Code   RO         6730    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000124   0x00000000   Code   RO         6732    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000124   0x00000004   Code   RO         6721    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000128   0x00000024   Code   RO          594    .text               startup_n32g401.o
    0x0800014c   0x00000024   Code   RO         6639    .text               mc_w.l(memcpya.o)
    0x08000170   0x00000024   Code   RO         6641    .text               mc_w.l(memseta.o)
    0x08000194   0x0000014e   Code   RO         6673    .text               mf_w.l(dadd.o)
    0x080002e2   0x00000022   Code   RO         6675    .text               mf_w.l(dflti.o)
    0x08000304   0x0000001a   Code   RO         6677    .text               mf_w.l(dfltui.o)
    0x0800031e   0x0000003e   Code   RO         6679    .text               mf_w.l(dfixi.o)
    0x0800035c   0x00000032   Code   RO         6681    .text               mf_w.l(dfixui.o)
    0x0800038e   0x0000001e   Code   RO         6734    .text               mc_w.l(llshl.o)
    0x080003ac   0x00000020   Code   RO         6736    .text               mc_w.l(llushr.o)
    0x080003cc   0x00000024   Code   RO         6738    .text               mc_w.l(llsshr.o)
    0x080003f0   0x00000000   Code   RO         6749    .text               mc_w.l(iusefp.o)
    0x080003f0   0x000000ba   Code   RO         6750    .text               mf_w.l(depilogue.o)
    0x080004aa   0x000000e4   Code   RO         6752    .text               mf_w.l(dmul.o)
    0x0800058e   0x000000de   Code   RO         6754    .text               mf_w.l(ddiv.o)
    0x0800066c   0x0000002e   Code   RO         6758    .text               mf_w.l(dscalb.o)
    0x0800069a   0x00000002   PAD
    0x0800069c   0x00000030   Code   RO         6760    .text               mf_w.l(cdrcmple.o)
    0x080006cc   0x00000024   Code   RO         6762    .text               mc_w.l(init.o)
    0x080006f0   0x000000a2   Code   RO         6764    .text               mf_w.l(dsqrt.o)
    0x08000792   0x0000003a   Code   RO         6776    .text               mc_w.l(__dczerorl.o)
    0x080007cc   0x00000014   Code   RO          730    i.ADC_AHB_Clock_Mode_Config  n32g401_adc.o
    0x080007e0   0x00000110   Code   RO          165    i.ADC_AllInit       adc.o
    0x080008f0   0x00000048   Code   RO          740    i.ADC_Calibration_Operation  n32g401_adc.o
    0x08000938   0x0000008c   Code   RO          741    i.ADC_Channel_Sample_Time_Config  n32g401_adc.o
    0x080009c4   0x00000028   Code   RO          742    i.ADC_Clock_Mode_Config  n32g401_adc.o
    0x080009ec   0x00000014   Code   RO          743    i.ADC_Continue_Conversion_Disable  n32g401_adc.o
    0x08000a00   0x00000014   Code   RO          744    i.ADC_Continue_Conversion_Enable  n32g401_adc.o
    0x08000a14   0x00000014   Code   RO          746    i.ADC_DMA_Transfer_Enable  n32g401_adc.o
    0x08000a28   0x0000001c   Code   RO          747    i.ADC_Data_Alignment_Config  n32g401_adc.o
    0x08000a44   0x0000002c   Code   RO          758    i.ADC_Flag_Status_Get  n32g401_adc.o
    0x08000a70   0x00000044   Code   RO          761    i.ADC_Initializes   n32g401_adc.o
    0x08000ab4   0x0000000e   Code   RO          763    i.ADC_Initializes_Structure  n32g401_adc.o
    0x08000ac2   0x00000002   PAD
    0x08000ac4   0x00000014   Code   RO          775    i.ADC_Multichannels_Disable  n32g401_adc.o
    0x08000ad8   0x00000014   Code   RO          776    i.ADC_Multichannels_Enable  n32g401_adc.o
    0x08000aec   0x00000014   Code   RO          778    i.ADC_ON            n32g401_adc.o
    0x08000b00   0x00000014   Code   RO          779    i.ADC_PLL_Clock_Mode_Config  n32g401_adc.o
    0x08000b14   0x0000001c   Code   RO          780    i.ADC_Regular_Channels_Number_Config  n32g401_adc.o
    0x08000b30   0x00000048   Code   RO          781    i.ADC_Regular_Channels_Software_Conversion_Operation  n32g401_adc.o
    0x08000b78   0x0000001c   Code   RO          783    i.ADC_Regular_Group_External_Trigger_Source_Config  n32g401_adc.o
    0x08000b94   0x00000098   Code   RO          784    i.ADC_Regular_Sequence_Conversion_Number_Config  n32g401_adc.o
    0x08000c2c   0x00000028   Code   RO          788    i.ADC_Temperature_Sensor_And_Vrefint_Channel_Enable  n32g401_adc.o
    0x08000c54   0x0000000c   Code   RO         6212    i.ALL_ParaInit      cmd_handle.o
    0x08000c60   0x0000013c   Code   RO         5932    i.Adaptation_REG_and_ALGO  VI4302_System.o
    0x08000d9c   0x00000004   Code   RO         6544    i.BusFault_Handler  n32g401_it.o
    0x08000da0   0x00000022   Code   RO         6214    i.CMD_SumNegateCheck  cmd_handle.o
    0x08000dc2   0x00000002   PAD
    0x08000dc4   0x00000060   Code   RO          234    i.DBG_SysTick_Config  delay.o
    0x08000e24   0x00000004   Code   RO         1561    i.DMA_Buffer_Size_Config  n32g401_dma.o
    0x08000e28   0x00000078   Code   RO          497    i.DMA_Channel3_IRQHandler  usart.o
    0x08000ea0   0x0000000a   Code   RO         1562    i.DMA_Channel_Disable  n32g401_dma.o
    0x08000eaa   0x0000000a   Code   RO         1563    i.DMA_Channel_Enable  n32g401_dma.o
    0x08000eb4   0x00000004   Code   RO         1564    i.DMA_Channel_Request_Remap  n32g401_dma.o
    0x08000eb8   0x00000010   Code   RO         1565    i.DMA_Circular_Mode_Config  n32g401_dma.o
    0x08000ec8   0x00000008   Code   RO         1566    i.DMA_Current_Data_Transfer_Number_Get  n32g401_dma.o
    0x08000ed0   0x00000010   Code   RO         1568    i.DMA_Destination_Config  n32g401_dma.o
    0x08000ee0   0x00000060   Code   RO         1571    i.DMA_Initializes   n32g401_dma.o
    0x08000f40   0x00000004   Code   RO         1572    i.DMA_Interrupt_Status_Clear  n32g401_dma.o
    0x08000f44   0x00000010   Code   RO         1573    i.DMA_Interrupt_Status_Get  n32g401_dma.o
    0x08000f54   0x00000008   Code   RO         1575    i.DMA_Interrupts_Enable  n32g401_dma.o
    0x08000f5c   0x00000010   Code   RO         1576    i.DMA_Memory_2_Memory_Config  n32g401_dma.o
    0x08000f6c   0x00000010   Code   RO         1577    i.DMA_Memory_Addr_Increment_Config  n32g401_dma.o
    0x08000f7c   0x00000004   Code   RO         1578    i.DMA_Memory_Address_Config  n32g401_dma.o
    0x08000f80   0x00000010   Code   RO         1579    i.DMA_Memory_Data_Width_Config  n32g401_dma.o
    0x08000f90   0x00000010   Code   RO         1580    i.DMA_Peripheral_Addr_Increment_Config  n32g401_dma.o
    0x08000fa0   0x00000004   Code   RO         1581    i.DMA_Peripheral_Address_Config  n32g401_dma.o
    0x08000fa4   0x00000010   Code   RO         1582    i.DMA_Peripheral_Data_Width_Config  n32g401_dma.o
    0x08000fb4   0x00000010   Code   RO         1583    i.DMA_Priority_Config  n32g401_dma.o
    0x08000fc4   0x000000b0   Code   RO         1584    i.DMA_Reset         n32g401_dma.o
    0x08001074   0x00000002   Code   RO         6545    i.DebugMon_Handler  n32g401_it.o
    0x08001076   0x00000002   PAD
    0x08001078   0x0000002c   Code   RO         6495    i.EXTI0_IRQHandler  main.o
    0x080010a4   0x00000020   Code   RO         6496    i.EXTI2_IRQHandler  main.o
    0x080010c4   0x0000000c   Code   RO         1731    i.EXTI_Interrupt_Status_Clear  n32g401_exti.o
    0x080010d0   0x00000020   Code   RO         1732    i.EXTI_Interrupt_Status_Get  n32g401_exti.o
    0x080010f0   0x00000018   Code   RO         1733    i.EXTI_LineCmd_Disable  n32g401_exti.o
    0x08001108   0x00000020   Code   RO         1734    i.EXTI_Peripheral_Initializes  n32g401_exti.o
    0x08001128   0x00000048   Code   RO         1739    i.EXTI_Trigger_Config  n32g401_exti.o
    0x08001170   0x0000002c   Code   RO         1740    i.EXTI_Work_Mode_Config  n32g401_exti.o
    0x0800119c   0x00000244   Code   RO         6215    i.Execute_instruction  cmd_handle.o
    0x080013e0   0x00000010   Code   RO         1823    i.FLASH_Flag_Status_Clear  n32g401_flash.o
    0x080013f0   0x00000026   Code   RO         1831    i.FLASH_Last_Operation_Wait  n32g401_flash.o
    0x08001416   0x00000002   PAD
    0x08001418   0x00000014   Code   RO         1834    i.FLASH_Lock        n32g401_flash.o
    0x0800142c   0x00000050   Code   RO         1836    i.FLASH_One_Page_Erase  n32g401_flash.o
    0x0800147c   0x00000034   Code   RO         1855    i.FLASH_Status_Get  n32g401_flash.o
    0x080014b0   0x00000018   Code   RO         1856    i.FLASH_Unlock      n32g401_flash.o
    0x080014c8   0x00000050   Code   RO         1857    i.FLASH_Word_Program  n32g401_flash.o
    0x08001518   0x00000040   Code   RO         2111    i.GPIO_Alternate_Set  n32g401_gpio.o
    0x08001558   0x0000001c   Code   RO         2112    i.GPIO_Driver_Set   n32g401_gpio.o
    0x08001574   0x0000003c   Code   RO         2113    i.GPIO_EXTI_Line_Set  n32g401_gpio.o
    0x080015b0   0x000000b8   Code   RO            4    i.GPIO_Init         GPIO.o
    0x08001668   0x00000044   Code   RO         2116    i.GPIO_Mode_Set     n32g401_gpio.o
    0x080016ac   0x00000056   Code   RO         2121    i.GPIO_Peripheral_Initialize  n32g401_gpio.o
    0x08001702   0x00000004   Code   RO         2125    i.GPIO_Pins_Reset   n32g401_gpio.o
    0x08001706   0x00000004   Code   RO         2126    i.GPIO_Pins_Set     n32g401_gpio.o
    0x0800170a   0x0000001c   Code   RO         2127    i.GPIO_Pull_Set     n32g401_gpio.o
    0x08001726   0x00000018   Code   RO         2129    i.GPIO_SlewRate_Set  n32g401_gpio.o
    0x0800173e   0x00000018   Code   RO         2130    i.GPIO_Structure_Initialize  n32g401_gpio.o
    0x08001756   0x00000006   Code   RO         5729    i.Get_Fw_bytes      fw_44_00_00_80_R00.o
    0x0800175c   0x00000028   Code   RO          601    i.Get_NVR_Value     system_n32g401.o
    0x08001784   0x00000028   Code   RO         6141    i.Get_Temperature_Rectified_Tof  data_handle.o
    0x080017ac   0x00000004   Code   RO         6546    i.HardFault_Handler  n32g401_it.o
    0x080017b0   0x00000004   Code   RO         6547    i.MemManage_Handler  n32g401_it.o
    0x080017b4   0x0000002c   Code   RO         6548    i.NMI_Handler       n32g401_it.o
    0x080017e0   0x000000c0   Code   RO          166    i.NTC_TempGet       adc.o
    0x080018a0   0x00000022   Code   RO            5    i.NVIC_Configuration  GPIO.o
    0x080018c2   0x00000002   PAD
    0x080018c4   0x00000070   Code   RO          676    i.NVIC_Initializes  misc.o
    0x08001934   0x00000014   Code   RO          677    i.NVIC_Priority_Group_Set  misc.o
    0x08001948   0x0000008a   Code   RO         6142    i.Noise_To_Confidence  data_handle.o
    0x080019d2   0x00000002   PAD
    0x080019d4   0x00000080   Code   RO          602    i.PLL_TrimValueLoad  system_n32g401.o
    0x08001a54   0x0000001c   Code   RO         3214    i.RCC_ADC_1M_Clock_Config  n32g401_rcc.o
    0x08001a70   0x00000018   Code   RO         3215    i.RCC_ADC_Hclk_Config  n32g401_rcc.o
    0x08001a88   0x00000018   Code   RO         3217    i.RCC_ADC_Hclk_Enable  n32g401_rcc.o
    0x08001aa0   0x00000014   Code   RO         3218    i.RCC_ADC_PLL_Clock_Disable  n32g401_rcc.o
    0x08001ab4   0x00000018   Code   RO         3219    i.RCC_ADC_PLL_Clock_Prescaler_Enable  n32g401_rcc.o
    0x08001acc   0x00000010   Code   RO         3221    i.RCC_AHB_Peripheral_Clock_Enable  n32g401_rcc.o
    0x08001adc   0x00000010   Code   RO         3224    i.RCC_APB1_Peripheral_Clock_Enable  n32g401_rcc.o
    0x08001aec   0x00000010   Code   RO         3227    i.RCC_APB2_Peripheral_Clock_Enable  n32g401_rcc.o
    0x08001afc   0x00000018   Code   RO         3228    i.RCC_APB2_Peripheral_Reset  n32g401_rcc.o
    0x08001b14   0x0000011c   Code   RO         3232    i.RCC_Clocks_Frequencies_Value_Get  n32g401_rcc.o
    0x08001c30   0x00000040   Code   RO         3233    i.RCC_Flag_Status_Get  n32g401_rcc.o
    0x08001c70   0x00000040   Code   RO         3234    i.RCC_HSE_Config    n32g401_rcc.o
    0x08001cb0   0x0000003e   Code   RO         6549    i.RCC_IRQHandler    n32g401_it.o
    0x08001cee   0x00000002   PAD
    0x08001cf0   0x00000010   Code   RO         3242    i.RCC_Interrupt_Enable  n32g401_rcc.o
    0x08001d00   0x00000010   Code   RO         3243    i.RCC_Interrupt_Status_Clear  n32g401_rcc.o
    0x08001d10   0x00000018   Code   RO         3244    i.RCC_Interrupt_Status_Get  n32g401_rcc.o
    0x08001d28   0x00000018   Code   RO         3259    i.RCC_MCO_Source_Config  n32g401_rcc.o
    0x08001d40   0x0000000c   Code   RO         3262    i.RCC_PLL_Enable    n32g401_rcc.o
    0x08001d4c   0x00000018   Code   RO         3270    i.RCC_Sysclk_Config  n32g401_rcc.o
    0x08001d64   0x00000074   Code   RO            6    i.RCO_OutputInit    GPIO.o
    0x08001dd8   0x000000c8   Code   RO          404    i.SPI1_Init         spi.o
    0x08001ea0   0x0000002e   Code   RO          405    i.SPI1_ReadNByte    spi.o
    0x08001ece   0x00000002   PAD
    0x08001ed0   0x00000064   Code   RO          406    i.SPI1_ReadWriteByte  spi.o
    0x08001f34   0x0000002e   Code   RO          407    i.SPI1_ReadWriteNByte  spi.o
    0x08001f62   0x0000002a   Code   RO          408    i.SPI1_WriteNByte   spi.o
    0x08001f8c   0x00000010   Code   RO         4029    i.SPI_BaudRatePres_Config  n32g401_spi.o
    0x08001f9c   0x00000010   Code   RO         4030    i.SPI_CLKPHA_Config  n32g401_spi.o
    0x08001fac   0x00000010   Code   RO         4031    i.SPI_CLKPOL_Config  n32g401_spi.o
    0x08001fbc   0x0000000a   Code   RO         4033    i.SPI_CRC_Disable   n32g401_spi.o
    0x08001fc6   0x00000004   Code   RO         4036    i.SPI_CRC_Polynomial_Set  n32g401_spi.o
    0x08001fca   0x00000010   Code   RO         4037    i.SPI_DataDirection_Config  n32g401_spi.o
    0x08001fda   0x00000010   Code   RO         4038    i.SPI_DataLen_Config  n32g401_spi.o
    0x08001fea   0x00000010   Code   RO         4039    i.SPI_FirstBit_Config  n32g401_spi.o
    0x08001ffa   0x00000006   Code   RO         4043    i.SPI_I2S_Data_Get  n32g401_spi.o
    0x08002000   0x00000004   Code   RO         4044    i.SPI_I2S_Data_Transmit  n32g401_spi.o
    0x08002004   0x00000012   Code   RO         4045    i.SPI_I2S_Flag_Status_Get  n32g401_spi.o
    0x08002016   0x00000002   PAD
    0x08002018   0x0000002c   Code   RO         4050    i.SPI_I2S_Reset     n32g401_spi.o
    0x08002044   0x00000050   Code   RO         4051    i.SPI_Initializes   n32g401_spi.o
    0x08002094   0x00000018   Code   RO         4052    i.SPI_Initializes_Structure  n32g401_spi.o
    0x080020ac   0x00000010   Code   RO         4053    i.SPI_NSS_Config    n32g401_spi.o
    0x080020bc   0x0000000a   Code   RO         4056    i.SPI_ON            n32g401_spi.o
    0x080020c6   0x0000001a   Code   RO         4059    i.SPI_Set_Nss_Level  n32g401_spi.o
    0x080020e0   0x00000010   Code   RO         4060    i.SPI_SpiMode_Config  n32g401_spi.o
    0x080020f0   0x00000020   Code   RO          292    i.STMFLASH_Read     flash.o
    0x08002110   0x00000006   Code   RO          293    i.STMFLASH_ReadByte  flash.o
    0x08002116   0x00000002   Code   RO         6550    i.SVC_Handler       n32g401_it.o
    0x08002118   0x00000008   Data   RO          606    .ARM.__AT_0x08002118  system_n32g401.o
    0x08002120   0x00000006   Code   RO          295    i.STMFLASH_ReadWord  flash.o
    0x08002126   0x00000002   PAD
    0x08002128   0x000000e0   Code   RO          296    i.STMFLASH_Write    flash.o
    0x08002208   0x00000002   Code   RO         6551    i.SysTick_Handler   n32g401_it.o
    0x0800220a   0x00000002   PAD
    0x0800220c   0x00000134   Code   RO          604    i.System_Clock_Set  system_n32g401.o
    0x08002340   0x000000b4   Code   RO          605    i.System_Initializes  system_n32g401.o
    0x080023f4   0x00000058   Code   RO          467    i.TIM6_Configuration  timer.o
    0x0800244c   0x00000034   Code   RO         6498    i.TIM6_IRQHandler   main.o
    0x08002480   0x00000004   Code   RO         4303    i.TIM_Base_Auto_Reload_Set  n32g401_tim.o
    0x08002484   0x00000016   Code   RO         4305    i.TIM_Base_Channel1  n32g401_tim.o
    0x0800249a   0x00000016   Code   RO         4306    i.TIM_Base_Channel2  n32g401_tim.o
    0x080024b0   0x00000016   Code   RO         4307    i.TIM_Base_Channel3  n32g401_tim.o
    0x080024c6   0x00000016   Code   RO         4308    i.TIM_Base_Channel4  n32g401_tim.o
    0x080024dc   0x00000010   Code   RO         4310    i.TIM_Base_Count_Mode_Set  n32g401_tim.o
    0x080024ec   0x000000b0   Code   RO         4312    i.TIM_Base_Initialize  n32g401_tim.o
    0x0800259c   0x00000016   Code   RO         4313    i.TIM_Base_OCrefClear  n32g401_tim.o
    0x080025b2   0x00000004   Code   RO         4315    i.TIM_Base_Prescaler_Set  n32g401_tim.o
    0x080025b6   0x00000004   Code   RO         4316    i.TIM_Base_Reload_Mode_Set  n32g401_tim.o
    0x080025ba   0x00000004   Code   RO         4317    i.TIM_Base_Repeat_Count_Set  n32g401_tim.o
    0x080025be   0x0000001e   Code   RO         4318    i.TIM_Base_Struct_Initialize  n32g401_tim.o
    0x080025dc   0x00000010   Code   RO         4343    i.TIM_Clock_Division_Set  n32g401_tim.o
    0x080025ec   0x00000008   Code   RO         4402    i.TIM_Interrupt_Enable  n32g401_tim.o
    0x080025f4   0x00000006   Code   RO         4403    i.TIM_Interrupt_Status_Clear  n32g401_tim.o
    0x080025fa   0x00000020   Code   RO         4404    i.TIM_Interrupt_Status_Get  n32g401_tim.o
    0x0800261a   0x0000000a   Code   RO         4417    i.TIM_On            n32g401_tim.o
    0x08002624   0x00000014   Code   RO            8    i.TX_Disen          GPIO.o
    0x08002638   0x00000014   Code   RO            9    i.TX_En             GPIO.o
    0x0800264c   0x00000074   Code   RO          498    i.UART2_SendData    usart.o
    0x080026c0   0x0000004c   Code   RO         6218    i.UART_AllHandle    cmd_handle.o
    0x0800270c   0x00000084   Code   RO         6219    i.UART_CmdAckSend   cmd_handle.o
    0x08002790   0x000000c0   Code   RO          499    i.USART2_DmaConfig  usart.o
    0x08002850   0x000000b0   Code   RO          501    i.USART2_Init       usart.o
    0x08002900   0x00000008   Code   RO         5333    i.USART_DMA_Transfer_Enable  n32g401_usart.o
    0x08002908   0x0000000a   Code   RO         5334    i.USART_Data_Receive  n32g401_usart.o
    0x08002912   0x00000004   Code   RO         6552    i.UsageFault_Handler  n32g401_it.o
    0x08002916   0x00000002   Code   RO          564    i.WDT_Init          wdt.o
    0x08002918   0x00000008   Data   RO          607    .ARM.__AT_0x08002918  system_n32g401.o
    0x08002920   0x000000e0   Code   RO          500    i.USART2_IRQHandler  usart.o
    0x08002a00   0x00000080   Code   RO         5327    i.USART_Baud_Rate_Config  n32g401_usart.o
    0x08002a80   0x0000000a   Code   RO         5337    i.USART_Enable      n32g401_usart.o
    0x08002a8a   0x00000010   Code   RO         5343    i.USART_Hardware_Flow_Control_Config  n32g401_usart.o
    0x08002a9a   0x00000038   Code   RO         5344    i.USART_Initializes  n32g401_usart.o
    0x08002ad2   0x00000036   Code   RO         5346    i.USART_Interrput_Enable  n32g401_usart.o
    0x08002b08   0x00000014   Code   RO         5347    i.USART_Interrupt_Status_Clear  n32g401_usart.o
    0x08002b1c   0x0000004a   Code   RO         5348    i.USART_Interrupt_Status_Get  n32g401_usart.o
    0x08002b66   0x00000010   Code   RO         5356    i.USART_Mode_Config  n32g401_usart.o
    0x08002b76   0x00000010   Code   RO         5357    i.USART_Parity_Config  n32g401_usart.o
    0x08002b86   0x00000010   Code   RO         5368    i.USART_Stop_Bits_Config  n32g401_usart.o
    0x08002b96   0x00000010   Code   RO         5371    i.USART_Word_Length_Config  n32g401_usart.o
    0x08002ba6   0x00000002   PAD
    0x08002ba8   0x00000124   Code   RO         5839    i.VI4302_AllInit    VI4302_Handle.o
    0x08002ccc   0x000000f4   Code   RO         5934    i.VI4302_BVD_Calculate  VI4302_System.o
    0x08002dc0   0x00000070   Code   RO         5935    i.VI4302_Bvd_Cal    VI4302_System.o
    0x08002e30   0x00000010   Code   RO          409    i.VI4302_CsHigh     spi.o
    0x08002e40   0x00000010   Code   RO          410    i.VI4302_CsLow      spi.o
    0x08002e50   0x00000010   Code   RO           10    i.VI4302_Disen      GPIO.o
    0x08002e60   0x00000010   Code   RO           11    i.VI4302_En         GPIO.o
    0x08002e70   0x0000003c   Code   RO         5936    i.VI4302_Enable_DcDc  VI4302_System.o
    0x08002eac   0x00000070   Code   RO         5937    i.VI4302_Frame_Rate_AutoCtrl  VI4302_System.o
    0x08002f1c   0x00000010   Code   RO         5939    i.VI4302_Get_Frame_Cnt  VI4302_System.o
    0x08002f2c   0x00000014   Code   RO         6377    i.VI4302_InfifoClear  uartfifo.o
    0x08002f40   0x0000003c   Code   RO         6378    i.VI4302_InfifoDataIn  uartfifo.o
    0x08002f7c   0x00000080   Code   RO         6379    i.VI4302_InfifoDataOut  uartfifo.o
    0x08002ffc   0x00000010   Code   RO         5940    i.VI4302_Pin_Init   VI4302_System.o
    0x0800300c   0x00000068   Code   RO         5941    i.VI4302_Read_His_Config  VI4302_System.o
    0x08003074   0x00000094   Code   RO         5942    i.VI4302_Read_Histogram  VI4302_System.o
    0x08003108   0x00000080   Code   RO         5944    i.VI4302_Reg_Init   VI4302_System.o
    0x08003188   0x00000024   Code   RO         5945    i.VI4302_Set_Bvd    VI4302_System.o
    0x080031ac   0x00000140   Code   RO         5946    i.VI4302_Set_Frame_Data_Format  VI4302_System.o
    0x080032ec   0x00000040   Code   RO         5947    i.VI4302_Set_Mp_All  VI4302_System.o
    0x0800332c   0x00000064   Code   RO         5948    i.VI4302_Set_Mp_Openonly  VI4302_System.o
    0x08003390   0x0000015c   Code   RO         5949    i.VI4302_SinglePixel_Output  VI4302_System.o
    0x080034ec   0x00000064   Code   RO         5950    i.VI4302_Start_Ranging  VI4302_System.o
    0x08003550   0x00000060   Code   RO         5951    i.VI4302_Stop_Ranging  VI4302_System.o
    0x080035b0   0x00000054   Code   RO         5952    i.VI4302_Stream_On  VI4302_System.o
    0x08003604   0x0000005c   Code   RO         5953    i.VI4302_TDC_Cal    VI4302_System.o
    0x08003660   0x0000005c   Code   RO         5954    i.VI4302_Temp_Bvd   VI4302_System.o
    0x080036bc   0x0000008c   Code   RO         5955    i.VI4302_Update_Config  VI4302_System.o
    0x08003748   0x0000003c   Code   RO         5956    i.VI4302_Update_Succcess_Fail  VI4302_System.o
    0x08003784   0x000000b0   Code   RO         5957    i.VI4302_Update_firmware  VI4302_System.o
    0x08003834   0x00000050   Code   RO         5958    i.VI4302_Write_Reg  VI4302_System.o
    0x08003884   0x00000078   Code   RO         5959    i.VI4302_read_frame  VI4302_System.o
    0x080038fc   0x00000002   Code   RO          565    i.WDT_Week          wdt.o
    0x080038fe   0x00000002   PAD
    0x08003900   0x00000134   Code   RO         6220    i.XSJ_DealProtocol  cmd_handle.o
    0x08003a34   0x0000009a   Code   RO         6221    i.XSJ_UART_AllHandle  cmd_handle.o
    0x08003ace   0x00000030   Code   RO         6703    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08003afe   0x00000002   PAD
    0x08003b00   0x00000118   Code   RO         6616    i.__hardfp_floor    m_wm.l(floor.o)
    0x08003c18   0x00000c50   Code   RO         6622    i.__hardfp_pow      m_wm.l(pow.o)
    0x08004868   0x000000f8   Code   RO         6705    i.__kernel_poly     m_wm.l(poly.o)
    0x08004960   0x00000030   Code   RO         6683    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x08004990   0x00000014   Code   RO         6685    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x080049a4   0x00000004   PAD
    0x080049a8   0x00000020   Code   RO         6686    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x080049c8   0x00000020   Code   RO         6687    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x080049e8   0x00000020   Code   RO         6689    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08004a08   0x0000000e   Code   RO         6770    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08004a16   0x00000002   Code   RO         6771    i.__scatterload_null  mc_w.l(handlers.o)
    0x08004a18   0x0000000e   Code   RO         6772    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08004a26   0x00000002   PAD
    0x08004a28   0x0000000c   Code   RO         6744    i.__set_errno       mc_w.l(errno.o)
    0x08004a34   0x000000e4   Code   RO         6144    i.cal_final_tof     data_handle.o
    0x08004b18   0x0000004e   Code   RO          237    i.delay_ms          delay.o
    0x08004b66   0x00000018   Code   RO         6699    i.fabs              m_wm.l(fabs.o)
    0x08004b7e   0x00000002   PAD
    0x08004b80   0x00000040   Code   RO          297    i.flash_DataInit    flash.o
    0x08004bc0   0x000000d4   Code   RO          298    i.flash_FmDataInit  flash.o
    0x08004c94   0x00000018   Code   RO          299    i.flash_ReadConfigerData  flash.o
    0x08004cac   0x00000018   Code   RO          300    i.flash_ReadFmData  flash.o
    0x08004cc4   0x00000018   Code   RO          301    i.flash_ReadSnData  flash.o
    0x08004cdc   0x0000001c   Code   RO          302    i.flash_SaveConfigerData  flash.o
    0x08004cf8   0x0000001c   Code   RO          303    i.flash_SaveFmData  flash.o
    0x08004d14   0x0000001c   Code   RO          304    i.flash_SaveSnData  flash.o
    0x08004d30   0x0000003c   Code   RO          305    i.flash_SnDataInit  flash.o
    0x08004d6c   0x00000050   Code   RO         6645    i.free              mc_w.l(malloc.o)
    0x08004dbc   0x00000064   Code   RO         6499    i.main              main.o
    0x08004e20   0x0000006c   Code   RO         6646    i.malloc            mc_w.l(malloc.o)
    0x08004e8c   0x0000001e   Code   RO         5840    i.spi_write_data    VI4302_Handle.o
    0x08004eaa   0x0000006e   Code   RO         6710    i.sqrt              m_wm.l(sqrt.o)
    0x08004f18   0x00000014   Code   RO         6380    i.uart1infifo_Clear  uartfifo.o
    0x08004f2c   0x0000003c   Code   RO         6381    i.uart1infifo_DataIn  uartfifo.o
    0x08004f68   0x00000024   Code   RO         6382    i.uart1infifo_DataOut  uartfifo.o
    0x08004f8c   0x0000001c   Code   RO         6383    i.uart1infifo_HaveData  uartfifo.o
    0x08004fa8   0x00000014   Code   RO         6384    i.uart1outfifo_Clear  uartfifo.o
    0x08004fbc   0x00000060   Code   RO         6385    i.uart1outfifo_DataIn  uartfifo.o
    0x0800501c   0x00000064   Code   RO         6386    i.uart1outfifo_DataOut  uartfifo.o
    0x08005080   0x0000001c   Code   RO         6387    i.uart1outfifo_HaveData  uartfifo.o
    0x0800509c   0x00000058   Code   RO         5841    i.user_driver_init  VI4302_Handle.o
    0x080050f4   0x00000014   Code   RO         5842    i.vi4302_HW_set_demo  VI4302_Handle.o
    0x08005108   0x00000030   Code   RO         5843    i.vi4302_read_his_reg  VI4302_Handle.o
    0x08005138   0x00000046   Code   RO         5844    i.vi4302_read_mul_reg  VI4302_Handle.o
    0x0800517e   0x0000003a   Code   RO         5845    i.vi4302_read_ranging_data_with_firmware  VI4302_Handle.o
    0x080051b8   0x0000003a   Code   RO         5846    i.vi4302_read_register  VI4302_Handle.o
    0x080051f2   0x00000002   PAD
    0x080051f4   0x0000003c   Code   RO         5961    i.vi4302_read_status_from_firmware  VI4302_System.o
    0x08005230   0x00000022   Code   RO         5847    i.vi4302_write_cmd  VI4302_Handle.o
    0x08005252   0x00000002   PAD
    0x08005254   0x00000044   Code   RO         5848    i.vi4302_write_register  VI4302_Handle.o
    0x08005298   0x000008c0   Code   RO         6146    i.vis_tof_compensation  data_handle.o
    0x08005b58   0x00000418   Data   RO          168    .constdata          adc.o
    0x08005f70   0x000003d8   Data   RO          307    .constdata          flash.o
    0x08006348   0x00000040   Data   RO         3273    .constdata          n32g401_rcc.o
    0x08006388   0x00001f14   Data   RO         5730    .constdata          fw_44_00_00_80_R00.o
    0x0800829c   0x0000008c   Data   RO         5815    .constdata          VI4302_Config.o
    0x08008328   0x00000004   Data   RO         5849    .constdata          VI4302_Handle.o
    0x0800832c   0x00000014   Data   RO         6234    .constdata          cmd_handle.o
    0x08008340   0x00000088   Data   RO         6625    .constdata          m_wm.l(pow.o)
    0x080083c8   0x00000008   Data   RO         6707    .constdata          m_wm.l(qnan.o)
    0x080083d0   0x00000020   Data   RO         6768    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x000031d8, Max: 0x00004000, ABSOLUTE, COMPRESSED[0x0000001c])

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000001   Data   RW          504    .data               usart.o
    0x20000001   0x00000003   PAD
    0x20000004   0x00000004   Data   RW          609    .data               system_n32g401.o
    0x20000008   0x00000018   Data   RW         5787    .data               User_Driver.o
    0x20000020   0x00000005   Data   RW         5850    .data               VI4302_Handle.o
    0x20000025   0x00000001   PAD
    0x20000026   0x0000000c   Data   RW         5962    .data               VI4302_System.o
    0x20000032   0x00000006   Data   RW         6148    .data               data_handle.o
    0x20000038   0x00000803   Data   RW         6235    .data               cmd_handle.o
    0x2000083b   0x00000001   PAD
    0x2000083c   0x0000000e   Data   RW         6389    .data               uartfifo.o
    0x2000084a   0x00000002   Data   RW         6500    .data               main.o
    0x2000084c   0x00000004   Data   RW         6740    .data               mc_w.l(mvars.o)
    0x20000850   0x00000004   Data   RW         6741    .data               mc_w.l(mvars.o)
    0x20000854   0x00000004   Data   RW         6745    .data               mc_w.l(errno.o)
    0x20000858   0x00000028   Zero   RW          167    .bss                adc.o
    0x20000880   0x00000348   Zero   RW          306    .bss                flash.o
    0x20000bc8   0x000001fe   Zero   RW          503    .bss                usart.o
    0x20000dc6   0x0000000c   Zero   RW         6233    .bss                cmd_handle.o
    0x20000dd2   0x000010ff   Zero   RW         6388    .bss                uartfifo.o
    0x20001ed1   0x00000007   PAD
    0x20001ed8   0x00000300   Zero   RW          592    HEAP                startup_n32g401.o
    0x200021d8   0x00001000   Zero   RW          591    STACK               startup_n32g401.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       406         38          0          0          0     275124   GPIO.o
         0          0          0         24          0       1061   User_Driver.o
         0          0        140          0          0        710   VI4302_Config.o
       766         78          4          5          0       7995   VI4302_Handle.o
      3224        244          0         12          0      22111   VI4302_System.o
       464         28       1048          0         40       2386   adc.o
      1296         84         20       2051         12       9890   cmd_handle.o
      2646         26          0          6          0       6181   data_handle.o
       174         10          0          0          0      31826   delay.o
       760        100        984          0        840      10628   flash.o
         6          0       7956          0          0       1215   fw_44_00_00_80_R00.o
       228         28          0          2          0       2139   main.o
       132         22          0          0          0       1797   misc.o
       886        104          0          0          0      13599   n32g401_adc.o
       472          4          0          0          0      13660   n32g401_dma.o
       216         24          0          0          0       4649   n32g401_exti.o
       310         34          0          0          0       4681   n32g401_flash.o
       390          4          0          0          0       7631   n32g401_gpio.o
       128          0          0          0          0       4486   n32g401_it.o
       720        118         64          0          0      12665   n32g401_rcc.o
       354         10          0          0          0      12073   n32g401_spi.o
       420         20          0          0          0      11923   n32g401_tim.o
       440         14          0          0          0      10089   n32g401_usart.o
       466         22          0          0          0       4949   spi.o
        36          8        276          0       4864        896   startup_n32g401.o
       656         86         16          4          0       3982   system_n32g401.o
        88          6          0          0          0        585   timer.o
       596        106          0         14       4351       7914   uartfifo.o
       828         66          0          1        510       4314   usart.o
         4          0          0          0          0        918   wdt.o

    ----------------------------------------------------------------------
     17142       <USER>      <GROUP>       2124      10624     492077   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        30          0          0          5          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       164         44          0          0          0        620   dunder.o
        24          0          0          0          0        124   fabs.o
       280         28          0          0          0        156   floor.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      3152        296        136          0          0        352   pow.o
         0          0          8          0          0          0   qnan.o
       110          0          0          0          0        148   sqrt.o
        58          0          0          0          0          0   __dczerorl.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       188         20          0          0          0        160   malloc.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
         0          0          0          8          0          0   mvars.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        50          0          0          0          0         76   dfixui.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        46          0          0          0          0         80   dscalb.o
       162          0          0          0          0        100   dsqrt.o

    ----------------------------------------------------------------------
      5950        <USER>        <GROUP>         12          0       3428   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4026        368        144          0          0       1676   m_wm.l
       514         42          0         12          0        676   mc_w.l
      1398          0          0          0          0       1076   mf_w.l

    ----------------------------------------------------------------------
      5950        <USER>        <GROUP>         12          0       3428   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     23092       1694      10684       2136      10624     469805   Grand Totals
     23092       1694      10684         28      10624     469805   ELF Image Totals (compressed)
     23092       1694      10684         28          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                33776 (  32.98kB)
    Total RW  Size (RW Data + ZI Data)             12760 (  12.46kB)
    Total ROM Size (Code + RO Data + RW Data)      33804 (  33.01kB)

==============================================================================

