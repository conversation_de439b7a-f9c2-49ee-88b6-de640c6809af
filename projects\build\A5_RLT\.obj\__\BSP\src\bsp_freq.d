.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: .\..\bsp\src\bsp_freq.c
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../bsp/inc/bsp_freq.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: user/inc/variable_table.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../bsp/inc/delay.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/device/n32g401.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/core/core_cm4.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/core/cmsis_version.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/core/cmsis_compiler.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/core/cmsis_armcc.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/core/mpu_armv7.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/device/system_n32g401.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/device/n32g401_conf.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_adc.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/CMSIS/device/n32g401.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_comp.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_crc.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_dbg.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_dma.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_exti.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_flash.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_gpio.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_i2c.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_iwdg.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_pwr.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_rcc.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_rtc.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_spi.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_tim.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_usart.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_wwdg.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_beeper.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../firmware/n32g401_std_periph_driver/inc/misc.h
.\build\A5_RLT\.obj\__\bsp\src\bsp_freq.o: ../bsp/inc/timer.h
