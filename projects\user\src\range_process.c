#include "range_process.h"

//************************bsp */
#include "bsp_adc.h"
#include "bsp_io.h"
#include "bsp_vi4302.h"
#include "usart.h"


//************************driver */
#include "n32g430_it.h"

//************************app */
#include "pid_ctrl.h"

typedef struct {
    float    tof1;
    uint32_t peak1;
    float    noise1;
    float    tof2;
    uint32_t peak2;
    float    noise2;
    float    ref;

} single_point_t;

/**
 * @brief
 *
 */
void RangeProcess(void) {
    uint16_t          fps_4302 = 0;
    single_point_t    single_point;
    rec_vi_raw_data_t raw_data;

    uint8_t rVall[2] = {0};  //,tmpSendCnt=0;
#if (defined A0_NA4_XX) || (defined A2_NA4_XX) || (defined A0_D6_XX) || (defined A2_D6_XX) || (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
    float tmp_ref_vol = 3.3f;
#endif


    Vi4302RangingConfig();
    // Vi4302FpsConfig(FPS_DELAY_TIME);
    FrameSetting(4000, &fps_4302);
    Vi4302StopRanging();
    Vi4302StartRanging();

    delay_1ms(1000);
    g_ptrSysParam.mcu_ref_vol = GettingAdcVref();
    g_ptrSysParam.mcu_ref_vol = g_ptrSysParam.mcu_ref_vol < 3.05f ? 3.05f : g_ptrSysParam.mcu_ref_vol;

    for (;;) {

        g_ptrTimeTask.TaskPoll();

        ParseAck();

        if (g_ptrSysFlag.isGpioTrig0 == true) {
            g_ptrSysFlag.isGpioTrig0 = false;
            memset(cache, 0, RANGING_FRAME_LENGTH);
            cache[0] = SPI_4302_REG_READ;
            cache[1] = 0;
            cache[2] = 0x30;
            g_ptrSpi.SpiReadBytes(cache, RANGING_FRAME_LENGTH, (uint8_t *)&raw_data, RANGING_FRAME_LENGTH);


            g_ptrSysParam.adc_value = GettingAdcValue();
#if (defined A0_NA4_XX) || (defined A2_NA4_XX) || (defined A0_D6_XX) || (defined A2_D6_XX) || (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
            tmp_ref_vol                      = g_ptrSysParam.mcu_ref_vol;
            tmp_ref_vol                      = tmp_ref_vol < 3.05f ? 3.05f : tmp_ref_vol;
            g_ptrSysParam.currentTemperature = g_ptrSysParam.adc_value * (tmp_ref_vol / 3.3f);
#else
            g_ptrSysParam.currentTemperature = g_ptrSysParam.adc_value;
#endif


            g_ptrSysParam.currentTemperature    = (g_ptrSysParam.currentTemperaturePri + g_ptrSysParam.currentTemperature) / 2.0f;
            g_ptrSysParam.currentTemperaturePri = g_ptrSysParam.currentTemperature;
            /*if(g_ptrSysParam.currentTemperature < 90 && g_ptrSysParam.currentTemperature > -20 && g_ptrSysParam.isDebugVbd == true) {
                    VI4302_Temp_Bvd();
            }*/


            g_cal_data_t.raw_tof = (raw_data.raw_tof1) / SCALE_MUTIPLES - RAW_TOF_OFFSET;

            if (g_ptrSysParam.currentTemperature >= g_ptrSysParam.tgMarkPoint) {  // raw tof lower
                g_cal_data_t.raw_tof -=
                    (g_ptrSysParam.tgCoefficientP3 * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature +
                     g_ptrSysParam.tgCoefficientP2 * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature +
                     g_ptrSysParam.tgCoefficientP1 * g_ptrSysParam.currentTemperature + g_ptrSysParam.tgCoefficientP0);

            } else {  // raw tof upper
                g_cal_data_t.raw_tof -=
                    (g_ptrSysParam.tgCoefficientP3_2 * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature +
                     g_ptrSysParam.tgCoefficientP2_2 * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature +
                     g_ptrSysParam.tgCoefficientP1_2 * g_ptrSysParam.currentTemperature + g_ptrSysParam.tgCoefficientP0_2);
            }


#if (defined A0_NA4_XX) || A2_NA4_XX
            single_point.tof1 = g_cal_data_t.raw_tof - ((g_ptrSysParam.mcu_ref_vol - 3.05f) * spad_compensation_confidence->vol_compensate_raw_tof) / 15.55f;
#else
            single_point.tof1                = g_cal_data_t.raw_tof;
#endif
#if A2_PROJECT
            single_point.peak1 = (raw_data.raw_peak1 << 9) / raw_data.raw_tof2;
#else
            single_point.peak1               = (raw_data.raw_peak1 << 7) / raw_data.raw_tof2;
#endif
            single_point.noise1 = (raw_data.raw_noise1_h << 16) | raw_data.raw_noise1;
            single_point.tof2   = g_ptrSysParam.currentTemperature;  // g_ptrSysParam.integration;//
            single_point.peak2  = (raw_data.raw_peak2 << 7) / g_ptrSysParam.integration;
// single_point.noise2 = (raw_data.raw_noise2_h<<16) | raw_data.raw_noise2;//raw_data.raw_noise2_h;//
#if A2_PROJECT
            single_point.noise2 = raw_data.raw_noise2;  // raw_data.raw_noise2_h;//
#else
            single_point.noise2              = (raw_data.raw_noise2_h << 16) | raw_data.raw_noise2;  // raw_data.raw_noise2_h;//
#endif

// tmp_noise1 = single_point.noise1;

/*
g_cal_data_t.sigma = (MA_COEFFICIENT_SUM*((CONFIDENCE_FUNC_A)/(tmp_noise1 + CONFIDENCE_FUNC_B) + ((CONFIDENCE_FUNC_C*tmp_noise1)/65536) +
CONFIDENCE_FUNC_D)/8.0f) + MA_COEFFICIENT_SUM; g_cal_data_t.confidence_up = (MA_COEFFICIENT_SUM*tmp_noise1)/SCALE_MUTIPLES +
CONFIDENCE_SIGMA_UP*g_cal_data_t.sigma; g_cal_data_t.confidence_low = (MA_COEFFICIENT_SUM*tmp_noise1)/SCALE_MUTIPLES	 +
CONFIDENCE_SIGMA_LOW*g_cal_data_t.sigma;
*/
#ifdef USING_CONFIDENCE_CONST
            g_cal_data_t.sigma          = (6557 * single_point.noise2 - 3.159e-09f) / (single_point.noise2 + 469.1f);
            g_cal_data_t.confidence_up  = g_cal_data_t.sigma * 8.0f;
            g_cal_data_t.confidence_low = g_cal_data_t.sigma * 5.5f;


            if (single_point.raw_peak1 > 500) {

                if (single_point.raw_peak1 > g_cal_data_t.confidence_up) {
                    g_cal_data_t.confidence = 100;
                } else if (single_point.raw_peak1 < g_cal_data_t.confidence_low) {
                    g_cal_data_t.confidence = 0;
                } else {
                    g_cal_data_t.confidence =
                        100 * (single_point.raw_peak1 - g_cal_data_t.confidence_low) / (g_cal_data_t.confidence_up - g_cal_data_t.confidence_low);
                }
            } else {
                g_cal_data_t.confidence = 0;
            }
#else
            if (single_point.noise1 > spad_compensation_confidence->rational_x_limit) {
                single_point.noise1 = spad_compensation_confidence->rational_x_limit;
            }

            if (single_point.noise1 < (spad_compensation_confidence->rational_x_limit / 4) && single_point.peak1 < 600) {
                g_cal_data_t.confidence = 0;
            } else {

                g_cal_data_t.sigma = (spad_compensation_confidence->rational_a * single_point.noise1 + spad_compensation_confidence->rational_b) /
                                     (single_point.noise1 + spad_compensation_confidence->rational_c);
                g_cal_data_t.confidence_up  = g_cal_data_t.sigma * spad_compensation_confidence->upper_factor + spad_compensation_confidence->base_peak;
                g_cal_data_t.confidence_low = g_cal_data_t.sigma * spad_compensation_confidence->low_factor + spad_compensation_confidence->base_peak;

                if (single_point.peak1 > g_cal_data_t.confidence_up) {
                    g_cal_data_t.confidence = 100;
                } else if (single_point.peak1 < g_cal_data_t.confidence_low) {
                    g_cal_data_t.confidence = 0;
                } else {
                    g_cal_data_t.confidence =
                        100 * (single_point.peak1 - g_cal_data_t.confidence_low) / (g_cal_data_t.confidence_up - g_cal_data_t.confidence_low);
                }
            }

#endif

            // single_point.peak1  -= (single_point.noise2*-41.28f);
            // single_point.peak2 -= (single_point.noise2*-10.33f);

            if (g_ptrSysParam.isDebugHistogram == false) {
                single_point.ref = g_cal_data_t.confidence;
            } else {
                single_point.ref = raw_data.raw_ref;
            }


            if (g_ptrSysFlag.isDetectError == true) {
                DDSCodePackage(LidarSendBuf);
                g_ptrCom.ComTransmitDmaData(LidarSendBuf, 11);
                PROCESS_DELAY(100);
            } else {

                // if(g_ptrSysFlag.isComTransmitFinished == true) {
                // if(DMA_Flag_Status_Get(DMA, DMA_CH3_TXCF) == SET) {
                // g_ptrSysFlag.isComTransmitFinished = false;
                FdbMessage(kD2H | kDFF, 0xAE, (uint8_t *)&single_point, sizeof(single_point_t), kWord);
                //}
                /*tmpSendCnt++;
                if(tmpSendCnt > 100) {
                    tmpSendCnt = 0;
                    g_ptrAdc.isSampleFinished = false;
                    FdbMessage(kD2H|kHRS|kHSS,0xAA,(uint8_t *)&g_ptrSysParam.currentTemperature,TEMPERATURE_BYTE_CNT,kWord);
                }*/
            }


            if (g_regVi4302.isWriteReg == true) {
                g_regVi4302.isWriteReg = false;
                g_ptrSpi.wRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, g_regVi4302.buff[0], NULL);
            }

            if (g_regVi4302.isReadReg == true) {
                g_regVi4302.isReadReg = false;
                g_ptrSpi.rRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, &rVall[0], NULL);
                FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&rVall, REGISTER_BYTE_CNT, kResponse);
            }

            delay_1ms(10);
            g_ptrSysFlag.isGpioTrig0 = false;
        }
    }
}
