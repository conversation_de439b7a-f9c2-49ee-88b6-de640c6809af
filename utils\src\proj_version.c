#include "proj_version.h"
#include "string.h"
#include "variable_table.h"

#define STR(s)  #s
#define XSTR(s) STR(s)

/* Compiler detection */
#if defined(__CC_ARM)
/* ARM Compiler 5 (AC5) */
#define AT_ADDR(x) __attribute__((at(x)))
#elif defined(__ARMCC_VERSION) && (__ARMCC_VERSION >= 6000000)
/* ARM Compiler 6 (AC6) */
#define AT_ADDR(x) __attribute__((section(".ARM.__at_" XSTR(x))))
#else
#error "Unsupported compiler"
#endif

/************ naming rules ***********
+ PORJECT_NAME:
    + Capitalize first letter
    + All letters after '-' are capitalized
    + custom id
        +
+ version：soft_version(reference xx rules, 3bytes) + hardware_version(, 1byte) + date(YY-MM-DD, 3bytes)

***********************/
// volatile const StProjVersion gst_proj_version = {{1, 0, 0, 2, , , , }, {31}, {0, 1, 0}, {1}, {24, 7, 22}};


// 计算每个字段的起始地址
#define FW_PROJ_ADDR 0x0800B800
// #define FW_CUSTOMER_ID_ADDR FW_PROJ_ADDR + FW_PROJ_NAME_LEN
// #define FW_SW_VERSION_ADDR  FW_CUSTOMER_ID_ADDR + FW_CUSTOM_NAME_LEN
// #define FW_HW_VERSION_ADDR  FW_SW_VERSION_ADDR + FW_SW_VERSION_LEN
// #define FW_DATE_ADDR        FW_HW_VERSION_ADDR + FW_HW_VERSION_LEN
// #define FW_SW_INFO_ADDR     FW_DATE_ADDR + FW_DATE_LEN

static const StProjVersion sst_proj_version = {
    //} AT_ADDR(FW_PROJ_ADDR) = {
    .proj      = {'A', '5'},
    .custom_id = {'R', 'L', 'T'},
    .sw_ver    = {0, 0, 1},
    .hw_ver    = {1},
    .date      = {25, 8, 1},
    .info      = {0},
};

// volatile const uint8_t FW_PREFIX[]                                                 = {'N', 'o', 'v', 'a'};
// const uint8_t FW_PROJ[FW_PROJ_NAME_LEN] AT_ADDR(FW_PROJ_ADDR)                    = {'L', '1'};
// const uint8_t FW_CUSTOMER_ID[FW_CUSTOM_NAME_LEN] AT_ADDR(FW_CUSTOMER_ID_ADDR)    = {'H', 'C', 'H'};
// const uint8_t FW_SOFTWARE_VERSION[FW_SW_VERSION_LEN] AT_ADDR(FW_SW_VERSION_ADDR) = {0, 0, 0};
// const uint8_t FW_HARDWARE_VERSION[FW_HW_VERSION_LEN] AT_ADDR(FW_HW_VERSION_ADDR) = {1};
// const uint8_t FW_DATE[FW_DATE_LEN] AT_ADDR(FW_DATE_ADDR)                         = {25, 4, 21};
// const uint8_t FW_INFO[FW_SW_INFO] AT_ADDR(FW_SW_INFO_ADDR)                       = {0};

// volatile const uint8_t FW_SOFTWARE_VERSION[FW_SW_VERSION_LEN] __attribute__((aligned(4), at(FW_SW_VERSION_ADDR))) = {0, 0, 0};
// volatile const uint8_t FW_HARDWARE_VERSION[FW_HW_VERSION_LEN] __attribute__((aligned(4), at(FW_HW_VERSION_ADDR))) = {1};
// volatile const uint8_t FW_DATE[FW_DATE_LEN] __attribute__((aligned(4), at(FW_DATE_ADDR)))                         = {25, 4, 22};
// volatile const uint8_t FW_CUSTOMER_ID[FW_CUSTOM_NAME_LEN] __attribute__((aligned(4), at(FW_CUSTOMER_ID_ADDR)))    = {'H', 'C', 'H'};


void get_proj_version(uint8_t *proj_version_info_tmp) {
    memcpy(proj_version_info_tmp, (uint8_t *)&sst_proj_version, FW_VERSION_LEN);
};
