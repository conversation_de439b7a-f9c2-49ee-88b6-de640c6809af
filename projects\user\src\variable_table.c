#include "variable_table.h"


PtrSysParam g_ptrSysParam;


void SystemParamInit(void) {
    uint8_t m = 0;
    /* 替换MCU ID地址 */
    g_ptrSysParam.uID[0]  = *(__IO uint8_t *)0x1FFFF7F0;
    g_ptrSysParam.uID[1]  = *(__IO uint8_t *)0x1FFFF7F1;
    g_ptrSysParam.uID[2]  = *(__IO uint8_t *)0x1FFFF7F2;
    g_ptrSysParam.uID[3]  = *(__IO uint8_t *)0x1FFFF7F3;
    g_ptrSysParam.uID[4]  = *(__IO uint8_t *)0x1FFFF7F4;
    g_ptrSysParam.uID[5]  = *(__IO uint8_t *)0x1FFFF7F5;
    g_ptrSysParam.uID[6]  = *(__IO uint8_t *)0x1FFFF7F6;
    g_ptrSysParam.uID[7]  = *(__IO uint8_t *)0x1FFFF7F7;
    g_ptrSysParam.uID[8]  = *(__IO uint8_t *)0x1FFFF7F8;
    g_ptrSysParam.uID[9]  = *(__IO uint8_t *)0x1FFFF7F9;
    g_ptrSysParam.uID[10] = *(__IO uint8_t *)0x1FFFF7FA;
    g_ptrSysParam.uID[11] = *(__IO uint8_t *)0x1FFFF7FB;

    g_ptrFlash.ReadFlashData(LIDAR_MODE_ADDR, (uint8_t *)&g_ptrLidarStatus.lidarMode, 2);
    if (g_ptrLidarStatus.lidarMode == 0x0000 || g_ptrLidarStatus.lidarMode == 0xFFFF) {
        g_ptrLidarStatus.lidarMode = kTofSinglePointMode;
    }

    memcpy(g_ptrSysParam.version, VER_BUAD, 12);
    // g_ptrSysParam.version[0] = VER_BUAD[0];
    if (VER_BUAD[7] == 0) {
        g_ptrSysParam.buadType = 230400;
    } else if (VER_BUAD[7] == 1) {
        g_ptrSysParam.buadType = 250000;
    }

    g_ptrSysParam.measureRange[0] = 2;
    g_ptrSysParam.measureRange[1] = 16000;

    float    tmpPeak                  = 0;
    uint8_t *calibration_param_ptr_u8 = (uint8_t *)CALIBRATION_ADDR;

    if ((calibration_param_ptr_u8[0] == 0 && calibration_param_ptr_u8[1] == 0 && calibration_param_ptr_u8[2] == 0 && calibration_param_ptr_u8[3] == 0 &&
         calibration_param_ptr_u8[4] == 0 && calibration_param_ptr_u8[5] == 0 && calibration_param_ptr_u8[6] == 0 && calibration_param_ptr_u8[7] == 0) ||
        (calibration_param_ptr_u8[0] == 0xFF && calibration_param_ptr_u8[1] == 0xFF && calibration_param_ptr_u8[2] == 0xFF &&
         calibration_param_ptr_u8[3] == 0xFF && calibration_param_ptr_u8[4] == 0xFF && calibration_param_ptr_u8[5] == 0xFF &&
         calibration_param_ptr_u8[6] == 0xFF && calibration_param_ptr_u8[7] == 0xFF)) {
        g_ptrSysParam.hasCalibrationParam = false;
    } else {
        float *calibration_param_ptr = (float *)CALIBRATION_ADDR;
        for (m = 0; m < 9; m++) {
            /*g_ptrSysParam.calibrationP2[m] = CALIBRATION_PARAM[m*8+0];
            g_ptrSysParam.calibrationP1[m] = CALIBRATION_PARAM[m*8+1];
            g_ptrSysParam.calibrationP0[m] = CALIBRATION_PARAM[m*8+2];
            g_ptrSysParam.calibrationMarkPoint[m] = CALIBRATION_PARAM[m*8+3];
            g_ptrSysParam.calibrationPeakUp[m] = CALIBRATION_PARAM[m*8+4];
            g_ptrSysParam.calibrationPeakLow[m] = CALIBRATION_PARAM[m*8+5];*/

            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 0], (uint8_t *)&g_ptrSysParam.calibrationP2[m], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 1], (uint8_t *)&g_ptrSysParam.calibrationP1[m], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 2], (uint8_t *)&g_ptrSysParam.calibrationP0[m], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 3], (uint8_t *)&g_ptrSysParam.calibrationMarkPoint[m], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 4], (uint8_t *)&g_ptrSysParam.calibrationPeakUp[m], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 5], (uint8_t *)&g_ptrSysParam.calibrationPeakLow[m], 4);

            if (g_ptrSysParam.calibrationPeakUp[m] > tmpPeak) {
                g_ptrSysParam.normalPeakMax = g_ptrSysParam.calibrationPeakUp[m];
            }
        }

        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[9 * 8 + 0], (uint8_t *)&g_ptrSysParam.pileupA, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[9 * 8 + 1], (uint8_t *)&g_ptrSysParam.pileupB, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[9 * 8 + 2], (uint8_t *)&g_ptrSysParam.pileupC, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[10 * 8 + 0], (uint8_t *)&g_ptrSysParam.noiseTofA, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[10 * 8 + 1], (uint8_t *)&g_ptrSysParam.noiseTofB, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[10 * 8 + 2], (uint8_t *)&g_ptrSysParam.noiseTofC, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[11 * 8 + 0], (uint8_t *)&g_ptrSysParam.noisePeak1A, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[11 * 8 + 1], (uint8_t *)&g_ptrSysParam.noisePeak1B, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[11 * 8 + 2], (uint8_t *)&g_ptrSysParam.noisePeak1C, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[12 * 8 + 0], (uint8_t *)&g_ptrSysParam.noisePeak2A, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[12 * 8 + 1], (uint8_t *)&g_ptrSysParam.noisePeak2B, 4);
        g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[12 * 8 + 2], (uint8_t *)&g_ptrSysParam.noisePeak2C, 4);

        for (m = 16; m < 26; m++) {
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 0], (uint8_t *)&g_ptrSysParam.materialConfficientLower[m - 16], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 1], (uint8_t *)&g_ptrSysParam.materialConfficientK[m - 16], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 2], (uint8_t *)&g_ptrSysParam.materialConfficientB[m - 16], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 3], (uint8_t *)&g_ptrSysParam.materialConfficientUpper[m - 16], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 4], (uint8_t *)&g_ptrSysParam.materialConfficientDetaTof[m - 16], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 5], (uint8_t *)&g_ptrSysParam.materialConfficientMarkPoint[m - 16], 4);
            g_ptrFlash.ReadFlashData((uint32_t)&calibration_param_ptr[m * 8 + 6], (uint8_t *)&g_ptrSysParam.materialConfficientBound[m - 16], 4);
        }

        if (g_ptrSysParam.calibrationMarkPoint[6] > 500 && g_ptrSysParam.calibrationMarkPoint[7] > 1500 && g_ptrSysParam.calibrationMarkPoint[8] > 3000) {
            g_ptrSysParam.hasCalibrationParam = true;

        } else {
            g_ptrSysParam.hasCalibrationParam = false;
        }
    }

    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[0], (uint8_t *)&g_ptrSysParam.tgCoefficientP3, 4);
    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[1], (uint8_t *)&g_ptrSysParam.tgCoefficientP2, 4);
    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[2], (uint8_t *)&g_ptrSysParam.tgCoefficientP1, 4);
    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[3], (uint8_t *)&g_ptrSysParam.tgCoefficientP0, 4);
    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[4], (uint8_t *)&g_ptrSysParam.tgMarkPoint, 4);
    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[5], (uint8_t *)&g_ptrSysParam.tgCoefficientP3_2, 4);
    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[6], (uint8_t *)&g_ptrSysParam.tgCoefficientP2_2, 4);
    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[7], (uint8_t *)&g_ptrSysParam.tgCoefficientP1_2, 4);
    g_ptrFlash.ReadFlashData((uint32_t)&TG_PARAM[8], (uint8_t *)&g_ptrSysParam.tgCoefficientP0_2, 4);

    g_ptrSysParam.histogramAndSpadSampleFreq[0] = 45;
    g_ptrSysParam.histogramAndSpadSampleFreq[1] = 1;

    uint8_t *angle_ptr = (uint8_t *)ANGLE_ADDR;
    if ((angle_ptr[0] == 0x00 && angle_ptr[1] == 0x00 && angle_ptr[2] == 0x00 && angle_ptr[3] == 0x00) ||
        (angle_ptr[0] == 0xFF && angle_ptr[1] == 0xFF && angle_ptr[2] == 0xFF && angle_ptr[3] == 0xFF)) {
        g_ptrSysParam.zeroAngle = 0.0f;
    } else {
        g_ptrFlash.ReadFlashData((uint32_t)angle_ptr, (uint8_t *)&g_ptrSysParam.zeroAngle, 4);
    }

    uint8_t *vbd_ptr = (uint8_t *)VBD_ADDR;
    if ((vbd_ptr[0] == 0x00 && vbd_ptr[1] == 0x00 && vbd_ptr[1] == 0x00) || (vbd_ptr[0] == 0xFF && vbd_ptr[1] == 0xFF && vbd_ptr[1] == 0xFF)) {
        g_ptrSysParam.vbdStep = 0;
        g_ptrSysParam.vbdTmp  = 0;
        g_ptrSysParam.tdcTmp  = 0;

    } else {
        g_ptrFlash.ReadFlashData((uint32_t)&vbd_ptr[0], (uint8_t *)&g_ptrSysParam.vbdStep, 1);
        g_ptrFlash.ReadFlashData((uint32_t)&vbd_ptr[1], (uint8_t *)&g_ptrSysParam.vbdTmp, 1);
        g_ptrFlash.ReadFlashData((uint32_t)&vbd_ptr[2], (uint8_t *)&g_ptrSysParam.tdcTmp, 1);
    }

    g_ptrSysParam.isVbdCal         = false;
    g_ptrSysParam.isDebugHistogram = false;
    g_ptrSysParam.isDebugVbd       = true;
    g_ptrSysParam.ranging_mode_fps = 115;
    g_ptrSysParam.mcu_ref_vol      = 3.3f;

    g_ptrSysFlag.isComTransmitFinished = true;
    g_ptrSysFlag.isTrigTO              = false;
    g_ptrSysFlag.isTrigPack            = true;
    g_ptrSysFlag.errorCode             = 0;
    g_ptrSysParam.close_led            = false;

    g_ptrSysParam.sensor_trig_ng_cnt = 0;

    g_regEncoder.calcEdgeCountLast   = 0;
    g_regEncoder.calcEdgeCountAve    = 0;
    g_regEncoder.calcEdgeNum         = 0;      // 当前计数
    g_regEncoder.startPack           = 0;      // 开始包标志
    g_regEncoder.isEnterLoop         = false;  // 进入数据开始标志
    g_regEncoder.isMarkPack          = false;
    g_regEncoder.calcEdgeSum         = 0;  // 边缘计数总和
    g_regEncoder.current_point_num   = 0;  // 25点 当前计数
    g_regEncoder.markEdgeCountSum    = 0;
    g_regEncoder.markEdgeCountPre    = 0;
    g_regEncoder.markEdgeCountLast   = 0;
    g_regEncoder.markEdgeSlowCount   = 0;
    g_regEncoder.markEdgeQuickCount  = 0;
    g_regEncoder.markEdgeStableCount = 0;  // 边缘稳定状态计数
    g_regEncoder.markEdgeCount       = 0;  // 边缘计数
    // 雷达当前角度
    g_regEncoder.angle = 0;

    g_regSpeed.isSpeedStabled       = false;  // 转速稳定标识
    g_regSpeed.isDetectStartEncoder = false;  // 检测到最小包标志
    g_regSpeed.isStalled            = false;  // 停转标志
    g_regSpeed.isShowFont           = false;  // 显示字体标志
    g_regSpeed.isHighSpeed          = false;  // 高速标志
    g_regSpeed.isMediumSpeed        = false;  // 中速标志
    g_regSpeed.isLowSpeed           = false;  // 低速标志
    g_regSpeed.isTrackAngle         = false;  // 角度追踪
    g_regSpeed.isDetectSpeed        = false;  // 检测到速度标志(一圈判断一次)
    g_regSpeed.isFinHighConfig      = false;  // 预设
    g_regSpeed.isFinMidConfig       = false;  // 预设
    g_regSpeed.isFinLowConfig       = false;
    g_regSpeed.isFinTrackConfig     = false;
    g_regSpeed.isSendStabled        = false;
    g_regSpeed.isReserved2          = false;
    g_regSpeed.isReserved3          = false;

    g_regSpeed.currentSpeed    = 0;  // 当前速度
    g_regSpeed.currentSpeedPre = 0;  // 当前速度的上一个速度
    // 转速稳定计数 3个稳定周期
    g_regSpeed.speedFlag       = 0;
    g_regSpeed.speedCnt        = 0;
    g_regSpeed.speedFlagPre    = 0;
    g_regSpeed.speedStableFlag = 0;
    g_regSpeed.motorOK         = 0;

    g_ptrAdc.isSampleFinished = false;
    g_ptrAdc.isOnceSample     = false;

    g_regVi4302.isWriteReg = false;
    g_regVi4302.isReadReg  = false;

    g_ptrDDSInfoCount.encoder_cnt   = 0;
    g_ptrDDSInfoCount.laserCnt      = 0;
    g_ptrDDSInfoCount.sensorTrigCnt = 0;
    g_ptrDDSInfoCount.tempCnt       = 0;

    g_stParseAck.ackTimes = 0;

    g_cal_data_t.angle_time = 0;
    g_cal_data_t.encoder_cnt = 0;
    g_cal_data_t.is_start_pack = 0;
    g_cal_data_t.is_ignore_one_circle = false;
    g_cal_data_t.point_num = 0;


    //param_combine_t
    g_ptrFlash.ReadFlashData((uint32_t)XTALK_ADDR, (uint8_t*)&g_param_combine, sizeof(param_combine_t));
    pid_param_init(&g_pid_speed, g_param_combine.motor_mode.speed);
    
 
}
