#ifndef _VARIABLE_TABLE_H
#define _VARIABLE_TABLE_H

#include "delay.h"
#include "n32g401.h"
#include "string.h"
#include <math.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>


#ifdef __cplusplus
extern "C" {
#endif


/**********/


#define PROCESS_DELAY(x)   delay_1ms(x)

/***** ADDRESS DEFINE******/

#define ADDR_FLASH_PAGE_0  ((uint32_t)0x08000000) /* Base @ of Page 0, 2 Kbytes */
#define ADDR_FLASH_PAGE_1  ((uint32_t)0x08000800) /* Base @ of Page 1, 2 Kbytes */
#define ADDR_FLASH_PAGE_2  ((uint32_t)0x08001000) /* Base @ of Page 2, 2 Kbytes */
#define ADDR_FLASH_PAGE_3  ((uint32_t)0x08001800) /* Base @ of Page 3, 2 Kbytes */
#define ADDR_FLASH_PAGE_4  ((uint32_t)0x08002000) /* Base @ of Page 4, 2 Kbytes */
#define ADDR_FLASH_PAGE_5  ((uint32_t)0x08002800) /* Base @ of Page 5, 2 Kbytes */
#define ADDR_FLASH_PAGE_6  ((uint32_t)0x08003000) /* Base @ of Page 6, 2 Kbytes */
#define ADDR_FLASH_PAGE_7  ((uint32_t)0x08003800) /* Base @ of Page 7, 2 Kbytes */
#define ADDR_FLASH_PAGE_8  ((uint32_t)0x08004000) /* Base @ of Page 8, 2 Kbytes */
#define ADDR_FLASH_PAGE_9  ((uint32_t)0x08004800) /* Base @ of Page 19, 2 Kbytes */
#define ADDR_FLASH_PAGE_10 ((uint32_t)0x08005000) /* Base @ of Page 10, 2 Kbytes */
#define ADDR_FLASH_PAGE_11 ((uint32_t)0x08005800) /* Base @ of Page 11, 2 Kbytes */
#define ADDR_FLASH_PAGE_12 ((uint32_t)0x08006000) /* Base @ of Page 12, 2 Kbytes */
#define ADDR_FLASH_PAGE_13 ((uint32_t)0x08006800) /* Base @ of Page 13, 2 Kbytes */
#define ADDR_FLASH_PAGE_14 ((uint32_t)0x08007000) /* Base @ of Page 14, 2 Kbytes */
#define ADDR_FLASH_PAGE_15 ((uint32_t)0x08007800) /* Base @ of Page 15, 2 Kbytes */
#define ADDR_FLASH_PAGE_16 ((uint32_t)0x08008000) /* Base @ of Page 16, 2 Kbytes */
#define ADDR_FLASH_PAGE_17 ((uint32_t)0x08008800) /* Base @ of Page 17, 2 Kbytes */
#define ADDR_FLASH_PAGE_18 ((uint32_t)0x08009000) /* Base @ of Page 18, 2 Kbytes */
#define ADDR_FLASH_PAGE_19 ((uint32_t)0x08009800) /* Base @ of Page 19, 2 Kbytes */
#define ADDR_FLASH_PAGE_20 ((uint32_t)0x0800A000) /* Base @ of Page 20, 2 Kbytes */
#define ADDR_FLASH_PAGE_21 ((uint32_t)0x0800A800) /* Base @ of Page 21, 2 Kbytes */
#define ADDR_FLASH_PAGE_22 ((uint32_t)0x0800B000) /* Base @ of Page 22, 2 Kbytes */
#define ADDR_FLASH_PAGE_23 ((uint32_t)0x0800B800) /* Base @ of Page 23, 2 Kbytes */
#define ADDR_FLASH_PAGE_24 ((uint32_t)0x0800C000) /* Base @ of Page 24, 2 Kbytes */
#define ADDR_FLASH_PAGE_25 ((uint32_t)0x0800C800) /* Base @ of Page 25, 2 Kbytes */
#define ADDR_FLASH_PAGE_26 ((uint32_t)0x0800D000) /* Base @ of Page 26, 2 Kbytes */
#define ADDR_FLASH_PAGE_27 ((uint32_t)0x0800D800) /* Base @ of Page 27, 2 Kbytes */
#define ADDR_FLASH_PAGE_28 ((uint32_t)0x0800E000) /* Base @ of Page 28, 2 Kbytes */
#define ADDR_FLASH_PAGE_29 ((uint32_t)0x0800E800) /* Base @ of Page 29, 2 Kbytes */
#define ADDR_FLASH_PAGE_30 ((uint32_t)0x0800F000) /* Base @ of Page 30, 2 Kbytes */
#define ADDR_FLASH_PAGE_31 ((uint32_t)0x0800F800) /* Base @ of Page 31, 2 Kbytes */

typedef struct {
    uint8_t  isInitParam;
    uint8_t  uID[12];
    uint8_t  version[8];
    uint32_t buadType;
    uint16_t measureRange[2];
    float    calibrationP2[10];
    float    calibrationP1[10];
    float    calibrationP0[10];
    float    calibrationMarkPoint[10];
    float    calibrationPeakUp[10];
    float    calibrationPeakLow[10];
    float    pileupA;
    float    pileupB;
    float    pileupC;
    float    noiseTofA;
    float    noiseTofB;
    float    noiseTofC;
    float    noisePeak1A;
    float    noisePeak1B;
    float    noisePeak1C;
    float    noisePeak2A;
    float    noisePeak2B;
    float    noisePeak2C;

    float tgCoefficientP3;
    float tgCoefficientP2;
    float tgCoefficientP1;
    float tgCoefficientP0;
    float tgMarkPoint;
    float tgCoefficientP3_2;
    float tgCoefficientP2_2;
    float tgCoefficientP1_2;
    float tgCoefficientP0_2;

    float materialConfficientK[10];
    float materialConfficientB[10];
    float materialConfficientUpper[10];
    float materialConfficientLower[10];
    float materialConfficientDetaTof[10];
    float materialConfficientMarkPoint[10];
    float materialConfficientBound[10];

    bool     hasCalibrationParam;
    bool     isVbdCal;
    bool     is_unlock;
    uint16_t currentTempAdc;
    float    currentTemperature, currentTemperaturePri, temperatureLast;
    float    zeroAngle;
    uint32_t histogramAndSpadSampleFreq[2];
    uint8_t  vbdStep;
    uint8_t  vbdTmp;
    uint8_t  tdcTmp;
    uint16_t integration;

    _Bool isDebugHistogram;
    _Bool isDebugVbd;

    float normalPeakMax;

    uint16_t ranging_mode_fps;
    uint16_t adc_value;
    int16_t  compensation_adc_value;
    float    mcu_ref_vol;
    bool     close_led;
    uint8_t  sensor_trig_ng_cnt;

} PtrSysParam;
extern PtrSysParam g_ptrSysParam;



void SystemParamInit(void);

#ifdef __cplusplus
}
#endif

#endif
