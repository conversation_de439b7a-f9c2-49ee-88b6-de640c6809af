.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: .\app\cmd_handle\cmd_handle.c
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../bsp/inc/GPIO.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/device/n32g401.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/core/core_cm4.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/core/cmsis_version.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/core/cmsis_compiler.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/core/cmsis_armcc.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/core/mpu_armv7.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/device/system_n32g401.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/device/n32g401_conf.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_adc.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/CMSIS/device/n32g401.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_comp.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_crc.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_dbg.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_dma.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_exti.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_flash.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_gpio.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_i2c.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_iwdg.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_pwr.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_rcc.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_rtc.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_spi.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_tim.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_usart.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_wwdg.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_beeper.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../firmware/n32g401_std_periph_driver/inc/misc.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: VI4302 API/inc/User_Driver.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: user/inc/main.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../bsp/inc/usart.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../bsp/inc/spi.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../bsp/inc/timer.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../bsp/inc/flash.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../bsp/inc/delay.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../bsp/inc/WDT.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: VI4302 API/inc/VI4302_System.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: VI4302 API/inc/VI4302_handle.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: .\app\cmd_handle\_cmd_handle.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: .\app\cmd_handle\cmd_handle.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: app/uartfifo/uartfifo.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: ../bsp/inc/adc.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: app/inc/converter.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: VI4302 API/inc/data_handle.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: app/work_mode/work_mode.h
.\build\A5_RLT\.obj\app\cmd_handle\cmd_handle.o: VI4302 API/inc/A2_Configurable.h
