#include "GPIO.h"


void NVIC_Configuration(uint8_t irq_val, uint8_t dis_enable, uint8_t preemptionPriority, uint8_t subPriority) {
    NVIC_InitType NVIC_InitStructure;

    /* Enable and configure RCC global IRQ channel */
    NVIC_InitStructure.NVIC_IRQChannel                   = irq_val;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = preemptionPriority;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority        = subPriority;
    NVIC_InitStructure.NVIC_IRQChannelCmd                = dis_enable;
    NVIC_Initializes(&NVIC_InitStructure);
}

ErrorStatus SetSysClockToPLL(void) {
    RCC_ClocksType RCC_ClockFreq;
    ErrorStatus    HSEStartUpStatus;

    uint32_t timeout_value = 0xFFFFFF;


    /* SYSCLK, HCLK, PCLK2 and PCLK1 configuration
     * -----------------------------*/
    /* RCC system reset(for debug purpose) */
    RCC_Reset();

    /* Enable HSE */
    RCC_HSE_Config(RCC_HSE_ENABLE);

    /* Wait till HSE is ready */
    HSEStartUpStatus = RCC_HSE_Stable_Wait();

    if (HSEStartUpStatus != SUCCESS) {
        /* If HSE fails to start-up, the application will have wrong clock
             configuration. User can add here some code to deal with this
             error */
        return ERROR;
    }

    FLASH_Latency_Set(FLASH_LATENCY_1);

    /* HCLK = SYSCLK */
    RCC_Hclk_Config(RCC_SYSCLK_DIV1);

    /* PCLK2 = HCLK */
    RCC_Pclk2_Config(RCC_HCLK_DIV1);

    /* PCLK1 = HCLK */
    RCC_Pclk1_Config(RCC_HCLK_DIV2);

    RCC_PLL_Config(RCC_PLL_SRC_HSE_DIV2, RCC_PLL_MUL_9);

    /* Enable PLL */
    RCC_PLL_Enable();

    /* Wait till PLL is ready */
    while ((RCC->CTRL & RCC_CTRL_PLLRDF) == 0) {
        if ((timeout_value--) == 0) {
            return ERROR;
        }
    }
    /* Select PLL as system clock source */
    RCC_Sysclk_Config(RCC_SYSCLK_SRC_PLLCLK);

    /* Wait till PLL is used as system clock source */
    timeout_value = 0xFFFF;
    while (RCC_Sysclk_Source_Get() != RCC_CFG_SCLKSTS_PLL) {
        if ((timeout_value--) == 0) {
            return ERROR;
        }
    }

    /* Enable Clock Security System(CSS): this will generate an NMI exception
   when HSE clock fails */
    RCC_Clock_Security_System_Enable();

    RCC_Clocks_Frequencies_Value_Get(&RCC_ClockFreq);  //获取主频

    return SUCCESS;
}

void RCO_OutputInit(void) {
    GPIO_InitType GPIO_InitStructure = {0};

    /* Output HSE clock on MCO pin
     * ---------------------------------------------*/
    RCC_AHB_Peripheral_Clock_Enable(RCC_AHB_PERIPH_GPIOA);
    RCC_AHB_Peripheral_Clock_Enable(RCC_AHB_PERIPH_GPIOB);
    RCC_APB2_Peripheral_Clock_Enable(RCC_APB2_PERIPH_AFIO);

    GPIO_Structure_Initialize(&GPIO_InitStructure);
    GPIO_InitStructure.Pin            = GPIO_PIN_8;
    GPIO_InitStructure.GPIO_Pull      = GPIO_PULL_UP;
    GPIO_InitStructure.GPIO_Current   = GPIO_DS_8MA;
    GPIO_InitStructure.GPIO_Slew_Rate = GPIO_SLEW_RATE_FAST;
    GPIO_InitStructure.GPIO_Mode      = GPIO_MODE_AF_PP;
    GPIO_InitStructure.GPIO_Alternate = GPIO_AF9_MCO;
    GPIO_Peripheral_Initialize(GPIOA, &GPIO_InitStructure);

    GPIO_Structure_Initialize(&GPIO_InitStructure);
    GPIO_InitStructure.Pin       = GPIO_PIN_15;
    GPIO_InitStructure.GPIO_Pull = GPIO_NO_PULL;
    GPIO_InitStructure.GPIO_Mode = GPIO_MODE_INPUT;
    GPIO_Peripheral_Initialize(GPIOB, &GPIO_InitStructure);
    //    RCC_MCO_PLL_Prescaler_Config(RCC_MCO_PLLCLK_DIV9);
    RCC_MCO_Source_Config(RCC_MCO_HSI);  // RCC_MCO_PLLCLK
}

void GPIO_Init(void) {
    GPIO_InitType GPIO_InitStructure;
    EXTI_InitType EXTI_InitStructure;

    /* Output HSE clock on MCO pin
     * ---------------------------------------------*/
    RCC_AHB_Peripheral_Clock_Enable(RCC_AHB_PERIPH_GPIOA);
    RCC_AHB_Peripheral_Clock_Enable(RCC_AHB_PERIPH_GPIOB);
    RCC_APB2_Peripheral_Clock_Enable(RCC_APB2_PERIPH_AFIO);

    GPIO_Pins_Reset(GPIOB, GPIO_PIN_6 | GPIO_PIN_8);

    GPIO_Structure_Initialize(&GPIO_InitStructure);
    GPIO_InitStructure.Pin            = GPIO_PIN_6 | GPIO_PIN_8;  //
    GPIO_InitStructure.GPIO_Mode      = GPIO_MODE_OUT_PP;
    GPIO_InitStructure.GPIO_Slew_Rate = GPIO_SLEW_RATE_FAST;
    GPIO_Peripheral_Initialize(GPIOB, &GPIO_InitStructure);


    //配置外部触发中断
    GPIO_Structure_Initialize(&GPIO_InitStructure);
    GPIO_InitStructure.Pin            = GPIO_PIN_0;
    GPIO_InitStructure.GPIO_Mode      = GPIO_MODE_INPUT;
    GPIO_InitStructure.GPIO_Slew_Rate = GPIO_SLEW_RATE_FAST;
    GPIO_InitStructure.GPIO_Pull      = GPIO_PULL_UP;
    GPIO_Peripheral_Initialize(GPIOB, &GPIO_InitStructure);

    GPIO_InitStructure.Pin = GPIO_PIN_2;
    GPIO_Peripheral_Initialize(GPIOA, &GPIO_InitStructure);

    GPIO_EXTI_Line_Set(EXTI_LINE_SOURCE0, AFIO_EXTI_PB0);
    GPIO_EXTI_Line_Set(EXTI_LINE_SOURCE2, AFIO_EXTI_PA2);

    /* Configure key EXTI line */
    EXTI_InitStructure.EXTI_Line    = EXTI_LINE0 | EXTI_LINE2;
    EXTI_InitStructure.EXTI_Mode    = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising;  //上升沿中断
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_Peripheral_Initializes(&EXTI_InitStructure);

    NVIC_Configuration(EXTI2_IRQn, ENABLE, 0, 0);
    NVIC_Configuration(EXTI0_IRQn, ENABLE, 0, 0);
}


void TX_En(void) {
    GPIO_Pins_Set(GPIOB, GPIO_PIN_8);
}

void TX_Disen(void) {
    GPIO_Pins_Reset(GPIOB, GPIO_PIN_8);
}

void VI4302_En(void) {
    GPIO_Pins_Set(GPIOB, GPIO_PIN_6);
}

void VI4302_Disen(void) {
    GPIO_Pins_Reset(GPIOB, GPIO_PIN_6);
}
