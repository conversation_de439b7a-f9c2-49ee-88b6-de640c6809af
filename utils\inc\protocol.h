/**
 * @file protocol.h
 * <AUTHOR> name (<EMAIL>)
 * @brief 通用协议，与端口分离
 * @version 0.1
 * @date 2025-07-31
 *
 * @copyright Copyright (c) 2025
 *
 */
#ifndef _PROTOCOL_H_
#define _PROTOCOL_H_

#include "typedef.h"


//*协议cmd枚举
enum EInteractionFrame {
    eHEADER     = 0x55A5,
    eACK_HEADER = 0x5AA5,
    eHEADER_LEN = 0x06,
    eXOR_INDEX  = 0x04,
};

/*协议cmd枚举*/
enum ECmd {
    eH2D = 0 << 0,  // host->device
    eD2H = 1 << 0,  // device->host

    //    eCMD        = 0<<2 | 0<<1, //cmd
    //    eACK        = 0<<2 | 1<<1, //ack
    //    eINFO       = 1<<2 | 0<<1, //info
    //    eDATA       = 1<<2 | 1<<1, //data

    eW = 0 << 1,  // write
    eR = 1 << 1,  // read

    //    eW          = 0<<3, //write
    //    eR          = 1<<3, //read
};

/*协议cmd枚举*/
enum {
    kH2D = 0x01, /*host->device*/
    kD2H = 0X02, /*device->host*/
    kHWS = 0x04, /*host write success*/
    kHRS = 0x08, /*host read success*/
    kHSS = 0x10, /*host send ok*/
    kDFF = 0x20, /*device fixed frequency send*/
    kDNP = 0x40, /*device not parse logic*/
};

#pragma pack(2)

// interaction protocol
#define INTERACTION_BUFF_SIZE 20
typedef struct _interaction_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint8_t  data[INTERACTION_BUFF_SIZE];
} StInteractionProtocol;

// greymap protocol
#define GreyMap_BUFF_SIZE 25  // 5* 5
typedef struct _greymap_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint16_t data[GreyMap_BUFF_SIZE];
} StGreyMapProtocol;

// histogram protocol
#define HISTOGRAM_BUFF_SIZE 2048
typedef struct _histogram_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint8_t  data[HISTOGRAM_BUFF_SIZE];
} StHistogramProtocol;

// range protocol
#define RANGE_BUFF_SIZE 23
typedef struct _range_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint8_t  data[RANGE_BUFF_SIZE];
} StRangeProtocol;

// scan points protocol
#define SCAN_POINTS_BUFF_SIZE 20
typedef struct _scan_points_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint8_t  data[SCAN_POINTS_BUFF_SIZE];
} StScanPointsProtocol;

/**
 * @brief 协议类型，为了向下兼容，不同模式采用了不同的协议结构体，实际协议可能相同
 *
 */
typedef enum {
    eInteraction = 0,
    eGreyMap,
    eHistogram,
    eRange,
    eScanPoints,
} EProtocolType;

/**
 * @brief 指令标识
 *
 */
typedef enum {
    eFrameIdle = 0,
    eFrameRec,
    eFrameSend,
} EFrameFlag;


/**
 * @brief 缓存指令信息
 *
 */
typedef struct {
    EProtocolType type;
    uint16_t      length;
    EFrameFlag    frame_flag;
} StFrameInfo;


#define MAX(a, b)     ((a) > (b) ? (a) : (b))
#define Com_BUFF_SIZE MAX(MAX(MAX(GreyMap_BUFF_SIZE << 1, HISTOGRAM_BUFF_SIZE), RANGE_BUFF_SIZE), SCAN_POINTS_BUFF_SIZE)

#if 1
/**
 * @brief 发送数据buffer
 *
 */
typedef union {
    uint8_t               buffer[Com_BUFF_SIZE];
    StInteractionProtocol interaction;
    StGreyMapProtocol     greymap;
    StHistogramProtocol   histogram;
    StRangeProtocol       range;
    StScanPointsProtocol  scan_points;
} UCommSendBuffer;

#else
/**
 * @brief
 *
 */
typedef struct {
    union {
        uint8_t               buffer[Com_BUFF_SIZE];
        StInteractionProtocol interaction;
        StGreyMapProtocol     greymap;
        StHistogramProtocol   histogram;
        StRangeProtocol       range;
        StScanPointsProtocol  scan_points;
    };
    uint16_t rec_buffe_size[20];
    uint8_t  rec_count;
} StComFifo;
#endif
#pragma pack()


// typedef union {
//     uint8_t buffer[eHEADER_LEN + DATA_LENGTH];
//     struct {
//         volatile uint16_t header;
//         volatile uint8_t  cmd;
//         volatile uint8_t  id;
//         volatile uint8_t  checkXor;
//         volatile uint8_t  num;
//         uint8_t           data_buff[DATA_LENGTH];
//     } FRAME;
// } U_FRAME;


/**
 * @brief 通讯协议
 *
 */
// typedef struct {
//     void (*init)(void);  //协议初始化
//     int (*read)(uint8_t *buf, size_t len);
//     int (*write)(const uint8_t *buf, size_t len);
//     void (*deinit)(void);
// } Protocol_API_T;


void            transmit_info_init(void);
void            uart_ack_test(void);
EFunctionStatus parse_protocol(U_FRAME const *const data_receive_, uint8_t *const task_index_);
void            cmd_ack(void);

#endif
