#include "time_tasks.h"


static TaskComponents *taskHead      = NULL;  //存放所有注册回调函数的链表头
static _Bool           isFirstConfig = true;



static void TaskRemarks(void);
static void TaskProcess(void);


/**
 * @brief 任务时间片处理, 在滴答定时器中调用
 *
 */
static void TaskRemarks(void) {
    TaskComponents *task;
    for (task = taskHead; task != NULL; task = task->next) {
        if (task->Timer) {                    // 时间不为0
            task->Timer--;                    // 任务倒计时
            if (task->Timer == 0) {           // 时间到的
                task->Timer = task->ItvTime;  // 恢复任务计时器，重新开始
                task->Run   = 1;              // 任务可以运行标志
            }
        }
    }
}

/**
 * @brief 任务处理
 *
 */
static void TaskProcess(void) {
    TaskComponents *task;
    ParseProtocol();
    for (task = taskHead; task != NULL; task = task->next) {
        if (task->Run) {  //运行标志
            task->callback();
            task->Run = 0;
        }
    }
}

/**
 * @brief 主函数调用ptrTask 执行本文件的函数,主函数注册callback  被本文件调用
 *
 * @param ptrTask
 * @param task
 * @param priority
 * @param duration
 * @param callback
 * @return true
 * @return false
 */
static bool RegisterTaskCallBackFunc(PtrTimeTask *ptrTask, TaskComponents *task, int priority, unsigned int duration, void (*callback)(void)) {
    if (isFirstConfig == true) {
        ptrTask->TsakTimePoll = TaskRemarks;
        ptrTask->TaskPoll     = TaskProcess;
        isFirstConfig         = false;
        if (taskHead != NULL) {
            taskHead = NULL;
        }
    }

    // TaskComponents *task1;
    TaskComponents *taskTail = taskHead;
    if (task == NULL || callback == NULL) {
        return 0;
    }
    task->callback = callback;
    task->priority = priority;
    task->Timer    = duration;
    task->ItvTime  = duration;
    task->next     = NULL;
    if (taskHead == NULL) {
        taskHead = task;
        return 1;
    }
    while (taskTail->next != NULL) { /*当存两个(以上)时逐步转至链尾*/
        taskTail = taskTail->next;
    }
    taskTail->next = task;
    return 1;
}

