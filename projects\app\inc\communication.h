/**
 * @file communication.h
 * <AUTHOR> name (<EMAIL>)
 * @brief 通用通讯模块，兼容UART、BLUETOOTH、TCP等等接口
 * @version 0.1
 * @date 2025-07-31
 *
 * @copyright Copyright (c) 2025
 *
 */
#ifndef _COMMUNICATION_H_
#define _COMMUNICATION_H_


#include "protocol.h"
#include "ring_buff.h"


#define RING_BUFF_REC_MAX_TIMES 10


/***************************COMMUNICATION*********/
/*模式与枚举（与D/T系列相同）*/
enum EModeData {
    kTriStopMode        = 0xA5FF,
    kTriTestMode        = 0xA5FE,
    kTriFaculaMode      = 0xA5FD,
    kTriScanMode        = 0xA5FC,
    kTriCalibrationMode = 0xA5FB,

    kTofStopMode        = 0xA5FF,
    kTofHistMode        = 0xA5FE,
    kTofFaculaMode      = 0xA5FD,
    kTofScanMode        = 0xA5FC,
    kTofCalibrationMode = 0xA5FB,
    kTofSinglePointMode = 0xA5FA,
    kTofRangingMode     = 0xA5F9,
    kTofRevCheckMode    = 0xA5F8,
    kTofSize
};

enum ECmdID {
    kTriMode          = 0xA0,
    kTofMode          = 0xA1,
    kSysParam         = 0xA2,
    kMcuId            = 0xA3,
    kVersionBuad      = 0xA4,
    kMeasureRange     = 0xA5,
    kCalibrationParam = 0xA6,
    kTgParam          = 0xA7,
    kReflelParam      = 0xA8,
    kHisAndFacula     = 0xA9,
    kTemperature      = 0xAA,
    kRegisterSetting  = 0xAB,
    kZeroAngleSetting = 0xAC,
    kLaserControl     = 0xAD,
    kLedControl       = 0xAE,
    kVbd              = 0xAF,
    kDebugSetting4    = 0xBC,
    kBootloader       = 0xBD,
    kDebugSetting2    = 0xBE,
    kDebugSetting     = 0xBF,
    kRangingMode      = 0xD0,
    kXtalkCalc        = 0xD1,

    //底板替换指令
    kBtMotorCtrl = 0xF0,  // kRunning  kStandby  kTurnOn  kTurnOff
    kBtMotorCode = 0xF1,
    kDdsInfo     = 0xF2,

    kSize,
};

typedef enum {
    eUART,
} ECommType;

/**
 * @brief 数据缓存buffer，发送和接收数据都可以缓存
 *
 */
typedef struct {
    StRingBuff  ring_buffer;
    StFrameInfo rec_buffe_size[RING_BUFF_REC_MAX_TIMES];
    uint8_t     rec_count;
} StCommFifo;


/**
 * @brief 通讯接口
 *
 */
typedef struct {
    void (*CommInit)(ECommType type, uint32_t param1);  // 接口函数
    void (*CommTransmitData)(uint8_t *, uint16_t buffSiz);  // 发送数据
} API_Communication_T;


extern const API_Communication_T *const g_comm_ptr;

#endif
