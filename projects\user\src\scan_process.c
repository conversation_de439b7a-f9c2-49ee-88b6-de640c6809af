/*
 * @Author: xx
 * @Date: 2024-05-21 20:06:29
 * @LastEditors: Do not edit
 * @LastEditTime: 2025-04-29 10:26:15
 * @Description: 
 * @FilePath: \MDK-ARMd:\DOC\300 WORKING\330 CODE REPOSITORY\332 ToF Lidar\003 code repository\002 drive\vscode_workspace\fw01_dToF_lidar\App\src\scan_process.c
 */
#include "scan_process.h"
#include "n32g430_it.h"
#include "n32g430.h"                    // Device header
#include "variable_table.h"
#include "bsp_vi4302.h"
#include "bsp_io.h"
#include "bsp_adc.h"
#include "systick.h"
#include "usart.h"
// 







#if D4_PROJECT
	/*(defined A0_D4_SAMSUNG) || (defined A2_D4_SAMSUNG) || (defined A0_D4_POSITIC) || (defined A2_D4_POSITIC) || \
	(defined A0_D4_SK) || (defined A2_D4_SK)*/

		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'C','O','I','N','-','D','4',0,0,0,0,0,
																						0,0,0,0,0,0,0,0};
			
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','D',0x4,'M',0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};
						
		#endif	
		
#elif D4A_PROJECT
	/*(defined A0_D4A_UMOUSE) || (defined A2_D4A_UMOUSE)*/
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'C','O','I','N','-','D','4','A',0,0,0,0,
																						0,0,0,0,0,0,0,0};
			
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','D',0x4,'M',0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};
						
		#endif																			
																					
#elif T4_PROJECT
		/*(defined A0_T4_JINGCHUANG) || (defined A2_T4_JINGCHUANG) || (defined A0_T4_WEILAN) || (defined A2_T4_WEILAN) || \
			(defined A0_T4_TAKDIR) || (defined A2_T4_TAKDIR) || (defined A0_T4_NARWAL) || (defined A2_T4_NARWAL) || \
			(defined A0_NA4_XX) || (defined A2_NA4_XX)*/
																					
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'M','I','N','I','-','T','4',0,0,0,0,0,
																							0,0,0,0,0,0,0,0};
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','T',0x4,'M',0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};

		#endif																			
																					
#elif T4A_PROJECT
		/*(defined A0_T4A_MEELUX) || (defined A2_T4A_MEELUX)*/
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'M','I','N','I','-','T','4','A',0,0,0,0,
																							0,0,0,0,0,0,0,0};
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','T',0x4,'M',0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};

		#endif	
#elif NA4_PROJECT
		/*(defined A0_T4_JINGCHUANG) || (defined A2_T4_JINGCHUANG) || (defined A0_T4_WEILAN) || (defined A2_T4_WEILAN) || \
			(defined A0_T4_TAKDIR) || (defined A2_T4_TAKDIR) || (defined A0_T4_NARWAL) || (defined A2_T4_NARWAL) || \
			(defined A0_NA4_XX) || (defined A2_NA4_XX)*/
																					
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'M','I','N','I','-','A','4',0,0,0,0,0,
																							0,0,0,0,0,0,0,0};
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','A',0x4,'M',0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};

		#endif	

#elif T4B_PROJECT
		/*(defined A0_T4B_XX) || (defined A2_T4B_XX)*/
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'M','I','N','I','-','T','4','B',0,0,0,0,
																							0,0,0,0,0,0,0,0};
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','T',0x4,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};

		#endif	
#elif D6_PROJECT
		/*(defined A0_D6_XX) || (defined A2_D6_XX)*/
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'C','O','I','N','-','D','6',0,0,0,0,0,
																							0,0,0,0,0,0,0,0};
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','D',0x6,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};
		#endif
		

#elif T5_PROJECT
		/*(defined A0_T5_XX) || (defined A2_T5_XX)*/
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'M','I','N','I','-','T','5',0,0,0,0,0,
																							0,0,0,0,0,0,0,0};
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','T',0x5,'M',0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};

		#endif
#elif D6A_PROJECT
	/*(defined A0_D4A_UMOUSE) || (defined A2_D4A_UMOUSE)*/
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'C','O','I','N','-','D','6','A',0,0,0,0,
																						0,0,0,0,0,0,0,0};
			
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','D',0x6,'M',0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};
						
		#endif					
#elif D7_PROJECT
		/*(defined A0_D6_XX) || (defined A2_D6_XX)*/
		#if USING_CSPC_PROTOCOL == 1
		uint8_t kDiviceInformation[27] = {0xA5,0X5A,20,0,0,0,1,'C','O','I','N','-','D','7',0,0,0,0,0,
																							0,0,0,0,0,0,0,0};
		#else
		const uint8_t kDiviceInformation[61] = {0x5A,0xAA,0x36,0x00,0x00,0x00,0x01,
			0x06,'G','K','D',0x7,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x01,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
		};
		#endif

#endif							
																					
uint32_t datajgs[LIDAR_DATABUFF_SIZE_MAX/2-1]={0};
																																									
																					

void ScanProcess(void)
{
	uint16_t fps_4302 = 0;
	Vi4302RangingConfig();
	//Vi4302FpsConfig(FPS_DELAY_TIME);

	if(g_ptrLidarStatus.lidarMode == kTofRangingMode) {
		FrameSetting(g_ptrSysParam.ranging_mode_fps, &fps_4302);
	}
	else {
		#if A0_T5_UMOUSE
		FrameSetting(2400, &fps_4302);
		#else 
		FrameSetting(4000, &fps_4302);
		#endif
	}
	
	Vi4302StopRanging();
	Vi4302StartRanging();
	
	if (g_ptrLidarStatus.lidarMode == kTofScanMode) {
		#if USING_SCAN_DEBUG == 0
		g_regSpeed.isSendStabled = false;
		while(g_regSpeed.isSpeedStabled == false || g_regSpeed.isDetectStartEncoder == false) {
			g_ptrTimeTask.TaskPoll();	
			ParseAck();
		}
		#endif
		DeviceInfo();
		LidarDataBegin();
	}
	g_ptrSysParam.mcu_ref_vol = GettingAdcVref();
	g_ptrSysParam.mcu_ref_vol = g_ptrSysParam.mcu_ref_vol<3.05f ? 3.05f : g_ptrSysParam.mcu_ref_vol;

	g_ptrDDSInfoCount.ddsCnt = getAbsolutionTime();
	g_ptrSysFlag.isGpioTrig0 = false;
	
	for(;;) { 
		g_ptrTimeTask.TaskPoll();

		if(g_ptrSysParam.sensor_trig_ng_cnt > 0)//VI4302 Trig异常后（复位）  重新初始化4302
		{
			g_ptrSysParam.sensor_trig_ng_cnt = 0;
			Vi4302RangingConfig();
			if(g_ptrLidarStatus.lidarMode == kTofRangingMode) {
				FrameSetting(g_ptrSysParam.ranging_mode_fps, &fps_4302);
			}
			else {
				#if A0_T5_UMOUSE
				FrameSetting(2400, &fps_4302);
				#else 
				FrameSetting(4000, &fps_4302);
				#endif
			}
		
			Vi4302StopRanging();
			Vi4302StartRanging();

			g_ptrSysFlag.isGpioTrig0 = false;
		}
		RunningScan();		
		TransmitData();
	}
}


void DeviceInfo(void)
{
	uint16_t i,check_sum=0;
	
	//========================���ڹ̶��������=========================//
	#if USING_CSPC_PROTOCOL == 1
	g_regLidarData0.PH = 0x55AA; 
	g_regLidarData1.PH = 0x55AA;
	g_regLidarData2.PH = 0x55AA;
	g_regLidarDataX.PH = 0x55AA;
	g_regLidarData0.LSN = 0;
	g_regLidarData1.LSN = 0;
	g_regLidarData2.LSN = 0;
	g_regLidarDataX.LSN = 0;
	g_regLidarData0.BufferLen = 0;
	g_regLidarData1.BufferLen = 0;
	g_regLidarData2.BufferLen = 0;
	g_regLidarDataX.BufferLen = 0;
	g_regLidarData0.Pack_Send_En = 0;
	g_regLidarData1.Pack_Send_En = 0;
	g_regLidarData2.Pack_Send_En = 0;
	g_regLidarDataX.Pack_Send_En = 0;
	
	#else
	g_regLidarData0.header 				= 0xAA55;
	g_regLidarData0.information 	= LIDAR_DATA_SCAN_INFO;
	g_regLidarData0.data_number		= LIDAR_DATA_NUM;
	g_regLidarData0.send_end			= 0;
	g_regLidarData1.header 				= 0xAA55;
	g_regLidarData1.information 	= LIDAR_DATA_SCAN_INFO;
	g_regLidarData1.data_number 	= LIDAR_DATA_NUM;
	g_regLidarData1.send_end			= 0;
	g_regLidarData2.header 				= 0xAA55;
	g_regLidarData2.information 	= LIDAR_DATA_SCAN_INFO;
	g_regLidarData2.data_number 	= LIDAR_DATA_NUM;
	g_regLidarData2.send_end			= 0;
	#endif
	
	
	//========================���ڲ�������ָ��=========================//
	/*if(g_lidarDataPackCount==0)	
	{
		g_regLidarDataPtr	=	&g_regLidarData0;
	}
	if(g_lidarDataPackCount==1)	
	{
		g_regLidarDataPtr	=	&g_regLidarData1;
	}
	if(g_lidarDataPackCount==2)	
	{
		g_regLidarDataPtr	=	&g_regLidarData2;
	}*/
	
	g_regLidarDataPtr	=	&g_regLidarData0;
	
	/*******transmit lidar information********/
	/*kDiviceInformation[26] = VER_BUAD[0];
	for(i=0; i<27; i++) {
		check_sum += kDiviceInformation[i];
	}
	kDiviceInformation[4] = check_sum;
	kDiviceInformation[5] = check_sum>>8;
	
	for(i=0; i<27; i++){
		printf("%c",kDiviceInformation[i]);
	}*/
	memcpy(cache,kDiviceInformation,sizeof(kDiviceInformation));
	cache[sizeof(kDiviceInformation)-1] = VER_BUAD[0];
	for(i=0; i<sizeof(kDiviceInformation); i++){
		check_sum += cache[i];
	}
	cache[4] = (uint8_t)check_sum;
	cache[5] = (uint8_t)(check_sum>>8);
	for(i=0; i<sizeof(kDiviceInformation); i++){
		printf("%c",cache[i]);
	}
	
	

	
	
}

void LidarDataBegin(void)
{
	// uint16_t i;
	/*******transmit lidar start data*********/	
	#if USING_CSPC_PROTOCOL == 1
	cache[0] = 0xA5;
	cache[1] = 0x5A;
	cache[2] = 0;
	cache[3] = 0;
	cache[4] = 0x80;
	cache[5] = 0x01;
	cache[6] = 0x81;	
	
	#else
	cache[0] = 0x5A;
	cache[1] = 0xAA;
	cache[2] = 0;
	cache[3] = 0;
	cache[4] = 0x85;
	cache[5] = 0x01;
	cache[6] = 0x81;
	
	#endif
	
	/*for(i=0; i<7; i++){
		printf("%c",cache[i]);
	}*/
	g_ptrCom.ComTransmitDmaData(cache, 7);
}


#if USING_CSPC_PROTOCOL == 1
void FillLidarBuf(PtrRegLidarData lidarInfo,uint8_t *sendbuff)
{
	uint8_t i=0;
	sendbuff[0] = lidarInfo.PH;
	sendbuff[1] = lidarInfo.PH>>8;
	sendbuff[2] = lidarInfo.F_And_C;
	sendbuff[3] = lidarInfo.LSN;
	sendbuff[4] = lidarInfo.FSA;
	sendbuff[5] = lidarInfo.FSA>>8;
	sendbuff[6] = lidarInfo.LSA;
	sendbuff[7] = lidarInfo.LSA>>8;
	sendbuff[8] = sendbuff[0]^sendbuff[2]^sendbuff[4]^sendbuff[6];
	sendbuff[9] = sendbuff[1]^sendbuff[3]^sendbuff[5]^sendbuff[7];
	
	if(g_ptrLidarStatus.lidarMode == kTofScanMode) {
		for(i=0; i<lidarInfo.LSN; i++)
		{
			#ifndef	USING_DISTANCE_2_BYTES
			sendbuff[10+3*i] = lidarInfo.SI[i]>>16;
			sendbuff[10+3*i+1] = lidarInfo.SI[i];
			sendbuff[10+3*i+2] = lidarInfo.SI[i]>>8;
					
			sendbuff[8] ^= sendbuff[10+3*i]^sendbuff[10+3*i+1];
			sendbuff[9] ^= sendbuff[10+3*i+2];
			#else 
			sendbuff[10+2*i] = lidarInfo.SI[i];
			sendbuff[10+2*i+1] = lidarInfo.SI[i]>>8;
					
			sendbuff[8] ^= sendbuff[10+2*i];
			sendbuff[9] ^= sendbuff[10+2*i+1];
			#endif
		}
	}
	else {
		for(i=0; i<lidarInfo.LSN; i++)
		{
			sendbuff[10+4*i] = lidarInfo.SI[i];
			sendbuff[10+4*i+1] = lidarInfo.SI[i]>>8;
			sendbuff[10+4*i+2] = lidarInfo.SI[i]>>16;
			sendbuff[10+4*i+3] = lidarInfo.SI[i]>>24;
					
			sendbuff[8] ^= sendbuff[10+4*i]^sendbuff[10+4*i+1];
			sendbuff[9] ^= sendbuff[10+4*i+2]^sendbuff[10+4*i+3];
		}
	}	
	
	
	
	
}

#else

uint8_t Lidar_ChecksumCal(PtrRegLidarData uartbuff,uint8_t *sendbuff)
{
	uint8_t data_length = 0;
	uint8_t data_size=4;
	int32_t chk32 = 0;
	int32_t check_sum = 0;
	if(sendbuff == NULL)
		return 0;
	sendbuff[0]=uartbuff.header;
	sendbuff[1]=uartbuff.header>>8;
	sendbuff[2]=uartbuff.information;
	sendbuff[3]=uartbuff.data_number;
	sendbuff[4]=uartbuff.speed;
	sendbuff[5]=uartbuff.speed>>8;
	sendbuff[6]=uartbuff.first_angle;
	sendbuff[7]=uartbuff.first_angle>>8;
	
	
	
	for(int i=0;i<uartbuff.data_number;i++)
	{
		sendbuff[8+data_size*i] = uartbuff.data[i];
		sendbuff[9+data_size*i] = uartbuff.data[i]>>8;
		sendbuff[10+data_size*i] = uartbuff.data[i]>>16;
		sendbuff[11+data_size*i] = uartbuff.data[i]>>24;
	}
	sendbuff[8+data_size*uartbuff.data_number] = uartbuff.last_angle;
	sendbuff[9+data_size*uartbuff.data_number] = uartbuff.last_angle>>8;
	data_length = 5+((data_size*uartbuff.data_number)>>1);

	for(int i=0;i<data_length;i++)
	{
		datajgs[i]=sendbuff[2*i] + (sendbuff[2*i+1]<<8);
	}
	
	for(int i=0;i<data_length;i++)
	{
		chk32 = (chk32 << 1) + datajgs[i];
	}
	check_sum = (chk32 & 0x7FFF) + ( chk32 >> 15 );
	check_sum = check_sum & 0x7FFF;


	sendbuff[data_length*2] = check_sum;
	sendbuff[data_length*2+1] = check_sum>>8;
	return data_length*2+2;//�������ݳ���
}

 
#endif
	
void RunningScan(void)
{
	

	static uint8_t r_value[2]={0};
	rec_vi_raw_data_t		raw_data;
	uint8_t hr_denote = 0;
	uint8_t polling_cnt = 0;
	float  angleValue = 0;
	float  tn_angle = 0;
	uint16_t angle_encode = 0;
	float angle_interval = 24;
	uint16_t angle_base = 0;
	#if (defined A0_NA4_XX) || (defined A2_NA4_XX) || (defined A0_D6_XX) || (defined A2_D6_XX) || (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
	float   tmp_ref_vol = 3.3f;
	#endif

	if(g_ptrSysFlag.isGpioTrig0 == true) {
			
			g_ptrSysFlag.isGpioTrig0 = false;
			memset(cache,0,RANGING_FRAME_LENGTH);
			cache[0] = SPI_4302_REG_READ;
			cache[1] = 0;
			cache[2] = 0x30;
			g_ptrSpi.SpiReadBytes(cache,RANGING_FRAME_LENGTH,(uint8_t*)&raw_data,RANGING_FRAME_LENGTH);
			
			g_sys_time_t.time_cur = get_absolution_stamp_us();
			g_sys_time_t.time_diff = g_sys_time_t.time_cur - g_sys_time_t.time_last;
			g_sys_time_t.time_last = g_sys_time_t.time_cur;

			g_cal_data_t.angle_time = 0;
			g_cal_data_t.raw_peak2 = 0;
			g_cal_data_t.peak2_l = 0;
			g_cal_data_t.peak2_r = 0;
			g_cal_data_t.raw_peak1 = 0;
			g_cal_data_t.encoder_cnt = 0;
			g_cal_data_t.delta_angle = 0;
			
			do {			
					g_cal_data_t.delta_angle = g_regEncoder.calcEdgeCountAve;
					g_cal_data_t.encoder_cnt = g_regEncoder.calcEdgeNum;
					g_cal_data_t.angle_time = TIM_Base_Count_Get(TIM8);		
			}while(g_cal_data_t.encoder_cnt != g_regEncoder.calcEdgeNum);
			
			
			g_cal_data_t.is_start_pack = g_regEncoder.startPack;
			//g_regEncoder.startPack = 0;//???? ?????????
			

			

			g_ptrSysParam.adc_value = GettingAdcValue();
			#if (defined A0_NA4_XX) || (defined A2_NA4_XX) || (defined A0_D6_XX) || (defined A2_D6_XX) || (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
			tmp_ref_vol = g_ptrSysParam.mcu_ref_vol;
			tmp_ref_vol = tmp_ref_vol<3.05f ? 3.05f : tmp_ref_vol;
			g_ptrSysParam.currentTemperature = g_ptrSysParam.adc_value*(tmp_ref_vol/3.3f);
			#else
			g_ptrSysParam.currentTemperature = g_ptrSysParam.adc_value;
			#endif


			g_ptrSysParam.currentTemperature = (g_ptrSysParam.currentTemperaturePri + g_ptrSysParam.currentTemperature)/2.0f;
			g_ptrSysParam.currentTemperaturePri = g_ptrSysParam.currentTemperature;
			/*if(g_ptrSysParam.currentTemperature < 90 && g_ptrSysParam.currentTemperature > -20 && g_ptrSysParam.isDebugVbd == true) {
				VI4302_Temp_Bvd();
			}*/
			

			g_cal_data_t.raw_tof = (raw_data.raw_tof1)/SCALE_MUTIPLES - RAW_TOF_OFFSET;
			//对tof值进行温度补偿
			if(g_ptrSysParam.currentTemperature >= g_ptrSysParam.tgMarkPoint) {//raw tof lower
				g_cal_data_t.raw_tof -= (g_ptrSysParam.tgCoefficientP3*g_ptrSysParam.currentTemperature*g_ptrSysParam.currentTemperature*g_ptrSysParam.currentTemperature\
										+ g_ptrSysParam.tgCoefficientP2*g_ptrSysParam.currentTemperature*g_ptrSysParam.currentTemperature \
										+ g_ptrSysParam.tgCoefficientP1*g_ptrSysParam.currentTemperature + g_ptrSysParam.tgCoefficientP0);
			
			}
			else {//raw tof upper
				g_cal_data_t.raw_tof -= (g_ptrSysParam.tgCoefficientP3_2*g_ptrSysParam.currentTemperature*g_ptrSysParam.currentTemperature*g_ptrSysParam.currentTemperature\
										+ g_ptrSysParam.tgCoefficientP2_2*g_ptrSysParam.currentTemperature*g_ptrSysParam.currentTemperature \
										+ g_ptrSysParam.tgCoefficientP1_2*g_ptrSysParam.currentTemperature + g_ptrSysParam.tgCoefficientP0_2);
			
			}
			
			g_cal_data_t.raw_tof *= 15.55f;	//换算成距离

			#if (defined A0_NA4_XX) || A2_NA4_XX
			g_cal_data_t.raw_tof = g_cal_data_t.raw_tof - ((g_ptrSysParam.mcu_ref_vol-3.05f)*spad_compensation_confidence->vol_compensate_raw_tof);
			#else
			g_cal_data_t.raw_tof = g_cal_data_t.raw_tof;
			#endif	
			#if A2_PROJECT
			g_cal_data_t.raw_peak1 = (raw_data.raw_peak1<<9)/raw_data.raw_tof2;
			#else
			g_cal_data_t.raw_peak1 = (raw_data.raw_peak1<<7)/raw_data.raw_tof2;
			#endif
			g_cal_data_t.raw_peak2 = (raw_data.raw_peak2<<7)/g_ptrSysParam.integration;//175����ִ������

		
			g_cal_data_t.noise1 = raw_data.raw_noise1_h<<16 | raw_data.raw_noise1;
			#if A2_PROJECT
			g_cal_data_t.noise2 = raw_data.raw_noise2;//raw_data.raw_noise2_h;//
			#else
			g_cal_data_t.noise2 = raw_data.raw_noise2_h<<16 | raw_data.raw_noise2;
			#endif
			
			#if A0_D6_XX
			//g_cal_data_t.raw_peak1 += (GettingTemperature(GettingAdcValue())-25)*50; // Ӧ�Լ��⹦��˥����laoying 7e��ʱʹ��
			#endif
			/*calc confidence*/
			/*
			g_cal_data_t.sigma = (MA_COEFFICIENT_SUM*((CONFIDENCE_FUNC_A)/(g_cal_data_t.noise1 + CONFIDENCE_FUNC_B) + ((CONFIDENCE_FUNC_C*g_cal_data_t.noise1)/65536) + CONFIDENCE_FUNC_D)/8.0f) + MA_COEFFICIENT_SUM;
			g_cal_data_t.confidence_up = (MA_COEFFICIENT_SUM*g_cal_data_t.noise1)/SCALE_MUTIPLES + CONFIDENCE_SIGMA_UP*g_cal_data_t.sigma;
			g_cal_data_t.confidence_low = (MA_COEFFICIENT_SUM*g_cal_data_t.noise1)/SCALE_MUTIPLES	 + CONFIDENCE_SIGMA_LOW*g_cal_data_t.sigma; 
			*/
			#ifdef USING_DONFIDENCE_CONST 
			g_cal_data_t.sigma = (6557*g_cal_data_t.noise2-3.159e-09f)/(g_cal_data_t.noise2 + 469.1f);
			g_cal_data_t.confidence_up = g_cal_data_t.sigma*8.0f;
			g_cal_data_t.confidence_low = g_cal_data_t.sigma*5.5f;

			
			if(g_cal_data_t.raw_peak1 > 500) {
				if(g_cal_data_t.raw_peak1 > g_cal_data_t.confidence_up) {
					g_cal_data_t.confidence = 100;
				}
				else if(g_cal_data_t.raw_peak1 < g_cal_data_t.confidence_low) {
					g_cal_data_t.confidence = 0;
				}
				else {
					g_cal_data_t.confidence = 100*(g_cal_data_t.raw_peak1 - g_cal_data_t.confidence_low)/(g_cal_data_t.confidence_up - g_cal_data_t.confidence_low);
				}
			}
			else {
				g_cal_data_t.confidence = 0;
			}
			#else
			if(g_cal_data_t.noise1 > spad_compensation_confidence->rational_x_limit) {
				g_cal_data_t.noise1 = spad_compensation_confidence->rational_x_limit;
			}

			if(g_cal_data_t.noise1 < (spad_compensation_confidence->rational_x_limit/4) && g_cal_data_t.raw_peak1 < 600) {
				g_cal_data_t.confidence = 0;
			}
			else {

				g_cal_data_t.sigma = (spad_compensation_confidence->rational_a*g_cal_data_t.noise1+spad_compensation_confidence->rational_b) \
									/(g_cal_data_t.noise1+spad_compensation_confidence->rational_c);
				g_cal_data_t.confidence_up = g_cal_data_t.sigma*spad_compensation_confidence->upper_factor + spad_compensation_confidence->base_peak;
				g_cal_data_t.confidence_low = g_cal_data_t.sigma*spad_compensation_confidence->low_factor + spad_compensation_confidence->base_peak; 

				if(g_cal_data_t.raw_peak1 > g_cal_data_t.confidence_up) {
						g_cal_data_t.confidence = 100;
				}
				else if(g_cal_data_t.raw_peak1 < g_cal_data_t.confidence_low) {
					g_cal_data_t.confidence = 0;
				}
				else {
					g_cal_data_t.confidence = 100*(g_cal_data_t.raw_peak1 - g_cal_data_t.confidence_low)/(g_cal_data_t.confidence_up - g_cal_data_t.confidence_low);
				}
			}
			
			#endif
			
			
			g_cal_data_t.raw_peak1 = g_cal_data_t.raw_peak1>>7;
			g_ptrRegCentroid.peak = g_cal_data_t.raw_peak1;

			
			//laser detect
			if(g_cal_data_t.raw_peak1 > DDS_LASER_PEAK_THRESHOLD ||  (g_cal_data_t.raw_tof > 6800 && g_cal_data_t.confidence > spad_compensation_confidence->confidence_threshold)) {
				g_ptrDDSInfoCount.laserCnt = getAbsolutionTime();
			}
			

			if(g_cal_data_t.confidence >= spad_compensation_confidence->confidence_threshold && g_cal_data_t.raw_tof > 0 && g_ptrSysParam.hasCalibrationParam == true) { //CONFIDENCEN_THRESHOLD
				//global pileup 
				g_cal_data_t.pileup_offset = g_cal_data_t.raw_tof + (g_ptrSysParam.pileupA*g_cal_data_t.raw_peak1*g_cal_data_t.raw_peak1 + g_ptrSysParam.pileupB*g_cal_data_t.raw_peak1 +  g_ptrSysParam.pileupC);
				
				//peak1		
				for(polling_cnt=0; polling_cnt<9; polling_cnt++) {
						 if(g_cal_data_t.pileup_offset <= g_ptrSysParam.calibrationMarkPoint[0]) {
									//peak �޷�
									if(g_cal_data_t.raw_peak1 > g_ptrSysParam.calibrationPeakUp[0]) {
										g_cal_data_t.offset1_l = (g_ptrSysParam.calibrationPeakUp[0]*g_ptrSysParam.calibrationP1[0] + g_ptrSysParam.calibrationP0[0]);
									}
									else if(g_cal_data_t.raw_peak1 < g_ptrSysParam.calibrationPeakLow[0]) {				
										g_cal_data_t.offset1_l =  (g_ptrSysParam.calibrationPeakLow[0]*g_ptrSysParam.calibrationP1[0] + g_ptrSysParam.calibrationP0[0]);
									}
									else {
										
										g_cal_data_t.offset1_l = (g_cal_data_t.raw_peak1*g_ptrSysParam.calibrationP1[0] + g_ptrSysParam.calibrationP0[0]);
									}
								
									
									g_ptrRegCentroid.distance_tmp = g_cal_data_t.offset1_l + g_cal_data_t.pileup_offset;
									
									break;	
						 }
						 else if((g_cal_data_t.pileup_offset <= g_ptrSysParam.calibrationMarkPoint[polling_cnt]) && (polling_cnt > 0)) {
								g_cal_data_t.l_val = (g_ptrSysParam.calibrationMarkPoint[polling_cnt] - g_cal_data_t.pileup_offset)/g_ptrSysParam.calibrationP2[polling_cnt];
								g_cal_data_t.r_val = (g_cal_data_t.pileup_offset - g_ptrSysParam.calibrationMarkPoint[polling_cnt-1])/g_ptrSysParam.calibrationP2[polling_cnt];
								
								//peak �޷�
								if(g_cal_data_t.raw_peak1 > g_ptrSysParam.calibrationPeakUp[polling_cnt-1]) {
									g_cal_data_t.offset1_l = (g_ptrSysParam.calibrationPeakUp[polling_cnt-1]*g_ptrSysParam.calibrationP1[polling_cnt-1] + g_ptrSysParam.calibrationP0[polling_cnt-1])*g_cal_data_t.l_val;
								}
								else if(g_cal_data_t.raw_peak1 < g_ptrSysParam.calibrationPeakLow[polling_cnt-1]) {				
									g_cal_data_t.offset1_l = (g_ptrSysParam.calibrationPeakLow[polling_cnt-1]*g_ptrSysParam.calibrationP1[polling_cnt-1] + g_ptrSysParam.calibrationP0[polling_cnt-1])*g_cal_data_t.l_val;
								}
								else {
									
									g_cal_data_t.offset1_l = (g_cal_data_t.raw_peak1*g_ptrSysParam.calibrationP1[polling_cnt-1] + g_ptrSysParam.calibrationP0[polling_cnt-1])*g_cal_data_t.l_val;
								}
								
								//peak �޷�
								if(g_cal_data_t.raw_peak1 > g_ptrSysParam.calibrationPeakUp[polling_cnt]) {
									g_cal_data_t.offset1_r = (g_ptrSysParam.calibrationPeakUp[polling_cnt]*g_ptrSysParam.calibrationP1[polling_cnt] + g_ptrSysParam.calibrationP0[polling_cnt])*g_cal_data_t.r_val;
								}
								else if(g_cal_data_t.raw_peak1 < g_ptrSysParam.calibrationPeakLow[polling_cnt]) {				
									g_cal_data_t.offset1_r = (g_ptrSysParam.calibrationPeakLow[polling_cnt]*g_ptrSysParam.calibrationP1[polling_cnt] + g_ptrSysParam.calibrationP0[polling_cnt])*g_cal_data_t.r_val;
								}
								else {
									
									g_cal_data_t.offset1_r = (g_cal_data_t.raw_peak1*g_ptrSysParam.calibrationP1[polling_cnt] + g_ptrSysParam.calibrationP0[polling_cnt])*g_cal_data_t.r_val;
								}
															
								g_ptrRegCentroid.distance_tmp = g_cal_data_t.offset1_l + g_cal_data_t.offset1_r + g_cal_data_t.pileup_offset;		
								break;
						 }
						 else if(g_cal_data_t.pileup_offset > g_ptrSysParam.calibrationMarkPoint[8]){
								if(g_cal_data_t.raw_peak1 > g_ptrSysParam.calibrationPeakUp[8]) {
									g_cal_data_t.offset1_r = (g_ptrSysParam.calibrationPeakUp[8]*g_ptrSysParam.calibrationP1[8] + g_ptrSysParam.calibrationP0[8]);
								}
								else if(g_cal_data_t.raw_peak1 < g_ptrSysParam.calibrationPeakLow[8]) {				
									g_cal_data_t.offset1_r = (g_ptrSysParam.calibrationPeakLow[8]*g_ptrSysParam.calibrationP1[8] + g_ptrSysParam.calibrationP0[8]);
								}
								else {
									
									g_cal_data_t.offset1_r = (g_cal_data_t.raw_peak1*g_ptrSysParam.calibrationP1[8] + g_ptrSysParam.calibrationP0[8]);
								}
							
								
								g_ptrRegCentroid.distance_tmp = g_cal_data_t.offset1_r + g_cal_data_t.pileup_offset;
								break;
						 }
						 else {
								g_ptrRegCentroid.distance_tmp = 0;
						 }
				
				
				}
				
				//peak2
				g_cal_data_t.raw_peak2 -= (g_cal_data_t.noise2*spad_compensation_confidence->peak2_factor);
				if(g_ptrRegCentroid.distance_tmp > 0) {
						for(polling_cnt=0; polling_cnt<9; polling_cnt++) {//peak2
								if(g_ptrRegCentroid.distance_tmp <= g_ptrSysParam.materialConfficientMarkPoint[0]){
										if(g_cal_data_t.raw_peak2 > g_ptrSysParam.materialConfficientBound[0]) {
											hr_denote = 1;
										}
										else {
											hr_denote = 0;
										}
										
										
										if(g_cal_data_t.raw_peak2 < g_ptrSysParam.materialConfficientLower[0]) {
												g_cal_data_t.peak2_l = g_ptrSysParam.materialConfficientLower[0] ;
										}
										else if(g_cal_data_t.raw_peak2 > g_ptrSysParam.materialConfficientUpper[0] ) {
												g_cal_data_t.peak2_l =  g_ptrSysParam.materialConfficientUpper[0] ;
										}
										else {
												g_cal_data_t.peak2_l = g_cal_data_t.raw_peak2;
										}

										g_cal_data_t.offset2 = (g_cal_data_t.peak2_l*g_ptrSysParam.materialConfficientK[0] + g_ptrSysParam.materialConfficientB[0]);	
										g_ptrRegCentroid.distance_tmp += g_cal_data_t.offset2;
										break;
								}
								else if((g_ptrRegCentroid.distance_tmp <= g_ptrSysParam.materialConfficientMarkPoint[polling_cnt]) && (polling_cnt > 0)) {
										g_cal_data_t.l_val = (g_ptrSysParam.materialConfficientMarkPoint[polling_cnt] - g_ptrRegCentroid.distance_tmp)/g_ptrSysParam.materialConfficientDetaTof[polling_cnt];
										g_cal_data_t.r_val = (g_ptrRegCentroid.distance_tmp - g_ptrSysParam.materialConfficientMarkPoint[polling_cnt-1])/g_ptrSysParam.materialConfficientDetaTof[polling_cnt];
										
										
										g_ptrRegCentroid.hr_mark_threshold = g_ptrSysParam.materialConfficientBound[polling_cnt-1]+g_cal_data_t.r_val*(g_ptrSysParam.materialConfficientBound[polling_cnt]-g_ptrSysParam.materialConfficientBound[polling_cnt-1]);
										if(g_cal_data_t.raw_peak2 > g_ptrRegCentroid.hr_mark_threshold) {
											hr_denote = 1;
										}
										else {
											hr_denote = 0;
										}
							
										
										if(g_cal_data_t.raw_peak2 < g_ptrSysParam.materialConfficientLower[polling_cnt-1]) {
												g_cal_data_t.peak2_l = g_ptrSysParam.materialConfficientLower[polling_cnt-1] ;
										}
										else if(g_cal_data_t.raw_peak2 > g_ptrSysParam.materialConfficientUpper[polling_cnt-1] ) {
												g_cal_data_t.peak2_l =  g_ptrSysParam.materialConfficientUpper[polling_cnt-1] ;
										}
										else {
												g_cal_data_t.peak2_l = g_cal_data_t.raw_peak2;
										}
										
										if(g_cal_data_t.raw_peak2 < g_ptrSysParam.materialConfficientLower[polling_cnt]) {
												g_cal_data_t.peak2_r = g_ptrSysParam.materialConfficientLower[polling_cnt] ;
										}
										else if(g_cal_data_t.raw_peak2 > g_ptrSysParam.materialConfficientUpper[polling_cnt] ) {
												g_cal_data_t.peak2_r =  g_ptrSysParam.materialConfficientUpper[polling_cnt] ;
										}
										else {
												g_cal_data_t.peak2_r = g_cal_data_t.raw_peak2;
										}
										
										g_cal_data_t.offset2 = (g_cal_data_t.peak2_l*g_ptrSysParam.materialConfficientK[polling_cnt-1] + g_ptrSysParam.materialConfficientB[polling_cnt-1])*g_cal_data_t.l_val \
														 + (g_cal_data_t.peak2_r*g_ptrSysParam.materialConfficientK[polling_cnt] + g_ptrSysParam.materialConfficientB[polling_cnt])*g_cal_data_t.r_val ;	
										g_ptrRegCentroid.distance_tmp += g_cal_data_t.offset2;
										break;
								}
								else if(g_ptrRegCentroid.distance_tmp > g_ptrSysParam.materialConfficientMarkPoint[8]) {
										if(g_cal_data_t.raw_peak2 > g_ptrSysParam.materialConfficientBound[8]) {
											hr_denote = 1;
										}
										else {
											hr_denote = 0;
										}
										
										if(g_cal_data_t.raw_peak2 < g_ptrSysParam.materialConfficientLower[8]) {
												g_cal_data_t.peak2_r = g_ptrSysParam.materialConfficientLower[8] ;
										}
										else if(g_cal_data_t.raw_peak2 > g_ptrSysParam.materialConfficientUpper[8] ) {
												g_cal_data_t.peak2_r =  g_ptrSysParam.materialConfficientUpper[8] ;
										}
										else {
												g_cal_data_t.peak2_r = g_cal_data_t.raw_peak2;
										}
										
										g_cal_data_t.offset2 = (g_cal_data_t.peak2_r*g_ptrSysParam.materialConfficientK[8] + g_ptrSysParam.materialConfficientB[8]) ;	
										g_ptrRegCentroid.distance_tmp += g_cal_data_t.offset2;
										
										break;
								}
								/*else {
										g_ptrRegCentroid.distance_tmp = 0;
								}*/
								
									
					 }
			  }
				
				if(g_ptrRegCentroid.distance_tmp > 7000) {
						g_ptrRegCentroid.distance_tmp += (g_ptrRegCentroid.distance_tmp-7000)*0.009f;
				}
								
			}
			else {
				g_ptrRegCentroid.distance_tmp = 0;
			}
			

			
			if (g_ptrLidarStatus.lidarMode == kTofScanMode) {
				/*distance filter*/
				if(g_ptrRegCentroid.distance_last >= g_ptrSysParam.measureRange[0] &&  g_ptrRegCentroid.distance_last < g_ptrSysParam.measureRange[1])
				{
					g_ptrRegCentroid.distance_update = g_ptrRegCentroid.distance_last;
					g_cal_data_t.den = (g_ptrRegCentroid.distance_last > g_ptrRegCentroid.distance_pri) ? g_ptrRegCentroid.distance_pri : g_ptrRegCentroid.distance_last;
					
					if(g_cal_data_t.den == 0) {
						g_cal_data_t.den = 1;
					}
					
					g_cal_data_t.slope_ratio = (abs(g_ptrRegCentroid.distance_last - g_ptrRegCentroid.distance_pri)<<7)/((float)g_cal_data_t.den); 
				
					if(g_cal_data_t.slope_ratio > 22.0f)
					{
						if(g_ptrRegCentroid.distance_last < 1000 && g_ptrRegCentroid.distance_last > 300)
						{
							g_ptrRegCentroid.distance_update = 0;
						}	
					}

					
					if(g_cal_data_t.slope_ratio > 100.0f)
					{
						g_ptrRegCentroid.distance_update = 0;
					}

				}
				else {
					g_ptrRegCentroid.distance_update = 0;
				}
			
			
				if(g_ptrRegCentroid.distance_pri == 0 && g_ptrRegCentroid.distance_last !=0 && g_ptrRegCentroid.distance_tmp < 0.001f) // * - * �м���о�����������û����ȥ
				{
					g_ptrRegCentroid.distance_update = 0;
				}
			}
			else {
				g_ptrRegCentroid.distance_update = g_ptrRegCentroid.distance_last;
			}	
				
			/*peak filter*/
			if(g_ptrRegCentroid.distance_update < 80) {
				if(g_ptrRegCentroid.peak_last < 40 ){
					g_ptrRegCentroid.distance_update = 0;
				}
			}
			else if(g_ptrRegCentroid.distance_update < 350) {
				if(g_ptrRegCentroid.peak_last < g_ptrSysParam.histogramAndSpadSampleFreq[0] ){
					g_ptrRegCentroid.distance_update = 0;
				}
			}
			else if(g_ptrRegCentroid.distance_update < 550) {
				if(g_ptrRegCentroid.peak_last < 8 ){
					g_ptrRegCentroid.distance_update = 0;
				}
				/*else if(g_ptrRegCentroid.distance_update < 50 && g_ptrRegCentroid.peakLast < 20) {
					g_ptrRegCentroid.distance_update = 0;
				}*/
			}

				
			g_ptrRegCentroid.out_peak = 255*g_ptrRegCentroid.peak_last/g_ptrSysParam.normalPeakMax;
			if(g_ptrRegCentroid.out_peak > 255) {
					g_ptrRegCentroid.out_peak = 255;
			}

			#if A0_T5_UMOUSE
			if(g_ptrRegCentroid.distance_update <= 250) {
				if(g_ptrRegCentroid.out_peak < 100 ){
					g_ptrRegCentroid.distance_update = 0;
				}
			}
			#endif
			
			if(g_ptrRegCentroid.distance_update > 16000) {
				g_ptrRegCentroid.distance_update = 0;
			}
			#if (defined A0_D6_XX) || (defined A2_D6_XX)|| (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
			else if(g_ptrRegCentroid.distance_update < 50) {
				g_ptrRegCentroid.distance_update = 0;
			}
			#endif

			// g_ptrRegCentroid.out_peak =	g_ptrRegCentroid.confidence_last;
			if (g_ptrLidarStatus.lidarMode == kTofScanMode) {
				#if USING_CSPC_PROTOCOL == 1
				#ifndef	USING_DISTANCE_2_BYTES
				g_cal_data_t.si3 = g_ptrRegCentroid.distance_update>>6;
				g_cal_data_t.si2 = (((g_ptrRegCentroid.distance_update&0xFF)<<2)|(g_ptrRegCentroid.out_peak>>6));
				g_cal_data_t.si1 = (((g_ptrRegCentroid.out_peak&0xFF)<<2)|g_ptrRegCentroid.hr_denote_last);
				g_ptrRegCentroid.distance_si = (g_cal_data_t.si1<<16)|(g_cal_data_t.si3<<8)|g_cal_data_t.si2;
				#else
				g_cal_data_t.si2 = (g_ptrRegCentroid.distance_update>>6)&0xFF;
				g_cal_data_t.si1 = (((g_ptrRegCentroid.distance_update&0x3F)<<2)|g_ptrRegCentroid.hr_denote_last);

				g_ptrRegCentroid.distance_si = (g_cal_data_t.si2<<8)|g_cal_data_t.si1;
				#endif
				#else
				if(g_cal_data_t.is_start_pack) {
					g_regEncoder.startPack = 0;
					g_ptrRegCentroid.hr_denote_last = 1;
				}
				else {
					g_ptrRegCentroid.hr_denote_last = 0;
				}
				
				g_ptrRegCentroid.distance_si = (0x3FFF&g_ptrRegCentroid.distance_update) | (((0x01&g_ptrRegCentroid.hr_denote_last)<<6)<<8) | (g_ptrRegCentroid.out_peak<<16);
												
				#endif

				//SpiRecInterruptCallback(g_cal_data_t.angle_time,g_cal_data_t.encoder_cnt,g_cal_data_t.delta_angle);
				
				/******************/
				#if (defined A0_D6_XX) || (defined A2_D6_XX) || (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
					#if USING_COIN_WHOLE_ENCODER == 1
						if(g_cal_data_t.encoder_cnt == 13) {
							angle_interval = 22.5;
							angle_base = (g_cal_data_t.encoder_cnt-1)*24+22;
						}
						else if(g_cal_data_t.encoder_cnt == 14) {
							angle_interval = 26.5;
							angle_base = g_cal_data_t.encoder_cnt*24;
						}
						else {
							angle_interval = 24;
							angle_base = g_cal_data_t.encoder_cnt*24;
						}
							
						tn_angle = (((float)g_cal_data_t.angle_time)/(g_cal_data_t.delta_angle==0?1:g_cal_data_t.delta_angle))*angle_interval;
						angleValue = (angle_base + tn_angle) + g_ptrSysParam.zeroAngle;
						#else
						angle_interval = 24;
						angle_base = g_cal_data_t.encoder_cnt*24;
						tn_angle = (((float)g_cal_data_t.angle_time)/(g_cal_data_t.delta_angle==0?1:g_cal_data_t.delta_angle))*angle_interval;
						angleValue = (angle_base + (tn_angle > angle_interval ? angle_interval : tn_angle)) + g_ptrSysParam.zeroAngle;
					#endif
				#else 

				angle_interval = 24;
				angle_base = g_cal_data_t.encoder_cnt*24;
				tn_angle = (((float)g_cal_data_t.angle_time)/(g_cal_data_t.delta_angle==0?1:g_cal_data_t.delta_angle))*angle_interval;
				angleValue = (angle_base + (tn_angle > angle_interval ? angle_interval : tn_angle)) + g_ptrSysParam.zeroAngle;

				#endif

				if(angleValue > 360.0f) {
					angleValue -= 360.0f;
				}
				else if(angleValue < 0.0f) {
					angleValue += 360.0f;
				}
				
				#if USING_CSPC_PROTOCOL == 1
				angle_encode = (((uint16_t)(angleValue*64))<<1)|0x01;
				if(g_cal_data_t.is_start_pack) {
						g_regEncoder.startPack = 0;
						if(g_cal_data_t.point_num != 0) {
								#ifndef	USING_DISTANCE_2_BYTES
								g_regLidarDataPtr->BufferLen = g_regLidarDataPtr->LSN*3+10;
								#else 
								g_regLidarDataPtr->BufferLen = g_regLidarDataPtr->LSN*2+10;
								#endif
								g_regLidarDataPtr->Pack_Send_En = 1;
								g_cal_data_t.point_num = 0;
								
								if(g_regLidarDataPtr == &g_regLidarData0) {
									g_regLidarDataPtr = &g_regLidarData1;
								}
								else if(g_regLidarDataPtr == &g_regLidarData1) {
									g_regLidarDataPtr = &g_regLidarData2;
								}
								else if(g_regLidarDataPtr == &g_regLidarData2) {
									g_regLidarDataPtr = &g_regLidarData0;
								}
						}
						
						g_regLidarDataX.F_And_C = (((uint16_t)g_regSpeed.currentSpeed)<<1)|0x01;								
				
						g_regLidarDataX.LSN = 0x01;
						#ifndef	USING_DISTANCE_2_BYTES
						g_regLidarDataX.BufferLen = 13;
						#else 
						g_regLidarDataX.BufferLen = 12;
						#endif 
						g_regLidarDataX.FSA = angle_encode;
						g_regLidarDataX.LSA = angle_encode;
						g_regLidarDataX.SI[0] = g_ptrRegCentroid.distance_si;
						g_regLidarDataX.Pack_Send_En = 1;
						
				
				}
				else {
						
						g_cal_data_t.point_num++;
						g_regLidarDataPtr->LSN = g_cal_data_t.point_num;
						if(g_cal_data_t.point_num == 1) {
								g_regLidarDataPtr->FSA = angle_encode;
						}
						g_regLidarDataPtr->LSA = angle_encode;
						g_regLidarDataPtr->SI[g_cal_data_t.point_num-1] = g_ptrRegCentroid.distance_si;
						g_regLidarDataPtr->F_And_C = 0x00;					
						
					
						if(g_cal_data_t.point_num == LIDAR_DATA_NUM) {
							
								g_cal_data_t.point_num = 0;
								#ifndef	USING_DISTANCE_2_BYTES
								g_regLidarDataPtr->BufferLen = g_regLidarDataPtr->LSN*3+10;
								#else 
								g_regLidarDataPtr->BufferLen = g_regLidarDataPtr->LSN*2+10;
								#endif
								g_regLidarDataPtr->Pack_Send_En = 1;	
							
								if(g_regLidarDataPtr == &g_regLidarData0) {
									g_regLidarDataPtr = &g_regLidarData1;
								}
								else if(g_regLidarDataPtr == &g_regLidarData1) {
									g_regLidarDataPtr = &g_regLidarData2;
								}
								else if(g_regLidarDataPtr == &g_regLidarData2) {
									g_regLidarDataPtr = &g_regLidarData0;
								}
						}
				}
				#else
				angle_encode = ((uint16_t)(angleValue*64)+0xA000);
					
				if(g_cal_data_t.point_num < (LIDAR_DATA_NUM-1)) {
					if(g_cal_data_t.point_num == 0) {
						g_regLidarDataPtr->first_angle = angle_encode;
					}
					g_regLidarDataPtr->data[g_cal_data_t.point_num] = g_ptrRegCentroid.distance_si;
					g_cal_data_t.point_num++;
				}
				else {			
					g_regLidarDataPtr->last_angle = angle_encode;
					g_regLidarDataPtr->data[g_cal_data_t.point_num] = g_ptrRegCentroid.distance_si;
					g_regLidarDataPtr->speed = (uint16_t)(g_regSpeed.speed_f*60*64); 				
					g_regLidarDataPtr->send_end = 1;
					g_cal_data_t.point_num = 0;
					
					if(g_regLidarDataPtr == &g_regLidarData0) {
						g_regLidarDataPtr = &g_regLidarData1;
					}
					else if(g_regLidarDataPtr == &g_regLidarData1) {
						g_regLidarDataPtr = &g_regLidarData2;
					}
					else if(g_regLidarDataPtr == &g_regLidarData2) {
						g_regLidarDataPtr = &g_regLidarData0;
					}						
				}
				#endif
			}
			else {
				sp_data_transmit();
			}
			
			
			
			
			
			/******************/
			
			
			//cal tof flow
			g_ptrRegCentroid.distance_pri = g_ptrRegCentroid.distance_last;
			g_ptrRegCentroid.distance_last = (uint16_t)g_ptrRegCentroid.distance_tmp;
			//cal peak flow
			g_ptrRegCentroid.peak_pri = g_ptrRegCentroid.peak_last;
			g_ptrRegCentroid.peak_last = g_ptrRegCentroid.peak;
			
			//cal hr flow
			g_ptrRegCentroid.hr_denote_pri = g_ptrRegCentroid.hr_denote_last;
			g_ptrRegCentroid.hr_denote_last = hr_denote;
			
			//cal confidence flow
			g_ptrRegCentroid.confidence_pri = g_ptrRegCentroid.confidence_last;
			g_ptrRegCentroid.confidence_last = g_cal_data_t.confidence;
			

			
						
			
			if(g_regVi4302.isWriteReg == true){
				g_regVi4302.isWriteReg = false;
				g_ptrSpi.wRegister(g_regVi4302.buff[1]|g_regVi4302.buff[2]<<8,g_regVi4302.buff[0],NULL);
			}
			
			if(g_regVi4302.isReadReg == true){
				g_regVi4302.isReadReg = false;
				g_ptrSpi.rRegister(g_regVi4302.buff[1]|g_regVi4302.buff[2]<<8,&r_value[0],NULL);
				FdbMessage(kD2H|kHRS|kHSS,0xAB,(uint8_t *)&r_value,REGISTER_BYTE_CNT,kResponse);
			}
			
			
			g_ptrSysFlag.isGpioTrig0 = false;
		}
		
}




void TransmitData(void)
{
	#if USING_CSPC_PROTOCOL == 1
	//if(g_ptrSysFlag.isComTransmitFinished == true) {
	if(DMA_Flag_Status_Get(DMA, DMA_CH3_TXCF) == SET) {
		if (g_ptrLidarStatus.lidarMode == kTofScanMode || g_ptrLidarStatus.lidarMode == kTofCalibrationMode) {
			if(g_regLidarData0.Pack_Send_En == 1 && g_regSpeed.isStalled == false) {
					g_regLidarData0.Pack_Send_En = 0;		
					FillLidarBuf(g_regLidarData0, LidarSendBuf);
					g_ptrCom.ComTransmitDmaData(LidarSendBuf, g_regLidarData0.BufferLen);
			}
			else if(g_regLidarData1.Pack_Send_En == 1 && g_regSpeed.isStalled == false) {
					g_regLidarData1.Pack_Send_En = 0;		
					FillLidarBuf(g_regLidarData1,LidarSendBuf);
					g_ptrCom.ComTransmitDmaData(LidarSendBuf, g_regLidarData1.BufferLen);
			}
			else if(g_regLidarData2.Pack_Send_En == 1 && g_regSpeed.isStalled == false) {
					g_regLidarData2.Pack_Send_En = 0;	
					FillLidarBuf(g_regLidarData2,LidarSendBuf);
					g_ptrCom.ComTransmitDmaData(LidarSendBuf, g_regLidarData2.BufferLen);		
			}	
			else if(g_regLidarDataX.Pack_Send_En == 1 && g_regLidarData0.Pack_Send_En == 0 
				&& g_regLidarData1.Pack_Send_En == 0 && g_regLidarData2.Pack_Send_En == 0 && g_regSpeed.isStalled == false) {
					g_regLidarDataX.Pack_Send_En = 0;	
					FillLidarBuf(g_regLidarDataX,LidarSendBuf);
					g_ptrCom.ComTransmitDmaData(LidarSendBuf, g_regLidarDataX.BufferLen);
			}
			else if(g_stParseAck.ackTimes > 0) {			
					ParseAck();
			}
			else if(getAbsolutionTime() - g_ptrDDSInfoCount.ddsCnt > DDS_OUPUT_CYCLE) {

						g_ptrDDSInfoCount.ddsCnt = getAbsolutionTime();
						if(g_regSpeed.isStalled == false) {
							DDSCodePackage(LidarSendBuf);
							g_ptrCom.ComTransmitDmaData(LidarSendBuf, 11);
						}
			}
		}
		else {
			if(g_stParseAck.ackTimes > 0) {			
					ParseAck();
			}
		}
	}
	#else
	uint16_t lidarBuffSize = 0; 
	//if(g_ptrSysFlag.isComTransmitFinished == true) {
	if(DMA_Flag_Status_Get(DMA, DMA_CH3_TXCF) == SET) {
		if(g_regLidarData0.send_end==1 && g_regSpeed.isStalled == false) {
				g_regLidarData0.send_end=0;		
				lidarBuffSize = Lidar_ChecksumCal(g_regLidarData0,LidarSendBuf);
				Usart_DMA_Send_Data(LidarSendBuf,lidarBuffSize);			
		}
		else if(g_regLidarData1.send_end == 1 && g_regSpeed.isStalled == false) {
				g_regLidarData1.send_end=0;		
				lidarBuffSize = Lidar_ChecksumCal(g_regLidarData1,LidarSendBuf);
				Usart_DMA_Send_Data(LidarSendBuf,lidarBuffSize);	
		}
		else if(g_regLidarData2.send_end == 1 && g_regSpeed.isStalled == false) {
				g_regLidarData2.send_end=0;	
				lidarBuffSize = Lidar_ChecksumCal(g_regLidarData2,LidarSendBuf);
				Usart_DMA_Send_Data(LidarSendBuf,lidarBuffSize);			
		}
		else if(g_stParseAck.ackTimes > 0) {			
					ParseAck();
		}
		/*else if(getAbsolutionTime() - g_ptrDDSInfoCount.ddsCnt > DDS_OUPUT_CYCLE) {
					g_ptrDDSInfoCount.ddsCnt = getAbsolutionTime();
					if(g_regSpeed.isStalled == false) {
						DDSCodePackage(LidarSendBuf);
						g_ptrCom.ComTransmitDmaData(LidarSendBuf, 11);
					}
		}*/
	}
	#endif	
}


void DDSCodePackage(uint8_t *buff)
{
	uint8_t sumTmp;
	uint16_t vref = (uint16_t)(GettingAdcVref()*100);
	vref = ((~vref) - 122);
	buff[0] = 0XA5;
	buff[1] = 0x5A;
	buff[2] = 0X04;
	buff[3] = 0X00;
	buff[4] = (uint8_t)(vref>>8);
	buff[5] = (uint8_t)(vref);
	buff[6] = 0X06;
	
	buff[8] = 0x01;//���ֽ���Ҫ���ݾ�����������
	
	if(g_ptrSysFlag.errorCode == 0)
	{
		buff[8] = 0x00;
	}
	else
	{
		buff[8] = 0x02;
	}
	
	buff[9] = g_ptrSysFlag.errorCode; //��ý���תλ���� 
	buff[10] = g_ptrSysFlag.errorCode>>8;
	
	sumTmp =  buff[0]^buff[1]^buff[2]^buff[3]^buff[4]^buff[5]^buff[6]^buff[8]^buff[9]^buff[10];
	
	buff[7] = sumTmp;
}

void sp_data_transmit(void)
{
	static sp_data_t sp_data;
	sp_data.header = 0xA5;
	sp_data.id = 0x01;
	sp_data.time_diff = 0;
	sp_data.distance = 0;
	sp_data.peak = 0;
	sp_data.time_diff = g_sys_time_t.time_diff;
	sp_data.distance = g_ptrRegCentroid.distance_update;
	sp_data.peak = g_ptrRegCentroid.out_peak;
	
	sp_data.check_sum = sp_data.header+sp_data.id+(sp_data.time_diff>>8)+(uint8_t)sp_data.time_diff+ \
													(sp_data.distance>>8)+(uint8_t)sp_data.distance + \
													(sp_data.peak>>8)+(uint8_t)sp_data.peak;
	g_ptrCom.ComTransmitDmaData((uint8_t*)&sp_data, 9);	
	WaitTransmit();
	
}