#include "main.h"

//*****************bsp ****************//
#include "ADC.h"
#include "GPIO.h"
#include "bsp_freq.h"
#include "cmd_handle.h"
#include "delay.h"
#include "n32g401.h"
#include "timer.h"
#include "usart.h"


//*****************common ****************//
#include "ring_buff.h"

//******************app ***************//
#include "calibration_process.h"
#include "communication.h"
#include "facula_process.h"
#include "histogram_process.h"
#include "scan_process.h"
#include "singlePoint_process.h"
#include "tasks_polling.h"


//******************api ***************/
#include "VI4302_System.h"
#include "VI4302_handle.h"

//*********************************** */

/**
 * @brief 向量表偏移地址
 *
 * @param NVIC_VectTab
 * @param Offset
 */
void NVIC_SetVectorTable(uint32_t NVIC_VectTab, uint32_t Offset) {
    SCB->VTOR = NVIC_VectTab | Offset;
}

/**
 * @brief 主函数
 *
 * @return int
 */
int main(void) {
#ifdef BOOT_APP
    NVIC_SetVectorTable(NVIC_VECTTAB_FLASH, 0x4000);
#endif

    //外部16M晶振,72M主频
    //  SetSysClockToPLL();

    /* NVIC initial */
    NVIC_Priority_Group_Set(NVIC_PER2_SUB2_PRIORITYGROUP);

    /* WDT initial */
    WDT_Init();

    /* initial */
    delay_ms(10);
    RCO_OutputInit();
    delay_ms(10);

    /* task system initial */
    TaskSystemInit();

    /* flash initial */
    flash_DataInit();
    flash_SnDataInit();
    flash_FmDataInit();

    /* IO initial */
    GPIO_Init();

    /* params initial */
    SystemParamInit();

    /* ADC initial */
    ADC_AllInit();

    /* 4302 initial */
    /** 注册触发模式回调函数，设置触发定时器 - 配置8Mhz时钟给vi4302(参考时钟-XREF ) */
    TrigModeTimer(8 - 1);  // // 64M/8 = 8M

    /* 电机PWM初始化 */
#if MOTOR_DRIVER
    motor_init();
    /* 注册编码器相关回调函数 */
    RegisterEncoderCallbackFunc(&g_ptrEncoder, EncoderMarkEdgeCallbackFunc, EncoderCalcEdgeUpCallbackFunc, EncoderCalcEdgeCallbackFunc, 0);
#endif

    /* 注册SPI接收回调函数 */
    RegisterSpiRecCallbackFunc(&g_ptrSpi, NULL);
    g_ptrSpi.InitSpi(NULL, 0);

    /* communication initial */
    g_comm_ptr->CommInit(ECommType::eUART, 230400);

    /* 温度采集相关注册 */
    RegisterAdcCallBackFunc(&g_ptrAdc, NULL);

    TIM6_Configuration();  // 1ms定时器

    /* 设置系统的默认模式 */
    if ((g_ptrLidarStatus.lidarMode != kTofStopMode) && (g_ptrLidarStatus.lidarMode != kTofHistMode) && (g_ptrLidarStatus.lidarMode != kTofFaculaMode) &&
        (g_ptrLidarStatus.lidarMode != kTofScanMode) && (g_ptrLidarStatus.lidarMode != kTofCalibrationMode) &&
        (g_ptrLidarStatus.lidarMode != kTofSinglePointMode) && (g_ptrLidarStatus.lidarMode != kTofRangingMode)) {
        g_ptrLidarStatus.lidarMode = kTofCalibrationMode;
    }

    /* 根据不同的工作模式执行相应的处理函数 */
    switch (g_ptrLidarStatus.lidarMode) {
    case kTofStopMode:  // 停止模式
        StandbyPolling();
        break;
    case kTofFaculaMode:  // 光斑模式
        FaculaProcess();
        break;
    case kTofHistMode:  // 直方图模式
        HistogramProcess();
        break;
    case kTofCalibrationMode:  // 动态校准模式
        // CalibrationProcess();
        break;
    case kTofRangingMode:
        // RangeProcess();
        break;
    case kTofScanMode:  // 扫描模式
        // ScanProcess();
        break;

#ifdef A0_D4_SAMSUNG
    case kTofRevCheckMode:
        revCheckProcess();
        break;
#endif
    default:
        StandbyPolling();
        break;
    }
}

/**
 * @brief VI
 *
 * @return int
 */
int main_vi(void) {
#ifdef BOOT_APP
    NVIC_SetVectorTable(NVIC_VECTTAB_FLASH, 0x4000);
#endif

    //外部16M晶振,72M主频
    //  SetSysClockToPLL();

    NVIC_Priority_Group_Set(NVIC_PER2_SUB2_PRIORITYGROUP);

    WDT_Init();

    delay_ms(10);
    RCO_OutputInit();
    delay_ms(10);
    flash_DataInit();
    flash_SnDataInit();
    flash_FmDataInit();
    ALL_ParaInit();

    USART2_Init(460800);
    ADC_AllInit();

    GPIO_Init();

    if (VI4302_AllInit() == FIRMWARE_STATUS_ACK) {
        VI4302_Start_Ranging();
    }

    g_ControlPara.vi4302_ranging_state = 1;

    TIM6_Configuration();  // 1ms定时器

    while (1) {
        UART_AllHandle();
        Execute_instruction();  //执行指令
    }
}

// 1ms定时器
void TIM6_IRQHandler(void) {
    static uint16_t ms_count = 0;

    if (TIM_Interrupt_Status_Get(TIM6, TIM_INT_UPDATE) != RESET) {
        TIM_Interrupt_Status_Clear(TIM6, TIM_INT_UPDATE);

        ms_count++;
        if (10 <= ms_count) {
            ms_count = 0;
        }
    }
}

//外部中断
void EXTI2_IRQHandler(void) {
    if (RESET != EXTI_Interrupt_Status_Get(EXTI_LINE2)) {
        EXTI_Interrupt_Status_Clear(EXTI_LINE2);
        EXTI_FLAG++;
    }
}

//外部中断
void EXTI0_IRQHandler(void) {
    if (RESET != EXTI_Interrupt_Status_Get(EXTI_LINE0)) {
        EXTI_Interrupt_Status_Clear(EXTI_LINE0);

        gpio0_int_cnt++;                           //该标志位置位用来计数，表示多少次数据周期后执行一次动作
        g_ControlPara.vi4302_data_valid_flag = 1;  //该标志位置位，代表4302芯片数据准备好，可以通过SPI接口读取。
    }
}
