#include "ring_buff.h"

#include "typedef.h"

#define PROTOCOL_HEADER 0xA5

static _Bool IsRingBufferFull(StRingBuff *ringBuf) {
    if (ringBuf == NULL) {
        return false;
    }

    if (((ringBuf->in + 1) % RING_BUFFER_BUFF_SIZE) == ringBuf->out) {
        return true;  //满
    }
    return false;
}

static _Bool WriteOneByteToRingBuffer(StRingBuff *ringBuf, uint8_t data) {
    uint16_t fullSize = RING_BUFFER_BUFF_SIZE;
    if (ringBuf == NULL) {
        return false;
    }

    //写之前先判断队列是否写满
    if (IsRingBufferFull(ringBuf)) {
        return false;
    }

    ringBuf->in                  = (ringBuf->in) % fullSize;  //防止越界
    ringBuf->buffer[ringBuf->in] = data;
    ringBuf->in++;


    return true;
}


static _Bool IsRingBufferEmpty(StRingBuff *ringBuf) {
    if (ringBuf == NULL) {

        return false;
    }

    //写入位置和读出位置相等时为空
    if (ringBuf->in == ringBuf->out) {
        return true;
    }
    return false;
}

static _Bool ReadOneByteFromRingBuffer(StRingBuff *ringBuf, uint8_t *data) {
    if (ringBuf == NULL) {
        return false;
    }

    //读之前判断队列是否为空
    if (IsRingBufferEmpty(ringBuf)) {
        return false;
    }
    ringBuf->out = (ringBuf->out) % RING_BUFFER_BUFF_SIZE;  //防止越界
    *data        = ringBuf->buffer[ringBuf->out];
    ringBuf->out++;

    return true;
}

static void WriteRingBuffer(StRingBuff *ringBuf, uint8_t *writeBuf, uint16_t len) {
    unsigned int i;

    if (ringBuf == NULL) {
        return;
    }

    for (i = 0; i < len; i++) {
        WriteOneByteToRingBuffer(ringBuf, writeBuf[i]);
    }
}

int16_t        length;
static int16_t ReadRingBuffer(StRingBuff *ringBuf, uint8_t *readBuf, uint16_t len) {
    /*unsigned int i;
        if(ringBuf == NULL)
    {
        return;
    }

    for(i = 0; i < len; i++)
    {
            ReadOneByteFromRingBuffer(ringBuf,&readBuf[i]);
    }*/

    uint16_t i = 0, j = 0, size = len;
    if (ringBuf == NULL) {
        return -1;
    }

    for (i = 0; i < len; i++) {
        ReadOneByteFromRingBuffer(ringBuf, &readBuf[j]);
        if (readBuf[j] == PROTOCOL_HEADER) {  // search protocol header
            size -= i;
            break;
        } else {
            j = 0;
        }
    }
    if (i == len) {  // no header return
        return -1;
    }


    for (i = 1; i < 6; i++) {  // read information before data
        ReadOneByteFromRingBuffer(ringBuf, &readBuf[i]);
    }
    length = (readBuf[4] | readBuf[5] << 8) * 2 + 6;


    if (length < size) {
        for (i = 6; i < length; i++) {
            ReadOneByteFromRingBuffer(ringBuf, &readBuf[i]);
        }
        return (size - length);
    } else {
        for (i = 6; i < size; i++) {
            ReadOneByteFromRingBuffer(ringBuf, &readBuf[i]);
        }
        return 0;
    }
}

static const API_Ringbuff_T ringBuffer_api_ptr = {
    .RingbuffReadData  = ReadRingBuffer,
    .RingbuffWriteData = WriteRingBuffer,
}

const API_Ringbuff_T *const g_ringBuffer_ptr = &ringBuffer_api_ptr;