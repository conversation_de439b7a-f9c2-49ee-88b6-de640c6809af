/*
 * @Author: xx
 * @Date: 2024-05-21 20:06:29
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-07-25 11:47:01
 * @Description: 
 * @FilePath: \MDK-ARMd:\Project_data\05_project_code\local_SVN\vscode_workspace\fw01_dToF_lidar\App\src\histogram_process.c
 */
#include "histogram_process.h"
#include "facula_process.h"
#include "scan_process.h"
#include "bsp_vi4302.h"
#include "bsp_io.h"
#include "bsp_adc.h"
#include "systick.h"




void HistogramProcess(void)
{
	uint8_t value = 0;
	uint16_t m = 0;
	uint8_t r_value[2]={0};
	uint16_t hist_addr = 0;
	
	Vi4302RangingConfig();
	Vi4302StopRanging();
	Vi4302StartHist(kHistNoraml);
	g_ptrSysFlag.isGpioTrig0 = false;
	g_ptrLidarStatus.hist_channel = kHistNoraml;
	
	for(;;) {
		
		g_ptrTimeTask.TaskPoll(); 
		ParseAck();
		
		if(g_ptrSysFlag.isGpioTrig0 == true) {
			
			g_ptrSpi.wRegister(0x50,0x01,NULL);/*enable hist*/
			
			g_ptrSpi.rRegister(0x50,&value,NULL);
			if((value&0x01) != 0x01) {
				continue;
			}

			if(g_ptrLidarStatus.hist_channel == kHistNoraml) {
				hist_addr = 0;
			}
			else if(g_ptrLidarStatus.hist_channel == kHistAtten) {
				hist_addr = 5*2048;
			}
			else if(g_ptrLidarStatus.hist_channel == kHistRef) {
				hist_addr = 6*2048;
			}

			cache[4] = SPI_4302_HIST_READ;
			cache[5] = hist_addr>>8;
			cache[6] = hist_addr;//start at addr 400
			g_ptrSpi.SpiReadBytes(&cache[4],2000+3,&cache[3],2000+3);
			
			g_ptrSpi.wRegister(0x50,0x00,NULL);/*disable hist*/
			
			
			cache[0] = 0xA5;
			cache[1] = kD2H|kDFF;
			cache[2] = 0xAC;
			
			cache[4] = 0xE8;
			cache[5] = 0x03;
			cache[3] = cache[0]^cache[1]^cache[2]^cache[4]^cache[5];
			for(m=0; m<2000; m++) {	
				cache[3] ^= cache[6+m];
			}

			g_ptrCom.ComTransmitDmaData(&cache[0],sizeof(cache));
			WaitTransmit();

			
			memset(cache,0,sizeof(cache));
			
			if(g_regVi4302.isWriteReg == true){
				g_regVi4302.isWriteReg = false;
				g_ptrSpi.wRegister(g_regVi4302.buff[1]|g_regVi4302.buff[2]<<8,g_regVi4302.buff[0],NULL);
			}
			
			if(g_regVi4302.isReadReg == true){
				g_regVi4302.isReadReg = false;
				g_ptrSpi.rRegister(g_regVi4302.buff[1]|g_regVi4302.buff[2]<<8,&r_value[0],NULL);
				FdbMessage(kD2H|kHRS|kHSS,0xAB,(uint8_t *)&r_value,REGISTER_BYTE_CNT,kResponse);
			}
			
			
			
			if(g_ptrAdc.isSampleFinished == true) {
					g_ptrAdc.isSampleFinished = false;
					WaitTransmit();
					g_ptrSysParam.currentTemperature = -0.0332f*GettingAdcValue() + 87.1023f;//((float)TEMPERATURE_TABLE[(GettingAdcValue() - 214)>>1])/10;
					FdbMessage(kD2H|kDFF,0xAA,(uint8_t *)&g_ptrSysParam.currentTemperature,TEMPERATURE_BYTE_CNT,kWord);
					
			}
			
			g_ptrSysFlag.isGpioTrig0 = false;
			
		}
		if(g_ptrSysFlag.isDetectError == true) {
			g_ptrSysFlag.isDetectError = false;
			DDSCodePackage(LidarSendBuf);
			g_ptrCom.ComTransmitDmaData(LidarSendBuf, 11);
		}
	}
	
}


