.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: .\VI4302 API\src\VI4302_Config.c
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: VI4302 API/inc/VI4302_Config.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: user/inc/main.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_spi.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/device/n32g401.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/core/core_cm4.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/core/cmsis_version.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/core/cmsis_compiler.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/core/cmsis_armcc.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/core/mpu_armv7.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/device/system_n32g401.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/device/n32g401_conf.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_adc.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/CMSIS/device/n32g401.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_comp.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_crc.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_dbg.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_dma.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_exti.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_flash.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_gpio.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_i2c.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_iwdg.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_pwr.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_rcc.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_rtc.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_spi.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_tim.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_usart.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_wwdg.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/n32g401_beeper.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../firmware/n32g401_std_periph_driver/inc/misc.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../bsp/inc/GPIO.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../bsp/inc/usart.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../bsp/inc/spi.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../bsp/inc/timer.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../bsp/inc/flash.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../bsp/inc/delay.h
.\build\A5_RLT\.obj\VI4302 API\src\VI4302_Config.o: ../bsp/inc/WDT.h
