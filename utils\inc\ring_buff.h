#ifndef _RING_BUFF_H
#define _RING_BUFF_H

#include <stdbool.h>
#include <stdint.h>



#ifndef RING_BUFFER_BUFF_SIZE
#define RING_BUFFER_BUFF_SIZE 1152  // 0x480
#endif

typedef struct {
    uint16_t in;                             //
    uint16_t out;                            //
    uint8_t  buffer[RING_BUFFER_BUFF_SIZE];  //
} StRingBuff;


typedef struct {
    int16_t (*RingbuffReadData)(StRingBuff *, uint8_t *, uint16_t);
    void (*RingbuffWriteData)(StRingBuff *, uint8_t *, uint16_t);
} API_Ringbuff_T;

extern const API_Ringbuff_T *const g_ringBuffer_ptr;

#endif
