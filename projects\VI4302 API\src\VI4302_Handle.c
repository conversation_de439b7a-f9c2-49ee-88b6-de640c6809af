#include "VI4302_handle.h"
#include "ADC.h"
#include "GPIO.h"
#include "User_Driver.h"
#include "VI4302_System.h"
#include "bin.h"
#include "flash.h"


#include "cmd_handle.h"

volatile uint8_t SPI_Send_Complete    = 0;
volatile uint8_t SPI_Receive_Complete = 0;
volatile uint8_t gpio0_int_cnt        = 0;
volatile uint8_t EXTI_FLAG            = 0;


uint8_t spi_write_data(uint8_t *data, uint16_t size) {
    uint8_t ret = 0;

    VI4302_CsLow();
    ret = SPI1_WriteNByte(data, size);

    VI4302_CsHigh();

    return ret;
}

void vi4302_HW_set_demo(void) {
    VI4302_Disen();  // reset 4302
    //	delay_ms(20);
    delay_ms(800);
    VI4302_En();  // reset 4302
    //	delay_ms(10);
}

uint8_t vi4302_write_cmd(uint8_t cmd) {
    uint8_t txbuf[1] = {0};
    uint8_t ret      = 0;

    VI4302_CsLow();

    txbuf[0] = cmd;

    ret = SPI1_WriteNByte(txbuf, 1);

    VI4302_CsHigh();

    return ret;
}

/**
 * @brief  Read register from VI4302.
 * @param  reg_addr
 * @retval reg_value
 */
uint8_t vi4302_read_register(uint16_t reg_addr) {
    uint8_t txbuf[4] = {0};
    uint8_t rxbuf[4] = {0};

    VI4302_CsLow();

    txbuf[0] = 0x00;
    txbuf[1] = (reg_addr & 0xff00) >> 8;
    txbuf[2] = (reg_addr & 0x00ff);
    txbuf[3] = 0xff;

    // SPI超时错误问题需改！！！！！！！！！！！！
    SPI1_ReadWriteNByte(txbuf, rxbuf, 4);

    VI4302_CsHigh();

    return rxbuf[3];
}

/**
 * @brief  Write VI4302 register.
 * @param  reg_addr - address
 * @param  val - value to be write
 * @retval None
 */
uint8_t vi4302_write_register(uint16_t reg_addr, uint8_t val) {
    uint8_t txbuf[4] = {1};
    uint8_t rxbuf[4] = {0};
    uint8_t ret      = 0;

    VI4302_CsLow();

    txbuf[0] = 0x01;
    txbuf[1] = (reg_addr & 0xff00) >> 8;
    txbuf[2] = (reg_addr & 0x00ff);
    txbuf[3] = val;

    ret = SPI1_ReadWriteNByte(txbuf, rxbuf, 4);

    VI4302_CsHigh();
    return ret;
}

/**
 * @brief  Read mul register.
 * @param  reg_addr - address
 * @param  Rx_Data - pointer to the read buffer
 * @param  Rx_Lenth - read size
 * @param  Tx_Data - Tx cmd
 * @retval None
 */
uint8_t vi4302_read_mul_reg(uint16_t Reg_Addr, uint8_t *Rx_Data, uint16_t Rx_Lenth, uint8_t *Tx_Data) {
    uint8_t txbuf[3] = {0};
    //	uint8_t rxbuf[3] = { 0 };
    uint8_t ret = 0;

    VI4302_CsLow();

    txbuf[0] = 0x00;
    txbuf[1] = (Reg_Addr & 0xff00) >> 8;
    txbuf[2] = (Reg_Addr & 0x00ff);

    ret = SPI1_WriteNByte(txbuf, 3);

    ret = SPI1_ReadNByte(Rx_Data, Rx_Lenth);
    VI4302_CsHigh();

    return ret;
}

uint8_t vi4302_read_his_reg(uint16_t Reg_Addr, uint8_t *Rx_Data, uint16_t Rx_Lenth, uint8_t *Tx_Data) {
    uint8_t ret = 0;

    VI4302_CsLow();


    ret = SPI1_WriteNByte(Tx_Data, 3);

    ret = SPI1_ReadNByte(Rx_Data, Rx_Lenth);

    VI4302_CsHigh();

    return ret;
}
/**
 * @brief  Get ranging data with firmware.
 * @param  data - pointer to the data buffer
 * @retval None
 */
uint8_t vi4302_read_ranging_data_with_firmware(uint8_t *Rx_Data) {
    uint8_t tx_buf[3] = {0};
    uint8_t ret       = 0;

    VI4302_CsLow();

    tx_buf[0] = 0x00;
    tx_buf[1] = (0X30 & 0xff00) >> 8;
    tx_buf[2] = (0X30 & 0x00ff);

    ret = SPI1_WriteNByte(tx_buf, 3);
    ret = SPI1_ReadNByte(Rx_Data, 16);

    VI4302_CsHigh();

    return ret;
}


/**
 * @brief  user driver function point init.
 * @param  None
 * @retval None
 */
void user_driver_init(void) {
    Chip_En           = vi4302_HW_set_demo;
    Spi_Read_Reg      = vi4302_read_register;
    Spi_Write_Reg     = vi4302_write_register;
    Spi_Read_Mul_Reg  = vi4302_read_mul_reg;
    Spi_Send_Cmd      = vi4302_write_cmd;
    Spi_Write_Mul_Reg = spi_write_data;
}

uint8_t test = 0;
uint8_t VI4302_AllInit(void) {
    uint8_t  VI4302_Update_flg = 0;  //内部固件下载 成功|失败 标志位
    uint16_t Fw_size           = 0;  //芯片内部固件的大小
    uint16_t VI4302_Frame      = 0;  //芯片返回的实际帧率
                                     //	uint16_t VI4302_FTDC = 0;		    //芯片FTDC 0x20A
    uint16_t R_Status = 0;           //设置帧率的状态


    SPI1_Init();
    VI4302_InfifoClear();
    user_driver_init();  // API 函数指针 实例化

    //	VI4302_Enable_DcDc();
    delay_ms(10);
    VI4302_Pin_Init();
    delay_ms(50);
    VI4302_Enable_DcDc();
    //	test = Spi_Read_Reg(0);
    //	Spi_Read_Reg(0); //测试VI4302是否通信成功

    //

    if (g_ControlPara.vi4302_work_mode == _SINGLE_PIXEL_MODE)  //单pixel模式
    {
        VI4302_Reg_Init(g_ControlPara.vi4302_work_mode);
        VI4302_BVD_Calculate();  //外部MCU进行BVD校准
    } else                       //测距模式
    {
        //		flash_ReadFmData();

        // GPIO 0 外部中断
        Fw_size = Get_Fw_bytes();                                   //获取内部固件大小【Byte】
        VI4302_Update_Config(1);                                    //更新内部固件前 对4302进行配置，使其准备好
        VI4302_Update_firmware((uint8_t *)vis_sensor_fw, Fw_size);  //下载
        VI4302_Update_Config(0);                                    //更新完、再次配置4302
        VI4302_Update_flg = VI4302_Update_Succcess_Fail();          //#* Return         : uint8_t 0x11 success  0x12 fail  0x00 timeout.

        if (VI4302_Update_flg != 0x11)  //升级失败
        {
            while (1)
                ;  //后续做别的处理
        }

        delay_ms(50);

        VI4302_Enable_DcDc();
        VI4302_Reg_Init(g_ControlPara.vi4302_work_mode);  //寄存器配置

        if (g_fmNeedSaved.vi4302_para_save == 0xFF)  //第一次上电，获取一次参数值并保存
        {
            g_fmNeedSaved.vi4302_para_save = 0x01;

            VI4302_TDC_Cal(&g_fmNeedSaved.vi4302_tdc_save);  //获取TDC标定值
            VI4302_Bvd_Cal(&g_fmNeedSaved.vi4302_bvd_save);  //获取BVD标定值 内部默认加12档 r03

            g_fmNeedSaved.vi4302_temp_save =
                (NTC_TempGet() + 50) / 100;  //获取BVD标定时的温度【有符号单字节温度，不含小数【bvd会随温度调整：10℃ 调整一个档，内部固件来做】】
            flash_SaveFmData();  //将工作模式 写入flash
            Bvd_Cal.OTP_BVD  = g_fmNeedSaved.vi4302_bvd_save;
            Bvd_Cal.OTP_Temp = g_fmNeedSaved.vi4302_para_save;
        } else {
            VI4302_Write_Reg(0x24f, g_fmNeedSaved.vi4302_bvd_save);
            VI4302_Write_Reg(0x242, g_fmNeedSaved.vi4302_tdc_save);
            Bvd_Cal.OTP_BVD  = g_fmNeedSaved.vi4302_bvd_save;
            Bvd_Cal.OTP_Temp = g_fmNeedSaved.vi4302_temp_save;
        }


        //		VI4302_Reg_Init(g_ControlPara.vi4302_work_mode);//寄存器配置

        Adaptation_REG_and_ALGO(&Al_Pa);  //算法适配，MA  PEAK缩放、LSB

        //		Search_logic_init( Al_Pa );

        //		R_Status = VI4302_Frame_Rate_AutoCtrl(2020, &VI4302_Frame);
        R_Status = VI4302_Frame_Rate_AutoCtrl(3600, &VI4302_Frame);

        opt_code = USER_RANGING_CMD;
    }

    TX_En();
    delay_ms(50);

    //	if( g_ControlPara.vi4302_work_mode == g_ControlPara.vi4302_work_mode )
    //	{
    //		if(VI4302_Start_Ranging() == FIRMWARE_STATUS_ACK)
    //		{
    //			printf("A4!\r\n");
    //		}
    //		else
    //		{
    //			printf("A5!\r\n");
    //		}
    //	}
    //	else if( g_ControlPara.vi4302_work_mode == _SINGLE_PIXEL_MODE)
    //	{
    //
    //	}

    //	delay_ms(20);


    return R_Status;
}
