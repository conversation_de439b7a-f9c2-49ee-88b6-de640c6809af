#include "communication.h"

//*****************bsp ****************//
#include "flash.h"
#include "usart.h"

//*****************common ****************//
#include "protocol.h"
#include "ring_buff.h"

//*****************app ****************//


//***********************************  */

static StCommFifo s_comm_fifo = {
    .rec_buffe_size = {0},
    .rec_count      = 0,
    .ring_buffer =
        {
            .buffer = {0},
            .in     = 0,
            .out    = 0,
        },
};


void CommInit(ECommType type, uint32_t param1) {
    /* init port */
    switch (type) {
    case eUART:
        USART2_Init(param1);  //

        USART2_RecHandlePtrInit(ComRecInterruptCallback);
        break;
    default:
        break;
    }

    /* init fifo */
}

/**
 * @brief '{'有显示问题，实际正常
 *
 */
static void ParseProtocol(void) {
    /**/
    uint8_t check      = 0;
    int16_t datalength = -1;

    uint16_t n                   = 0;
    uint16_t totalNum            = 0;
    uint8_t  mode_change_buff[4] = {0x00};
    uint16_t fps_4302 = 0, fps = 0;

    FLASH_STS fmcStaus = FLASH_BUSY;

    if (g_stComFifo.rec_count > 0 && g_stParseAck.ackTimes == 0) {
        datalength = g_ringBuffer_ptr->ReadRingBuffer(&g_stRingBuff, (uint8_t *)&g_stComFifo.FRAME, g_stComFifo.rec_buffe_size[0]);

        if (datalength == 0) {  // only once data
            g_stComFifo.rec_count--;
        } else if (datalength == -1) {  // no data
            g_stComFifo.rec_count--;
            return;
        } else {  // remanent data
            g_stComFifo.rec_buffe_size[0] = datalength;
        }

        totalNum = g_stComFifo.FRAME.num * 2 + 6;

        if (totalNum > Com_BUFF_SIZE) {
            return;
        }

        for (n = 0; n < totalNum; n++) {
            if (n != 3) {
                check ^= g_stComFifo.buffer[n];
            }
        }
        if (check == g_stComFifo.FRAME.checkXor) {
            // g_ptrSysParam.is_unlock = true;
            switch (g_stComFifo.FRAME.id) {
            case 0xA0: /*���Ƿ� lidar ģʽ�л�*/

                break;

            case 0xA1: /*tof lidar ģʽ�л�*/
                cmdBit = g_stComFifo.FRAME.cmd;
#if (defined A0_D4_SAMSUNG) || (defined A2_D4_SAMSUNG) || (defined A0_D4_SK) || (defined A2_D4_SK) || (defined A0_D4_SAMSUNG_C) || (defined A2_D4_SAMSUNG_C)

                if ((cmdBit & kHWS) == kHWS) { /*д״̬*/
#else
                if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) { /*д״̬*/
#endif
                    g_ptrSysParam.is_unlock = false;
                    mode_change_buff[2]     = 0xA1;
                    mode_change_buff[3]     = g_stComFifo.FRAME.cmd;
                    if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofScanMode) { /**/
                        mode_change_buff[0] = (uint8_t)kTofScanMode;
                        mode_change_buff[1] = kTofScanMode >> 8;
                        fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                        // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofScanMode);
                    } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofFaculaMode) { /**/
                        mode_change_buff[0] = (uint8_t)kTofFaculaMode;
                        mode_change_buff[1] = kTofFaculaMode >> 8;

                        fmcStaus = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                        // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofFaculaMode);
                    } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofHistMode) { /**/
                        mode_change_buff[0] = (uint8_t)kTofHistMode;
                        mode_change_buff[1] = kTofHistMode >> 8;
                        fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                        // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofHistMode);
                    } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofStopMode) { /**/
                        mode_change_buff[0] = (uint8_t)kTofStopMode;
                        mode_change_buff[1] = kTofStopMode >> 8;
                        fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                        // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofStopMode);
                    } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofCalibrationMode) { /**/
                        mode_change_buff[0] = (uint8_t)kTofCalibrationMode;
                        mode_change_buff[1] = kTofCalibrationMode >> 8;
                        fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                        // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofCalibrationMode);
                    } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofSinglePointMode) { /**/
                        mode_change_buff[0] = (uint8_t)kTofSinglePointMode;
                        mode_change_buff[1] = kTofSinglePointMode >> 8;
                        fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                        // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofSinglePointMode);
                    } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofRangingMode) { /**/
                        mode_change_buff[0] = (uint8_t)kTofRangingMode;
                        mode_change_buff[1] = kTofRangingMode >> 8;
                        fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                        // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofSinglePointMode);
                    } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofRevCheckMode) { /**/
                        mode_change_buff[0] = (uint8_t)kTofRevCheckMode;
                        mode_change_buff[1] = kTofRevCheckMode >> 8;
                        fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                        // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofSinglePointMode);
                    }

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;

            case 0xA2:                                        /*��ȡϵͳ����*/
                if ((g_stComFifo.FRAME.cmd & kHWS) == kHWS) { /*д״̬*/

                } else if ((g_stComFifo.FRAME.cmd & kHWS) == kHRS) { /*��״̬*/
                }
                break;

            case 0xA3: /*mcu id*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHRS) == kHRS) { /*��״̬*/
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;

            case 0xA4: /*version baud*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS) { /*д״̬*/

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;

            case 0xA5: /*��෶Χ*/
                       //						cmdBit = g_stComFifo.FRAME.cmd;
                       //						if((cmdBit&kHWS) == kHWS) { /*д״̬*/
                       //						//	fmcStaus = g_ptrFlash.WriteFlashSomeWord(RANGE_ADDR,g_stComFifo.FRAME.data,1);
                       //									g_stParseAck.ackTimes++;
                       //									g_stParseAck.ackType = fmcStaus == FLASH_EOP?kWriteOk:kWriteError;
                       //									g_stParseAck.ackId = g_stComFifo.FRAME.id;
                       //							//}
                       //						}
                       //						else if((cmdBit&kHRS) == kHRS) {/*��״̬*/

                //							g_stParseAck.ackTimes++;
                //							g_stParseAck.ackType = kReadOk;
                //							g_stParseAck.ackId = g_stComFifo.FRAME.id;
                //
                //						}
                break;

            case 0xA6: /*��������*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) { /*д״̬*/
                    g_ptrSysParam.is_unlock = false;
                    fmcStaus                = g_ptrFlash.WriteFlashSomeWord(CALIBRATION_ADDR, g_stComFifo.FRAME.data, 256);

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;

            case 0xA7: /*�¶Ȳ���*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS) { /*д״̬*/
                    // fmcStaus = g_ptrFlash.WriteFlashSomeWord(TG_COEFFICIENT,g_stComFifo.FRAME.data,3);

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;

            case 0xA8: /*���ʲ���*/
                       //						cmdBit = g_stComFifo.FRAME.cmd;
                       //						if((cmdBit&kHWS) == kHWS) { /*д״̬*/
                       //							//fmcStaus = g_ptrFlash.WriteFlashSomeWord(MATERIAL_COEFFICIENT,g_stComFifo.FRAME.data,12);
                       //
                       //							g_stParseAck.ackTimes++;
                       //							g_stParseAck.ackType = fmcStaus == FLASH_EOP?kWriteOk:kWriteError;
                       //							g_stParseAck.ackId = g_stComFifo.FRAME.id;
                       //
                       //						}
                       //						else if((cmdBit&kHRS) == kHRS) {/*��״̬*/
                       //							g_stParseAck.ackTimes++;
                       //							g_stParseAck.ackType = kReadOk;
                       //							g_stParseAck.ackId = g_stComFifo.FRAME.id;
                       //
                       //							//FdbMessage(kD2H|kHRS|kHSS,g_stComFifo.FRAME.id,(uint8_t
                       //*)&g_ptrSysParam.materialConfficientP3,REFLECT_BYTE_CNT,kWord);
                       //						}
                break;

            case 0xA9: /*ֱ��ͼ ��� �Ŀ���Ƶ��*/
                       //						cmdBit = g_stComFifo.FRAME.cmd;
                       //						if((cmdBit&kHWS) == kHWS) { /*д״̬*/
                       //							//fmcStaus = g_ptrFlash.WriteFlashSomeWord(HISTG_FACULA_ADDR,g_stComFifo.FRAME.data,2);
                       //
                       //							g_stParseAck.ackTimes++;
                       //							g_stParseAck.ackType = fmcStaus == FLASH_EOP?kWriteOk:kWriteError;
                       //							g_stParseAck.ackId = g_stComFifo.FRAME.id;
                       //
                       //						}
                       //						else if((cmdBit&kHRS) == kHRS) {/*��״̬*/
                       //
                       //							g_stParseAck.ackTimes++;
                       //							g_stParseAck.ackType = kReadOk;
                       //							g_stParseAck.ackId = g_stComFifo.FRAME.id;
                       //
                       //						}
                break;

            case 0xAA: /*�¶�*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS) { /*д״̬*/

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;

            case 0xAB: /*�Ĵ���*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) { /*д״̬*/
                    g_ptrSysParam.is_unlock = false;
                    /*д�Ĵ���*/
                    g_regVi4302.isWriteReg = true;
                    g_regVi4302.buff[0]    = g_stComFifo.FRAME.data[0];
                    g_regVi4302.buff[1]    = g_stComFifo.FRAME.data[1];
                    g_regVi4302.buff[2]    = g_stComFifo.FRAME.data[2];

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kWriteOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/
                    /*���Ĵ���*/

                    g_regVi4302.isReadReg = true;
                    g_regVi4302.buff[1]   = g_stComFifo.FRAME.data[0];
                    g_regVi4302.buff[2]   = g_stComFifo.FRAME.data[1];
                }
                break;
            case 0xAC: /*��Ƚǲ���*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock) { /*д״̬*/
                    g_ptrSysParam.is_unlock = false;
                    fmcStaus                = g_ptrFlash.WriteFlashSomeWord(ANGLE_ADDR, g_stComFifo.FRAME.data, 1);


                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;
            case 0xAD: /*LASER ����*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS) { /*д״̬*/
                    if (g_stComFifo.FRAME.data[0] == 0x01) {
                        VLD_ENABLE
                    } else {
                        VLD_DISABLE
                    }

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kWriteOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }

                break;
            case 0xAE: /*LED ����*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS) { /*д״̬*/
                    if (g_stComFifo.FRAME.data[0] == 0x01) {
                        OPEN_LED1
                        g_ptrSysParam.close_led = false;
                    } else {
                        CLOSE_LED1
                        g_ptrSysParam.close_led = true;
                    }
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kWriteOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }

                break;
            case 0xAF: /*VBD ����*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) { /*д״̬*/
                    g_ptrSysParam.is_unlock = false;
                    Vi4302StopRanging();
                    g_ptrSpi.wRegister(0x024F, 0xC0, NULL);
                    delay_1ms(20);

                    Vi4302ExeVbdTdc(false);
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kWriteOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }

                break;
            case 0xBC:
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS && g_ptrLidarStatus.lidarMode == kTofHistMode) {
                    Vi4302StopRanging();
                    if (g_stComFifo.FRAME.data[0] == 0x00) {
                        Vi4302StartHist(kHistNoraml);
                        g_ptrLidarStatus.hist_channel = kHistNoraml;
                    } else if (g_stComFifo.FRAME.data[0] == 0x01) {
                        Vi4302StartHist(kHistAtten);
                        g_ptrLidarStatus.hist_channel = kHistAtten;
                    } else if (g_stComFifo.FRAME.data[0] == 0x02) {
                        Vi4302StartHist(kHistRef);
                        g_ptrLidarStatus.hist_channel = kHistRef;
                    }
                }
                break;
            case 0xBD: /*BOOTLOADER*/
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) { /*д״̬*/
                    g_ptrSysParam.is_unlock = false;
                    mode_change_buff[0]     = 'l';
                    mode_change_buff[1]     = 'o';
                    mode_change_buff[2]     = 'a';
                    mode_change_buff[3]     = 'd';
                    fmcStaus                = g_ptrFlash.WriteFlashSomeWord(BOOT_ADD, mode_change_buff, 1);

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kWriteOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }

                break;
            case 0xBF: /*����ʹ�� */
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS) {                /*д״̬*/
                    if (g_stComFifo.FRAME.data[0] == 0x01) {  // g_ptrSysParam.isDebugVbd
                        g_ptrSysParam.isDebugHistogram = true;
                    } else if (g_stComFifo.FRAME.data[0] == 0x02) {
                        g_ptrSysParam.isDebugVbd = true;  // ��vbd �¶ȵ���
                    } else if (g_stComFifo.FRAME.data[0] == 0x03) {
                        g_ptrSysParam.isDebugVbd = false;  // �ر�vbd �¶ȵ���
                    } else {
                        g_ptrSysParam.isDebugHistogram = false;
                    }

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kWriteOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                    // FdbMessage(kD2H|kHWS|kHSS,g_stComFifo.FRAME.id,NULL,0,kOk);
                } else {
                }

                break;
            case 0xBE: /*����ʹ�� */
                cmdBit = g_stComFifo.FRAME.cmd;
                if ((cmdBit & kHWS) == kHWS) { /*д״̬*/
                    g_ptrSysParam.is_unlock = true;
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kWriteOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }

                break;
            case 0xD0: /*ranging mode fps ����*/
                cmdBit = g_stComFifo.FRAME.cmd;

                if ((cmdBit & kHWS) == kHWS) { /*д״̬*/  //&& g_ptrSysParam.is_unlock == true
                    g_ptrSysParam.is_unlock = false;

                    fps = g_stComFifo.FRAME.data[0] | (g_stComFifo.FRAME.data[1] << 8);
                    if (fps > 4000) {
                        fps = 4000;
                    } else if (fps < 1) {
                        fps = 1;
                    }


                    Vi4302StopRanging();
                    FrameSetting(fps, &fps_4302);
                    Vi4302StartRanging();

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kWriteOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) { /*��״̬*/

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }

                break;
            case 0xD1:
                cmdBit = g_stComFifo.FRAME.cmd;

                if ((cmdBit & kHWS) == kHWS && g_ptrLidarStatus.lidarMode == kTofHistMode) {
                    param_combine_t param_combine;
                    g_ptrFlash.ReadFlashData((uint32_t)XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t));


                    if (xtalk_calc(255, &param_combine.xtalk.xtalk_bin, &param_combine.xtalk.xtalk_peak)) {
                        if (param_combine.xtalk.xtalk_calc_cnt == 0xFFFF) {
                            param_combine.xtalk.xtalk_calc_cnt = 0;
                        }

                        // param_combine.xtalk.xtalk_peak *= 2;
                        param_combine.xtalk.xtalk_calc_cnt++;

                        fmcStaus              = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                        g_param_combine.xtalk = param_combine.xtalk;
                        g_stParseAck.ackTimes++;
                        g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
                        g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                        Vi4302StartHist(kHistNoraml);
                    }


                } else if ((cmdBit & kHRS) == kHRS) {
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }


                break;
#if (defined A0_T5_XX)
            case kBtMotorCtrl:
                cmdBit = g_stComFifo.FRAME.cmd;

                if ((cmdBit & kHWS) == kHWS) {
                    param_combine_t param_combine;
                    g_ptrFlash.ReadFlashData((uint32_t)XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t));

                    if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == kRunning) {
                        if (param_combine.motor_mode.mode != kRunning) {
                            param_combine.motor_mode.mode = kRunning;
                            fmcStaus = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                        }
                        g_ptrDDSInfoCount.encoder_cnt   = getAbsolutionTime();
                        g_param_combine.motor_mode.mode = kRunning;
                        fmcStaus                        = FLASH_EOP;

                    } else if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == kStandby) {
                        if (param_combine.motor_mode.mode != kStandby) {
                            param_combine.motor_mode.mode = kStandby;
                            fmcStaus = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                        }
                        g_param_combine.motor_mode.mode = kStandby;
                        fmcStaus                        = FLASH_EOP;
                    } else if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == kTurnOn) {
                        if (g_param_combine.motor_mode.mode != kRunning) {
                            g_param_combine.motor_mode.mode = kTurnOn;
                            g_ptrDDSInfoCount.encoder_cnt   = getAbsolutionTime();
                            fmcStaus                        = FLASH_EOP;
                        }
                    } else if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == kTurnOff) {
                        if (g_param_combine.motor_mode.mode != kRunning) {
                            g_param_combine.motor_mode.mode = kTurnOff;
                            fmcStaus                        = FLASH_EOP;
                        }

                    } else if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == 0x0000 && g_stComFifo.FRAME.data[0] != 0 && g_ptrSysParam.is_unlock == true) {
                        g_ptrSysParam.is_unlock          = false;
                        g_param_combine.motor_mode.speed = *(uint16_t *)&g_stComFifo.FRAME.data[0];
                        param_combine.motor_mode.speed   = g_param_combine.motor_mode.speed;
                        pid_param_init(&g_pid_speed, g_param_combine.motor_mode.speed);
                        fmcStaus = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                    }

                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) {
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;
            case kBtMotorCode:
                cmdBit = g_stComFifo.FRAME.cmd;

                if ((cmdBit & kHWS) == kHWS) {
                    param_combine_t param_combine;

                    g_ptrFlash.ReadFlashData((uint32_t)XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t));

                    if (*(uint16_t *)&g_stComFifo.FRAME.data[0] <= 128) {
                        memcpy(&param_combine.motor_sn, g_stComFifo.FRAME.data, g_stComFifo.FRAME.num * 2);
                        fmcStaus                 = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                        g_param_combine.motor_sn = param_combine.motor_sn;
                    }


                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;

                } else if ((cmdBit & kHRS) == kHRS) {
                    g_param_combine.motor_sn.length = *(uint16_t *)&g_stComFifo.FRAME.data[0];
                    g_stParseAck.ackTimes++;
                    g_stParseAck.ackType = kReadOk;
                    g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                }
                break;
            case kDdsInfo:
                cmdBit = g_stComFifo.FRAME.cmd;

                if ((cmdBit & kHWS) == kHWS) {

                } else if ((cmdBit & kHRS) == kHRS) {
                }
                break;
#endif
            default:
                break;
            }
        }
    }
}

/**
 * @brief
 *
 */
static void ParseAck(void) {
    if (g_stParseAck.ackTimes > 0) {
        switch (g_stParseAck.ackId) {
        case kTofMode:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);  //��ֹ���еĿ������ж�
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kWriteError) {  // kD2H|kHSS �·��ɹ�����δִ�гɹ�
                FdbMessage(kD2H | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrLidarStatus.lidarMode, SYSTEM_MODE_BYTE_CNT, kResponse);
            }
            break;
        case kMcuId:
            if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrSysParam.uID, MCU_ID_BYTE_CNT, kResponse);
            }
            break;
        case kVersionBuad:
            if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, g_ptrSysParam.version, VERSION_BAUD_BYTE_CNT, kResponse);
            }
            break;
        case kMeasureRange:
            /*if(g_stParseAck.ackType == kWriteOk) {
                    FdbMessage(kD2H|kHWS|kHSS,g_stParseAck.ackId,NULL,0,kOk);
                    PROCESS_DELAY(1);
                    NVIC_SystemReset();
            }
            else if(g_stParseAck.ackType == kWriteError) {
                    FdbMessage(kD2H|kHSS,g_stParseAck.ackId,NULL,0,kOk);
            }
            else if(g_stParseAck.ackType == kReadOk) {
                    FdbMessage(kD2H|kHRS|kHSS,g_stParseAck.ackId,(uint8_t *)&g_ptrSysParam.measureRange,MEASURE_RANGE_BYTE_CNT,kResponse);
            }*/
            break;
        case kCalibrationParam:
            if (g_stParseAck.ackType == kWriteOk) {
                // OPEN_LED1
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kWriteError) {
                FdbMessage(kD2H | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                //	OPEN_LED1
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)CALIBRATION_ADDR, 256 * 4, kWord);
            }
            break;
        case kTgParam:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kWriteError) {
                FdbMessage(kD2H | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrSysParam.tgCoefficientP3, TG_BYTE_CNT, kWord);
            }
            break;
        case kReflelParam:
            /*if(g_stParseAck.ackType == kWriteOk) {
                    FdbMessage(kD2H|kHWS|kHSS,g_stParseAck.ackId,NULL,0,kOk);
                    PROCESS_DELAY(1);
                    NVIC_SystemReset();
            }
            else if(g_stParseAck.ackType == kWriteError) {
                    FdbMessage(kD2H|kHSS,g_stParseAck.ackId,NULL,0,kOk);
            }
            else if(g_stParseAck.ackType == kReadOk) {
                    //FdbMessage(kD2H|kHRS|kHSS,g_stParseAck.ackId,(uint8_t *)&g_ptrSysParam.materialConfficientP3,REFLECT_BYTE_CNT,kWord);
            }*/
            break;
        case kHisAndFacula:
            /*if(g_stParseAck.ackType == kWriteOk) {
                    FdbMessage(kD2H|kHWS|kHSS,g_stParseAck.ackId,NULL,0,kOk);
                    PROCESS_DELAY(1);
                    NVIC_SystemReset();
            }
            else if(g_stParseAck.ackType == kWriteError) {
                    FdbMessage(kD2H|kHSS,g_stParseAck.ackId,NULL,0,kOk);
            }
            else if(g_stParseAck.ackType == kReadOk) {
                    FdbMessage(kD2H|kHRS|kHSS,g_stParseAck.ackId,(uint8_t *)&g_ptrSysParam.histogramAndSpadSampleFreq,HIS_FAC_FREQ_BYTE_CNT,kResponse);
            }*/
            break;
        case kTemperature:
            if (g_stParseAck.ackType == kReadOk) {
                // g_ptrSysParam.currentTemperature = ((float)TEMPERATURE_TABLE[(g_ptrSysParam.currentTempAdc - 214)>>1])/10;
                g_ptrSysParam.currentTemperature = GettingTemperature(
                    GettingAdcValue());  //-0.0332f*GettingAdcValue() + 87.1023f;//((float)TEMPERATURE_TABLE[(GettingAdcValue() - 214)>>1])/10;
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrSysParam.currentTemperature, TEMPERATURE_BYTE_CNT, kWord);
            }
            break;
        case kRegisterSetting:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kZeroAngleSetting:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kWriteError) {
                FdbMessage(kD2H | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrSysParam.zeroAngle, ZERO_ANGLE_BYTE_CNT, kWord);
            }
            break;
        case kLaserControl:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kLedControl:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kVbd:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, &g_ptrSysParam.vbdStep, VBD_BYTE_CNT, kResponse);
            }
            break;
        case kDebugSetting:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kDebugSetting2:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kBootloader:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            }
            break;
        case kRangingMode:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kXtalkCalc:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_param_combine.xtalk.xtalk_bin, 8, kResponse);
            }
            break;
        // g_param_combine
        case kBtMotorCtrl:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_param_combine.motor_mode.mode, 2, kResponse);
            }
            break;
        case kBtMotorCode:
            if (g_stParseAck.ackType == kWriteOk) {
                FdbMessage(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                FdbMessage(kD2H | kHRS | kHSS,
                           g_stParseAck.ackId,
                           (uint8_t *)&g_param_combine.motor_sn.sn,
                           (g_param_combine.motor_sn.length > 256 ? 256 : g_param_combine.motor_sn.length),
                           kResponse);
            }
            break;
        default:
            break;
        }
        g_stParseAck.ackTimes--;
    }
}

/**
 * @brief 端口数据接收回调函数，
 *
 * @param recSize
 */
static void ComRecInterruptCallback(uint16_t rec_size, uint8_t *rec_buff) {
    g_ringBuffer_ptr->RingbuffWriteData(&s_comm_fifo.ring_buffer, rec_buff, rec_size);
    if (s_comm_fifo.rec_count < RING_BUFF_REC_MAX_TIMES) {
        s_comm_fifo.ring_buffer[s_comm_fifo.rec_count] = rec_size;
        s_comm_fifo.rec_count++;
    }
}

/**
 * @brief
 *
 */
static void ComTransmitInterruptCallback(void) {
    // g_ptrSysFlag.isComTransmitFinished = true;
    // CLOSE_LED1
}

static const API_Communication_T comm_api_ptr = {
    .CommInit = CommInit,
    // .handleRecIntrPtr = ComRecInterruptCallback,
    .CommTransmitData = UART2_SendData,
};

const API_Communication_T *const g_comm_ptr = &comm_api_ptr;