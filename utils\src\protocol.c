#include "protocol.h"


void FdbMessage(uint8_t cmd, uint8_t id, uint8_t *transmitBuff, uint16_t len, uint8_t fdbType) {
    cache[0] = 0xA5;
    cache[1] = cmd;
    cache[2] = id;

    uint16_t m = 0;
    if (fdbType == kOk) {
        m = 0;
    } else {
        m = len >> 1;
    }

    cache[4] = m;
    cache[5] = m >> 8;
    cache[3] = cache[0] ^ cache[1] ^ cache[2] ^ cache[4] ^ cache[5];

    if (fdbType == kWord) {
        for (uint16_t n = 0; n < len; n = n + 4) {
            cache[6 + n]     = transmitBuff[n];
            cache[6 + n + 1] = transmitBuff[n + 1];
            cache[6 + n + 2] = transmitBuff[n + 2];
            cache[6 + n + 3] = transmitBuff[n + 3];
            cache[3] ^= cache[6 + n] ^ cache[6 + n + 1] ^ cache[6 + n + 2] ^ cache[6 + n + 3];
        }
    } else if (fdbType == kResponse) {
        for (uint16_t n = 0; n < len; n = n + 2) {
            cache[6 + n]     = transmitBuff[n];
            cache[6 + n + 1] = transmitBuff[n + 1];
            cache[3] ^= cache[6 + n] ^ cache[6 + n + 1];
        }
    }

    g_ptrCom.ComTransmitDmaData(cache, COM_PROTOCOL_FIX_LENGTH + len);
    WaitTransmit();
}


static U_FRAME s_transmit_buff;

static uint8_t calXOR(uint8_t const *data, uint16_t len, uint8_t xor_index) {
    uint8_t xor_tmp = 0, i = 0;

    for (i = 0; i < len; ++i) {
        if (i != xor_index) {
            xor_tmp ^= *(data + i);
        }
    }
    return xor_tmp;
}

void TransmitInfoInit() {
    uint8_t i = 0;

    //* receive
    //  s_receive_buff.FRAME.header = eHEADER;

    //* transmit
    s_transmit_buff.FRAME.header = eACK_HEADER;

    //*
    g_protocol_ack_flag.ack_num = 0;
    for (i = 0; i < CMD_ACK_CACHE_NUM; i++) {
        g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id     = 0;
        g_protocol_ack_flag.st_cmd_ack_flag[i].ack_status = false;
        g_protocol_ack_flag.st_cmd_ack_flag[i].ack_timers = 1;
        g_protocol_ack_flag.st_cmd_ack_flag[i].send_len   = 1;
    }
}

void UartAckTest() {
    uart1_send_data(s_transmit_buff.buffer, 6);
}

/**
 *@brief: comm data send
 *
 */
static void ProtocolInteractorAckMessage(uint8_t cmd, uint8_t id, uint8_t *send_data, uint8_t length) {
    uint8_t transmit_buff_length = 0;

    s_transmit_buff.FRAME.cmd = (cmd & (uint8_t)eR) | (uint8_t)eH2D;
    s_transmit_buff.FRAME.id  = id;
    s_transmit_buff.FRAME.num = length;

    memcpy(s_transmit_buff.FRAME.data_buff, send_data, length);

    transmit_buff_length           = eHEADER_LEN + s_transmit_buff.FRAME.num;
    s_transmit_buff.FRAME.checkXor = calXOR((uint8_t const *)s_transmit_buff.buffer, transmit_buff_length, (uint8_t)eXOR_INDEX);

    uart1_send_data(s_transmit_buff.buffer, transmit_buff_length);
}

EFunctionStatus ProtocolParse(U_FRAME const *const data_receive_, uint8_t *const task_index_) {
    uint8_t i = 0;

    if (data_receive_->FRAME.header == (uint16_t)eHEADER) {  //帧头

        //* check sum cal
        uint8_t xor_cal = 0;
        //        uint8_t cache[3] = {0};

        uint8_t cmd_length = (uint8_t)eHEADER_LEN + data_receive_->FRAME.num;
        xor_cal            = calXOR((uint8_t *)data_receive_->buffer, cmd_length, (uint8_t)eXOR_INDEX);

        if (xor_cal == data_receive_->FRAME.checkXor) {
            if (g_protocol_ack_flag.ack_num < CMD_ACK_CACHE_NUM) {
                for (i = 0; i < CMD_ACK_CACHE_NUM; i++) {
                    if (!g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id) {  // id不能是0
                        g_protocol_ack_flag.st_cmd_ack_flag[i].ack_cmd = data_receive_->FRAME.cmd;
                        g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id  = data_receive_->FRAME.id;  // id cant be 0x00
                        *task_index_                                   = i;
                        break;
                    }
                }
            } else
                return FUNC_BUSY;

            //        *id_ = data_receive_->FRAME.id;
            //        data_buf_ = data_receive_->FRAME.data_buff;

            g_protocol_ack_flag.ack_num++;
            return FUNC_COMPLETED;
        }
        return FUNC_ERROR;
    }
    return FUNC_ERROR;
}


void ProtocolCmdAck() {
    uint8_t ack_try_timers = 10, i = 0;
    if (g_protocol_ack_flag.ack_num > 0) {
        for (i = 0; i < CMD_ACK_CACHE_NUM; i++) {
            if (g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id != 0 && g_protocol_ack_flag.st_cmd_ack_flag[i].ack_status) {

                do {
                    if (g_comm_flag.StFlag.is_uart0_sended) {
                        interactor_ack_message(g_protocol_ack_flag.st_cmd_ack_flag[i].ack_cmd,
                                               g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id,
                                               g_protocol_ack_flag.st_cmd_ack_flag[i].send_buff,
                                               g_protocol_ack_flag.st_cmd_ack_flag[i].send_len);
                        memset(g_protocol_ack_flag.st_cmd_ack_flag[i].send_buff, 0, sizeof(g_protocol_ack_flag.st_cmd_ack_flag[i].send_buff));

                        g_protocol_ack_flag.ack_num--;
                        break;
                    }
                } while (ack_try_timers--);

                g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id = 0;
            }
        }
    }
}
