>>> cc

"../utils/inc/protocol.h", line 209: Error:  #20: identifier "U_FRAME" is undefined
  EFunctionStatus parse_protocol(U_FRAME const *const data_receive_, uint8_t *const task_index_);
                                 ^
".\..\utils\src\protocol.c", line 5: Error:  #20: identifier "cache" is undefined
      cache[0] = 0xA5;
      ^
".\..\utils\src\protocol.c", line 10: Error:  #20: identifier "kOk" is undefined
      if (fdbType == kOk) {
                     ^
".\..\utils\src\protocol.c", line 20: Error:  #20: identifier "kWord" is undefined
      if (fdbType == kWord) {
                     ^
".\..\utils\src\protocol.c", line 28: Error:  #20: identifier "kResponse" is undefined
      } else if (fdbType == kResponse) {
                            ^
".\..\utils\src\protocol.c", line 36: Error:  #20: identifier "g_ptrCom" is undefined
      g_ptrCom.ComTransmitDmaData(cache, COM_PROTOCOL_FIX_LENGTH + len);
      ^
".\..\utils\src\protocol.c", line 36: Error:  #20: identifier "COM_PROTOCOL_FIX_LENGTH" is undefined
      g_ptrCom.ComTransmitDmaData(cache, COM_PROTOCOL_FIX_LENGTH + len);
                                         ^
".\..\utils\src\protocol.c", line 37: Warning:  #223-D: function "WaitTransmit" declared implicitly
      WaitTransmit();
      ^
".\..\utils\src\protocol.c", line 41: Error:  #20: identifier "U_FRAME" is undefined
  static U_FRAME s_transmit_buff;
         ^
".\..\utils\src\protocol.c", line 64: Error:  #20: identifier "g_protocol_ack_flag" is undefined
      g_protocol_ack_flag.ack_num = 0;
      ^
".\..\utils\src\protocol.c", line 65: Error:  #20: identifier "CMD_ACK_CACHE_NUM" is undefined
      for (i = 0; i < CMD_ACK_CACHE_NUM; i++) {
                      ^
".\..\utils\src\protocol.c", line 67: Error:  #20: identifier "false" is undefined
          g_protocol_ack_flag.st_cmd_ack_flag[i].ack_status = false;
                                                              ^
".\..\utils\src\protocol.c", line 74: Warning:  #223-D: function "uart1_send_data" declared implicitly
      uart1_send_data(s_transmit_buff.buffer, 6);
      ^
".\..\utils\src\protocol.c", line 88: Warning:  #223-D: function "memcpy" declared implicitly
      memcpy(s_transmit_buff.FRAME.data_buff, send_data, length);
      ^
".\..\utils\src\protocol.c", line 93: Warning:  #223-D: function "uart1_send_data" declared implicitly
      uart1_send_data(s_transmit_buff.buffer, transmit_buff_length);
      ^
".\..\utils\src\protocol.c", line 96: Error:  #20: identifier "U_FRAME" is undefined
  EFunctionStatus ProtocolParse(U_FRAME const *const data_receive_, uint8_t *const task_index_) {
                                ^
".\..\utils\src\protocol.c", line 109: Error:  #20: identifier "g_protocol_ack_flag" is undefined
              if (g_protocol_ack_flag.ack_num < CMD_ACK_CACHE_NUM) {
                  ^
".\..\utils\src\protocol.c", line 109: Error:  #20: identifier "CMD_ACK_CACHE_NUM" is undefined
              if (g_protocol_ack_flag.ack_num < CMD_ACK_CACHE_NUM) {
                                                ^
".\..\utils\src\protocol.c", line 124: Error:  #20: identifier "g_protocol_ack_flag" is undefined
              g_protocol_ack_flag.ack_num++;
              ^
".\..\utils\src\protocol.c", line 135: Error:  #20: identifier "g_protocol_ack_flag" is undefined
      if (g_protocol_ack_flag.ack_num > 0) {
          ^
".\..\utils\src\protocol.c", line 136: Error:  #20: identifier "CMD_ACK_CACHE_NUM" is undefined
          for (i = 0; i < CMD_ACK_CACHE_NUM; i++) {
                          ^
".\..\utils\src\protocol.c", line 140: Error:  #20: identifier "g_comm_flag" is undefined
                      if (g_comm_flag.StFlag.is_uart0_sended) {
                          ^
".\..\utils\src\protocol.c", line 141: Warning:  #223-D: function "interactor_ack_message" declared implicitly
                          interactor_ack_message(g_protocol_ack_flag.st_cmd_ack_flag[i].ack_cmd,
                          ^
".\..\utils\src\protocol.c", line 145: Warning:  #223-D: function "memset" declared implicitly
                          memset(g_protocol_ack_flag.st_cmd_ack_flag[i].send_buff, 0, sizeof(g_protocol_ack_flag.st_cmd_ack_flag[i].send_buff));
                          ^
".\..\utils\src\protocol.c", line 81: Warning:  #177-D: function "ProtocolInteractorAckMessage"  was declared but never referenced
  static void ProtocolInteractorAckMessage(uint8_t cmd, uint8_t id, uint8_t *send_data, uint8_t length) {
              ^
.\..\utils\src\protocol.c: 7 warnings, 18 errors
".\..\utils\src\ring_buff.c", line 135: Error:  #65: expected a ";"
  const API_Ringbuff_T *const g_ringBuffer_ptr = &ringBuffer_api_ptr;
  ^
".\..\utils\src\ring_buff.c", line 130: Warning:  #177-D: variable "ringBuffer_api_ptr"  was declared but never referenced
  static const API_Ringbuff_T ringBuffer_api_ptr = {
                              ^
.\..\utils\src\ring_buff.c: 1 warning, 1 error
".\..\bsp\src\bsp_freq.c", line 15: Warning:  #223-D: function "MX_TIM3_Init" declared implicitly
      MX_TIM3_Init(count);
      ^
".\..\bsp\src\bsp_freq.c", line 50: Warning:  #223-D: function "motor_pwm_init" declared implicitly
      motor_pwm_init();
      ^
.\..\bsp\src\bsp_freq.c: 2 warnings, 0 errors

>>> ld

