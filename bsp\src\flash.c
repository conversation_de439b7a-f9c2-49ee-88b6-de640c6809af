/*包含私有头文件 */
#include "WDT.h"
#include "_flash.h"
#include "cmd_handle.h"
#include "delay.h"



#define DEFAULT_FLASH_TEMP_CAL_P1      510
#define DEFAULT_FLASH_TEMP_CAL_P2      143

#define DEFAULT_FLASH_FENDUAN_NUMBER   23  //分段点数

// 3#
#define DEFAULT_FLASH_GAOFAN_QUANJU_EN 0

const int16_t VI4302_NORMAL_QUANJU_PARA[5] = {-25, -50, 1634, -147, 4817};

const int16_t VI4302_ATT_QUANJU_PARA[4] = {0, -2, 17, 12835};

const int16_t VI4302_NORMAL_PARA[DEFAULT_FLASH_FENDUAN_NUMBER][5] = {
    -8,   14,   88,  252,  4771, -17,  22,   108, 565,  4817, -24,  28,   133, 2116, 4802, -26,  30,   159, 2047, 4716, -11,  19,   195,
    1726, 4670, -9,  18,   212,  1575, 4576, -4,  17,   264,  450,  4204, -4,  19,   316,  441,  3701, -7,  16,   363,  128,  3323, -12,
    18,   413,  392, 3087, -14,  17,   462,  365, 2917, -12,  13,   509,  361, 2770, 4,    -7,   644,  180, 2263, 11,   -18,  785,  360,
    1968, -23,  -13, 982,  60,   1638, -170, 12,  1985, 250,  1212, -229, 18,  2991, 57,   930,  -258, 20,  3996, 79,   771,  -270, 18,
    4998, -147, 626, -288, 19,   6001, 34,   528, -281, 17,   7003, 19,   465, -251, 14,   8003, 13,   377, -254, 14,   8003, 13,   377,
};

const int32_t VI4302_ATT_PARA[DEFAULT_FLASH_FENDUAN_NUMBER][8] = {
    3,    0,  -131, 13, 1594, 77,   246, 65535, 1,    0,  -126, 14, 1704, 97,   320, 65535, -1,   0, -97,  11, 1761, 123,  355, 65535,
    -5,   2,  -94,  11, 1703, 148,  333, 65535, 12,   0,  -134, 14, 1567, 182,  278, 65535, 26,   0, -123, 13, 1488, 197,  243, 65535,
    -8,   1,  -124, 9,  1183, 245,  169, 65535, 2,    1,  -131, 8,  926,  294,  102, 65535, 16,   0, -143, 9,  885,  344,  91,  65535,
    -15,  2,  -146, 10, 1005, 394,  168, 65535, -29,  2,  -135, 9,  1009, 444,  219, 65535, -22,  2, -134, 9,  1001, 495,  241, 65535,
    28,   -1, -79,  5,  936,  647,  243, 65535, 36,   -1, -3,   1,  818,  800,  188, 65535, 12,   0, 60,   -2, 611,  1004, 112, 65535,
    -28,  0,  139,  -3, 231,  2008, 17,  65535, -111, 1,  95,   -1, 163,  3005, 8,   65535, -262, 2, 67,   -1, 137,  4004, 7,   65535,
    -178, 1,  49,   0,  111,  5003, 3,   65535, -388, 2,  26,   -1, 88,   6002, 3,   65535, -456, 2, -18,  0,  72,   7000, 3,   65535,
    -225, 0,  -58,  0,  59,   7998, 2,   65535, -294, 1,  -73,  0,  59,   7997, 2,   65535,

};

/*********************************************************
函数名:uint16_t STMFLASH_ReadHalfWord(uint32_t faddr)
说明  :stm32内部读指定地址的字节(8位数据)
参数  :
faddr:读地址
返回值:
对应数据.
*********************************************************/
uint8_t STMFLASH_ReadByte(uint32_t faddr) {
    return *(__IO uint8_t *)faddr;
}

uint16_t STMFLASH_ReadHalfWord(uint32_t faddr) {
    return *(uint16_t *)faddr;
}

uint32_t STMFLASH_ReadWord(uint32_t faddr) {
    return *(uint32_t *)faddr;
}

/*公开的子程序*/
/***************************************************************************
 * 函数介绍：
 * 输入参数：初始化，更新状态寄存器
 * 输出参数：
 * 返回值  ：
 ****************************************************************************/
void flash_SnDataInit(void) {
    uint8_t i;

    for (i = 0; i < MAX_FLASH_WRITE_TIMES; i++)  // flash参数读出来，看看标志是否需要初始化，为了预防一次读错，多读了几次
    {
        flash_ReadSnData();
        if (g_snNeedSaved.flag != FLASH_SN_INIT_FLAG) {
            delay_ms(100);
        } else {
            return;
        }
    }

    //各参数初始化
    g_snNeedSaved.flag              = FLASH_SN_INIT_FLAG;
    g_snNeedSaved.updateProgramFlag = UPDATE_UPDAT_SUCCESS_FLAG;

    flash_SaveSnData();
}

void flash_DataInit(void) {
    uint8_t i;

    for (i = 0; i < MAX_FLASH_WRITE_TIMES; i++)  // flash参数读出来，看看标志是否需要初始化，为了预防一次读错，多读了几次
    {
        flash_ReadConfigerData();
        if (g_dataNeedSaved.flag != FLASH_INIT_FLAG) {
            delay_ms(100);
        } else {
            return;
        }
    }

    //各参数初始化
    g_dataNeedSaved.flag              = FLASH_INIT_FLAG;
    g_dataNeedSaved.service_work_mode = DEFAULT_SERVICE_WORK_MODE;
    g_dataNeedSaved.point0_offset     = DEFAULT_POINT0_OFFSET;

    flash_SaveConfigerData();
}

void flash_FmDataInit(void) {
    uint8_t i, j, z;

    //	for (i=0; i < MAX_FLASH_WRITE_TIMES; i++)		//flash参数读出来，看看标志是否需要初始化，为了预防一次读错，多读了几次
    //	{
    //		flash_ReadFmData();
    //		if(g_fmNeedSaved.flag != DEFAULT_FLASH_FM_INIT_FLAG)
    //		{
    //			delay_ms(100);
    //		}
    //		else
    //		{
    //			return;
    //		}
    //	}

    //各参数初始化
    g_fmNeedSaved.flag             = DEFAULT_FLASH_FM_INIT_FLAG;
    g_fmNeedSaved.vi4302_bvd_save  = DEFAULT_FLASH_FM_VI4302_BVD_SAVE;
    g_fmNeedSaved.vi4302_temp_save = DEFAULT_FLASH_FM_VI4302_TEMP_SAVE;
    g_fmNeedSaved.vi4302_tdc_save  = DEFAULT_FLASH_FM_VI4302_TDC_SAVE;
    g_fmNeedSaved.vi4302_para_save = DEFAULT_FLASH_FM_VI4302_PARA_SAVE;

    g_fmNeedSaved.wendu_cal_para[0] = DEFAULT_FLASH_TEMP_CAL_P1;
    g_fmNeedSaved.wendu_cal_para[1] = DEFAULT_FLASH_TEMP_CAL_P2;

    g_fmNeedSaved.gaofan_quanju_en      = DEFAULT_FLASH_GAOFAN_QUANJU_EN;
    g_fmNeedSaved.heibai_fanduan_number = DEFAULT_FLASH_FENDUAN_NUMBER;

    for (z = 0; z < 5; z++) {
        g_fmNeedSaved.heibai_quanju_para[z] = VI4302_NORMAL_QUANJU_PARA[z];
    }
    for (z = 0; z < 4; z++) {
        g_fmNeedSaved.gaofan_quanju_para[z] = VI4302_ATT_QUANJU_PARA[z];
    }

    for (j = 0; j < DEFAULT_FLASH_FENDUAN_NUMBER; j++) {
        for (z = 0; z < 5; z++) {
            g_fmNeedSaved.heibai_fenduan_para[j][z] = VI4302_NORMAL_PARA[j][z];
        }
        for (z = 0; z < 8; z++) {
            g_fmNeedSaved.gaofan_fenduan_para[j][z] = VI4302_ATT_PARA[j][z];
        }
    }

    flash_SaveFmData();
}
//从指定地址开始读出指定长度的数据
// ReadAddr:起始地址
// pBuffer:数据指针
// NumToWrite:字节(8位)数
void STMFLASH_Read(uint32_t ReadAddr, uint8_t *pBuffer, uint16_t NumToRead) {
    uint16_t i;

    for (i = 0; i < NumToRead; i++) {
        pBuffer[i] = STMFLASH_ReadByte(ReadAddr);  //读取2个字节.
        ReadAddr++;                                //偏移1个字节.
    }
}


// uint64_t g_FlashData = 0;

//从指定地址开始写入指定长度的数据
// WriteAddr:起始地址(此地址必须为2的倍数!!)
// pBuffer:数据指针
// NumToWrite:字节(8位)数(就是要写入的8位数据的个数)。偶数个，好算
void STMFLASH_Write(uint32_t WriteAddrStart, uint8_t *pBuffer, uint16_t NumToWrite) {
    uint16_t i = 0, j = 0;
    uint32_t Address;
    uint32_t PageOfAddr;
    uint32_t WriteAddrEND;
    uint32_t STMFlash_BUFFER[FLASH_PAGE_SIZE / 4] = {0};

    if (WriteAddrStart < H_AppStart) {
        return;
    }

    FLASH_Unlock();
    WriteAddrEND = WriteAddrStart + NumToWrite;
    PageOfAddr   = ((WriteAddrStart - FLASH_BASE) / FLASH_PAGE_SIZE) * FLASH_PAGE_SIZE + FLASH_BASE;  // 要擦除的页首地址
    Address      = PageOfAddr;
    for (i = 0; i < FLASH_PAGE_SIZE / 4; i++) {
        STMFlash_BUFFER[i] = STMFLASH_ReadWord(Address + 4 * i);
    }
    j       = 0;
    i       = (WriteAddrStart - PageOfAddr) / 4;
    Address = WriteAddrStart;
    while (Address < WriteAddrEND)  //写入需要写入的字节
    {
        STMFlash_BUFFER[i] = ((pBuffer[j + 3] & 0x000000ff) << 24) + ((pBuffer[j + 2] & 0x000000ff) << 16) + ((pBuffer[j + 1] & 0x000000ff) << 8) + pBuffer[j];
        j += 4;
        i++;
        Address += 4;
    }
    FLASH_One_Page_Erase(PageOfAddr);
    Address = PageOfAddr;
    for (i = 0; i < FLASH_PAGE_SIZE / 4; i++) {
        FLASH_Word_Program(Address, STMFlash_BUFFER[i]);
        Address = Address + 4;
        WDT_Week();
    }

    FLASH_Lock();  //上锁
    //	__enable_irq();
}


/***************************************************************************
 * 函数介绍：
 * 输入参数：读数据
 * 输出参数：
 * 返回值  ：
 ****************************************************************************/
void flash_ReadConfigerData(void) {
    STMFLASH_Read(FLASH_START_ADDR, (uint8_t *)(&g_dataNeedSaved), sizeof(g_dataNeedSaved));
}

/***************************************************************************
 * 函数介绍：
 * 输入参数：写数据
 * 输出参数：
 * 返回值  ：
 ****************************************************************************/
void flash_SaveConfigerData(void) {
    STMFLASH_Write(FLASH_START_ADDR, (uint8_t *)(&g_dataNeedSaved), sizeof(g_dataNeedSaved));
    flash_ReadConfigerData();
}

/***************************************************************************
 * 函数介绍：
 * 输入参数：读数据
 * 输出参数：
 * 返回值  ：
 ****************************************************************************/
void flash_ReadFmData(void) {
    STMFLASH_Read(FLASH_FM_START_ADDR, (uint8_t *)(&g_fmNeedSaved), sizeof(g_fmNeedSaved));
}

/***************************************************************************
 * 函数介绍：
 * 输入参数：写数据
 * 输出参数：
 * 返回值  ：
 ****************************************************************************/
void flash_SaveFmData(void) {
    STMFLASH_Write(FLASH_FM_START_ADDR, (uint8_t *)(&g_fmNeedSaved), sizeof(g_fmNeedSaved));
    flash_ReadFmData();
}

/***************************************************************************
 * 函数介绍：
 * 输入参数：读数据
 * 输出参数：
 * 返回值  ：
 ****************************************************************************/
void flash_ReadSnData(void) {
    STMFLASH_Read(FLASH_SN_START_ADDR, (uint8_t *)(&g_snNeedSaved), sizeof(g_snNeedSaved));
}

/***************************************************************************
 * 函数介绍：
 * 输入参数：写数据
 * 输出参数：
 * 返回值  ：
 ****************************************************************************/
void flash_SaveSnData(void) {
    STMFLASH_Write(FLASH_SN_START_ADDR, (uint8_t *)(&g_snNeedSaved), sizeof(g_snNeedSaved));
    flash_ReadSnData();
}
