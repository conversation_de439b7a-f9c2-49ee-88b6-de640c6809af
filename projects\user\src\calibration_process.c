#include "calibration_process.h"

//*****************************bsp */
#include "bsp_adc.h"
#include "bsp_io.h"
#include "bsp_vi4302.h"
#include "facula_process.h"
#include "n32g430_it.h"
#include "pid_ctrl.h"
#include "scan_process.h"
#include "usart.h"

//*****************************app */


//****************************function */
static void DataCalibration(void);

/**
 * @brief
 *
 */
void CalibrationProcess(void) {

    uint16_t fps_4302 = 0;
    Vi4302RangingConfig();
    // Vi4302FpsConfig(FPS_DELAY_TIME);
    FrameSetting(4000, &fps_4302);
    Vi4302StopRanging();
    Vi4302StartRanging();


#if USING_SCAN_DEBUG == 0
    while (g_regSpeed.isSpeedStabled == false || g_regSpeed.isDetectStartEncoder == false) {
        g_ptrTimeTask.TaskPoll();
        ParseAck();
    }
#endif
    DeviceInfo();
    LidarDataBegin();

#if USING_CSPC_PROTOCOL == 1
    g_regLidarData0.PH = 0x66AA;
    g_regLidarData1.PH = 0x66AA;
    g_regLidarData2.PH = 0x66AA;
    g_regLidarDataX.PH = 0x66AA;
#endif

    g_ptrSysParam.mcu_ref_vol = GettingAdcVref();
    g_ptrSysParam.mcu_ref_vol = g_ptrSysParam.mcu_ref_vol < 3.05f ? 3.05f : g_ptrSysParam.mcu_ref_vol;

    g_regSpeed.isStalled     = false;
    g_ptrSysFlag.isGpioTrig0 = false;

    for (;;) {
        g_ptrTimeTask.TaskPoll();
        DataCalibration();
        TransmitData();
    }
}


void DataCalibration(void) {

    static uint8_t    r_value[2] = {0};
    rec_vi_raw_data_t raw_data;
    float             angleValue     = 0;
    float             tn_angle       = 0;
    uint16_t          angle_encode   = 0;
    float             angle_interval = 24;
    uint16_t          angle_base     = 0;
#if (defined A0_NA4_XX) || (defined A2_NA4_XX) || (defined A0_D6_XX) || (defined A2_D6_XX) || (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
    static float tmp_ref_vol = 3.3f;
#endif

    if (g_ptrSysFlag.isGpioTrig0 == true) {

        g_ptrSysFlag.isGpioTrig0 = false;
        memset(cache, 0, RANGING_FRAME_LENGTH);
        cache[0] = SPI_4302_REG_READ;
        cache[1] = 0;
        cache[2] = 0x30;
        g_ptrSpi.SpiReadBytes(cache, RANGING_FRAME_LENGTH, (uint8_t *)&raw_data, RANGING_FRAME_LENGTH);

        /*g_sys_time_t.time_cur = get_absolution_stamp_us();
        g_sys_time_t.time_diff = g_sys_time_t.time_cur - g_sys_time_t.time_last;
        g_sys_time_t.time_last = g_sys_time_t.time_cur;*/


        g_cal_data_t.raw_tof   = 0;
        g_cal_data_t.noise1    = 0;
        g_cal_data_t.raw_peak1 = 0;

        do {
            g_cal_data_t.delta_angle = g_regEncoder.calcEdgeCountAve;
            g_cal_data_t.encoder_cnt = g_regEncoder.calcEdgeNum;
            g_cal_data_t.angle_time  = TIM_Base_Count_Get(TIM8);
        } while (g_cal_data_t.encoder_cnt != g_regEncoder.calcEdgeNum);

        g_cal_data_t.is_start_pack = g_regEncoder.startPack;


        g_ptrSysParam.adc_value = GettingAdcValue();
#if (defined A0_NA4_XX) || (defined A2_NA4_XX) || (defined A0_D6_XX) || (defined A2_D6_XX) || (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
        tmp_ref_vol                      = g_ptrSysParam.mcu_ref_vol;
        tmp_ref_vol                      = tmp_ref_vol < 3.05f ? 3.05f : tmp_ref_vol;
        g_ptrSysParam.currentTemperature = g_ptrSysParam.adc_value * (tmp_ref_vol / 3.3f);
#else
        g_ptrSysParam.currentTemperature = g_ptrSysParam.adc_value;
#endif


        g_ptrSysParam.currentTemperature    = (g_ptrSysParam.currentTemperaturePri + g_ptrSysParam.currentTemperature) / 2.0f;
        g_ptrSysParam.currentTemperaturePri = g_ptrSysParam.currentTemperature;
        /*if(g_ptrSysParam.currentTemperature < 90 && g_ptrSysParam.currentTemperature > -20 && g_ptrSysParam.isDebugVbd == true) {
                VI4302_Temp_Bvd();
        }*/


        if (g_regVi4302.isWriteReg == true) {
            g_regVi4302.isWriteReg = false;
            g_ptrSpi.wRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, g_regVi4302.buff[0], NULL);
        }
        if (g_regVi4302.isReadReg == true) {
            g_regVi4302.isReadReg = false;
            g_ptrSpi.rRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, &r_value[0], NULL);
            FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&r_value, REGISTER_BYTE_CNT, kResponse);
        }


        /*rec raw data*/
        /*g_cal_data_t.raw_tof = (raw_data.raw_tof1)/SCALE_MUTIPLES - RAW_TOF_OFFSET \
                            -(g_ptrSysParam.tgCoefficientP2*g_ptrSysParam.currentTemperature*g_ptrSysParam.currentTemperature \
                                + g_ptrSysParam.tgCoefficientP1*g_ptrSysParam.currentTemperature + g_ptrSysParam.tgCoefficientP0);*/


        g_cal_data_t.raw_tof = (raw_data.raw_tof1) / SCALE_MUTIPLES - RAW_TOF_OFFSET;


        if (g_ptrSysParam.currentTemperature >= g_ptrSysParam.tgMarkPoint) {  // raw tof lower
            g_cal_data_t.raw_tof -=
                (g_ptrSysParam.tgCoefficientP3 * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature +
                 g_ptrSysParam.tgCoefficientP2 * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature +
                 g_ptrSysParam.tgCoefficientP1 * g_ptrSysParam.currentTemperature + g_ptrSysParam.tgCoefficientP0);

        } else {  // raw tof upper
            g_cal_data_t.raw_tof -=
                (g_ptrSysParam.tgCoefficientP3_2 * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature +
                 g_ptrSysParam.tgCoefficientP2_2 * g_ptrSysParam.currentTemperature * g_ptrSysParam.currentTemperature +
                 g_ptrSysParam.tgCoefficientP1_2 * g_ptrSysParam.currentTemperature + g_ptrSysParam.tgCoefficientP0_2);
        }


#if (defined A0_NA4_XX) || A2_NA4_XX
        g_cal_data_t.raw_tof = g_cal_data_t.raw_tof - ((g_ptrSysParam.mcu_ref_vol - 3.05f) * spad_compensation_confidence->vol_compensate_raw_tof) / 15.55f;
#else
        g_cal_data_t.raw_tof             = g_cal_data_t.raw_tof;
#endif

#if A2_PROJECT
        g_cal_data_t.raw_peak1 = (raw_data.raw_peak1 << 9) / raw_data.raw_tof2;  // A2芯片 peak有设置缩放
#else
        g_cal_data_t.raw_peak1           = (raw_data.raw_peak1 << 7) / raw_data.raw_tof2;
#endif
        g_cal_data_t.raw_peak2 = (raw_data.raw_peak2 << 7) / g_ptrSysParam.integration;

        g_cal_data_t.noise1 = raw_data.raw_noise1_h << 16 | raw_data.raw_noise1;
#if A2_PROJECT
        g_cal_data_t.noise2 = raw_data.raw_noise2;  // raw_noise2_h 改为flag位
#else
        g_cal_data_t.noise2              = raw_data.raw_noise2_h << 16 | raw_data.raw_noise2;
#endif


        /*calc confidence*/
        /*
        g_cal_data_t.sigma = (MA_COEFFICIENT_SUM*((CONFIDENCE_FUNC_A)/(g_cal_data_t.noise1 + CONFIDENCE_FUNC_B) +
        ((CONFIDENCE_FUNC_C*g_cal_data_t.noise1)/65536) + CONFIDENCE_FUNC_D)/8.0f) + MA_COEFFICIENT_SUM; g_cal_data_t.confidence_up =
        (MA_COEFFICIENT_SUM*g_cal_data_t.noise1)/SCALE_MUTIPLES + CONFIDENCE_SIGMA_UP*g_cal_data_t.sigma; g_cal_data_t.confidence_low =
        (MA_COEFFICIENT_SUM*g_cal_data_t.noise1)/SCALE_MUTIPLES	 + CONFIDENCE_SIGMA_LOW*g_cal_data_t.sigma;
        */

#ifdef USING_CONFIDENCE_CONST
        g_cal_data_t.sigma          = (6557 * g_cal_data_t.noise2 - 3.159e-09f) / (g_cal_data_t.noise2 + 469.1f);
        g_cal_data_t.confidence_up  = g_cal_data_t.sigma * 8.0f;
        g_cal_data_t.confidence_low = g_cal_data_t.sigma * 5.5f;

        if (g_cal_data_t.raw_peak1 > 500) {

            if (g_cal_data_t.raw_peak1 > g_cal_data_t.confidence_up) {
                g_cal_data_t.confidence = 100;
            } else if (g_cal_data_t.raw_peak1 < g_cal_data_t.confidence_low) {
                g_cal_data_t.confidence = 0;
            } else {
                g_cal_data_t.confidence =
                    100 * (g_cal_data_t.raw_peak1 - g_cal_data_t.confidence_low) / (g_cal_data_t.confidence_up - g_cal_data_t.confidence_low);
            }
            if (g_cal_data_t.confidence >= CONFIDENCEN_THRESHOLD && g_cal_data_t.raw_tof > 0) {
                g_ptrRegCentroid.distance_tmp = (uint32_t)(g_cal_data_t.raw_tof * 100);


                if (g_ptrSysParam.isDebugHistogram == true) {
                    g_cal_data_t.raw_peak1 = g_cal_data_t.raw_peak2;
                }

            } else {
                g_ptrRegCentroid.distance_tmp = 0;
                g_cal_data_t.raw_peak1        = 0;
            }
        } else {
            g_cal_data_t.confidence       = 0;
            g_ptrRegCentroid.distance_tmp = 0;
            g_cal_data_t.raw_peak1        = 0;
        }

#else

        if (g_cal_data_t.noise1 > spad_compensation_confidence->rational_x_limit) {
            g_cal_data_t.noise1 = spad_compensation_confidence->rational_x_limit;
        }

        if (g_cal_data_t.noise1 < (spad_compensation_confidence->rational_x_limit / 4) && g_cal_data_t.raw_peak1 < 600) {
            g_cal_data_t.confidence       = 0;
            g_ptrRegCentroid.distance_tmp = 0;
            g_cal_data_t.raw_peak1        = 0;
        } else {
            g_cal_data_t.sigma = (spad_compensation_confidence->rational_a * g_cal_data_t.noise1 + spad_compensation_confidence->rational_b) /
                                 (g_cal_data_t.noise1 + spad_compensation_confidence->rational_c);
            g_cal_data_t.confidence_up  = g_cal_data_t.sigma * spad_compensation_confidence->upper_factor + spad_compensation_confidence->base_peak;
            g_cal_data_t.confidence_low = g_cal_data_t.sigma * spad_compensation_confidence->low_factor + spad_compensation_confidence->base_peak;

            if (g_cal_data_t.raw_peak1 > g_cal_data_t.confidence_up) {
                g_cal_data_t.confidence = 100;
            } else if (g_cal_data_t.raw_peak1 < g_cal_data_t.confidence_low) {
                g_cal_data_t.confidence = 0;
            } else {
                g_cal_data_t.confidence =
                    100 * (g_cal_data_t.raw_peak1 - g_cal_data_t.confidence_low) / (g_cal_data_t.confidence_up - g_cal_data_t.confidence_low);
            }

            if (g_cal_data_t.confidence >= spad_compensation_confidence->confidence_threshold && g_cal_data_t.raw_tof > 0) {
#if USING_CSPC_PROTOCOL == 1
                g_ptrRegCentroid.distance_tmp = (uint32_t)(g_cal_data_t.raw_tof * 100);
#else
                g_ptrRegCentroid.distance_tmp = (uint32_t)(g_cal_data_t.raw_tof * 15.55f);
#endif


                if (g_ptrSysParam.isDebugHistogram == true) {
                    g_cal_data_t.raw_peak1 = g_cal_data_t.raw_peak2;
                }

            } else {
                g_ptrRegCentroid.distance_tmp = 0;
                g_cal_data_t.raw_peak1        = 0;
            }
        }


#endif

        // laser detect
        if (g_cal_data_t.raw_peak1 > DDS_LASER_PEAK_THRESHOLD) {
            g_ptrDDSInfoCount.laserCnt = getAbsolutionTime();
        }

// g_ptrRegCentroid.peak_last = g_ptrSysParam.currentTemperature;
// g_ptrRegCentroid.peak_last = g_sys_time_t.time_diff;
// g_ptrRegCentroid.peak_last = g_param_combine.motor_mode.mode;//g_pid_speed.speed_feedback*100;//g_ptrRegCentroid.confidence_last ;
#if USING_CSPC_PROTOCOL == 1
        g_ptrRegCentroid.distance_si = ((g_ptrRegCentroid.distance_last << 16) & 0xffff0000) | (g_ptrRegCentroid.peak_last & 0x0000ffff);
#else
        // g_ptrRegCentroid.distance_si = ((uint8_t)g_ptrRegCentroid.distance_last)| ((((uint8_t)(g_ptrRegCentroid.distance_last>>8)&0x3F)| (0)<<6)<<8) |
        // (g_ptrRegCentroid.peak_last<<16);
        if (g_cal_data_t.is_start_pack) {
            g_regEncoder.startPack          = 0;
            g_ptrRegCentroid.hr_denote_last = 1;
        } else {
            g_ptrRegCentroid.hr_denote_last = 0;
        }
        g_ptrRegCentroid.distance_si =
            (0x3FFF & g_ptrRegCentroid.distance_last) | (((0x01 & g_ptrRegCentroid.hr_denote_last) << 6) << 8) | (g_ptrRegCentroid.peak_last << 16);
#endif


// SpiRecInterruptCallback(g_cal_data_t.angle_time,g_cal_data_t.encoder_cnt,g_cal_data_t.delta_angle);
/***********************/
/******************/
#if (defined A0_D6_XX) || (defined A2_D6_XX) || (defined A0_D6A_UMOUSE) || (defined A2_D6A_UMOUSE)
#if USING_COIN_WHOLE_ENCODER == 1
        if (g_cal_data_t.encoder_cnt == 13) {
            angle_interval = 22.5;
            angle_base     = (g_cal_data_t.encoder_cnt - 1) * 24 + 22;
        } else if (g_cal_data_t.encoder_cnt == 14) {
            angle_interval = 26.5;
            angle_base     = g_cal_data_t.encoder_cnt * 24;
        } else {
            angle_interval = 24;
            angle_base     = g_cal_data_t.encoder_cnt * 24;
        }

        tn_angle   = (((float)g_cal_data_t.angle_time) / (g_cal_data_t.delta_angle == 0 ? 1 : g_cal_data_t.delta_angle)) * angle_interval;
        angleValue = (angle_base + tn_angle) + g_ptrSysParam.zeroAngle;
#else
        angle_interval = 24;
        angle_base     = g_cal_data_t.encoder_cnt * 24;
        tn_angle       = (((float)g_cal_data_t.angle_time) / (g_cal_data_t.delta_angle == 0 ? 1 : g_cal_data_t.delta_angle)) * angle_interval;
        angleValue     = (angle_base + (tn_angle > angle_interval ? angle_interval : tn_angle)) + g_ptrSysParam.zeroAngle;
#endif
#else

        angle_interval = 24;
        angle_base     = g_cal_data_t.encoder_cnt * 24;
        tn_angle       = (((float)g_cal_data_t.angle_time) / (g_cal_data_t.delta_angle == 0 ? 1 : g_cal_data_t.delta_angle)) * angle_interval;
        angleValue     = (angle_base + (tn_angle > angle_interval ? angle_interval : tn_angle)) + g_ptrSysParam.zeroAngle;

#endif

        if (angleValue > 360.0f) {
            angleValue -= 360.0f;
        } else if (angleValue < 0.0f) {
            angleValue += 360.0f;
        }

#if USING_CSPC_PROTOCOL == 1
        angle_encode = (((uint16_t)(angleValue * 64)) << 1) | 0x01;
        if (g_cal_data_t.is_start_pack) {
            g_regEncoder.startPack = 0;
            if (g_cal_data_t.point_num != 0) {
                g_regLidarDataPtr->BufferLen    = g_regLidarDataPtr->LSN * 4 + 10;
                g_regLidarDataPtr->Pack_Send_En = 1;
                g_cal_data_t.point_num          = 0;

                if (g_regLidarDataPtr == &g_regLidarData0) {
                    g_regLidarDataPtr = &g_regLidarData1;
                } else if (g_regLidarDataPtr == &g_regLidarData1) {
                    g_regLidarDataPtr = &g_regLidarData2;
                } else if (g_regLidarDataPtr == &g_regLidarData2) {
                    g_regLidarDataPtr = &g_regLidarData0;
                }
            }

            g_regLidarDataX.F_And_C = (((uint16_t)g_regSpeed.currentSpeed) << 1) | 0x01;

            g_regLidarDataX.LSN          = 0x01;
            g_regLidarDataX.BufferLen    = 14;
            g_regLidarDataX.FSA          = angle_encode;
            g_regLidarDataX.LSA          = angle_encode;
            g_regLidarDataX.SI[0]        = g_ptrRegCentroid.distance_si;
            g_regLidarDataX.Pack_Send_En = 1;


        } else {

            g_cal_data_t.point_num++;
            g_regLidarDataPtr->LSN = g_cal_data_t.point_num;
            if (g_cal_data_t.point_num == 1) {
                g_regLidarDataPtr->FSA = angle_encode;
            }
            g_regLidarDataPtr->LSA                            = angle_encode;
            g_regLidarDataPtr->SI[g_cal_data_t.point_num - 1] = g_ptrRegCentroid.distance_si;
            g_regLidarDataPtr->F_And_C                        = 0x00;


            if (g_cal_data_t.point_num == LIDAR_DATA_NUM) {

                g_cal_data_t.point_num          = 0;
                g_regLidarDataPtr->BufferLen    = g_regLidarDataPtr->LSN * 4 + 10;
                g_regLidarDataPtr->Pack_Send_En = 1;

                if (g_regLidarDataPtr == &g_regLidarData0) {
                    g_regLidarDataPtr = &g_regLidarData1;
                } else if (g_regLidarDataPtr == &g_regLidarData1) {
                    g_regLidarDataPtr = &g_regLidarData2;
                } else if (g_regLidarDataPtr == &g_regLidarData2) {
                    g_regLidarDataPtr = &g_regLidarData0;
                }
            }
        }
#else
        angle_encode   = ((uint16_t)(angleValue * 64) + 0xA000);

        if (g_cal_data_t.point_num < (LIDAR_DATA_NUM - 1)) {
            if (g_cal_data_t.point_num == 0) {
                g_regLidarDataPtr->first_angle = angle_encode;
            }
            g_regLidarDataPtr->data[g_cal_data_t.point_num] = g_ptrRegCentroid.distance_si;
            g_cal_data_t.point_num++;
        } else {
            g_regLidarDataPtr->last_angle                   = angle_encode;
            g_regLidarDataPtr->data[g_cal_data_t.point_num] = g_ptrRegCentroid.distance_si;
            g_regLidarDataPtr->speed                        = (uint16_t)(g_regSpeed.speed_f * 60 * 64);
            g_regLidarDataPtr->send_end                     = 1;
            g_cal_data_t.point_num                          = 0;

            if (g_regLidarDataPtr == &g_regLidarData0) {
                g_regLidarDataPtr = &g_regLidarData1;
            } else if (g_regLidarDataPtr == &g_regLidarData1) {
                g_regLidarDataPtr = &g_regLidarData2;
            } else if (g_regLidarDataPtr == &g_regLidarData2) {
                g_regLidarDataPtr = &g_regLidarData0;
            }
        }


#endif


        /***********************/

        // cal tof flow
        g_ptrRegCentroid.distance_pri  = g_ptrRegCentroid.distance_last;
        g_ptrRegCentroid.distance_last = g_ptrRegCentroid.distance_tmp;

        // cal peak flow
        g_ptrRegCentroid.peak_pri  = g_ptrRegCentroid.peak_last;
        g_ptrRegCentroid.peak_last = g_cal_data_t.raw_peak1;


        // cal confidence flow
        g_ptrRegCentroid.confidence_pri  = g_ptrRegCentroid.confidence_last;
        g_ptrRegCentroid.confidence_last = g_cal_data_t.confidence;


        g_ptrSysFlag.isGpioTrig0 = false;
    }
}


void StandbyPolling(void) {
    uint8_t rVall[2] = {0};
    delay_1ms(100);
    g_ptrSysFlag.isGpioTrig0 = false;
    for (;;) {
        g_ptrTimeTask.TaskPoll();
        ParseAck();

        if (g_ptrSysFlag.isGpioTrig0 == true) {
            if (g_regVi4302.isWriteReg == true) {
                g_regVi4302.isWriteReg = false;
                g_ptrSpi.wRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, g_regVi4302.buff[0], NULL);
            }

            if (g_regVi4302.isReadReg == true) {
                g_regVi4302.isReadReg = false;
                g_ptrSpi.rRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, &rVall[0], NULL);
                FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&rVall, REGISTER_BYTE_CNT, kResponse);
            }

            delay_1ms(10);
            g_ptrSysFlag.isGpioTrig0 = false;
        }
    }
}
